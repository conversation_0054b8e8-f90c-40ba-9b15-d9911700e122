package corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.SaleGuiDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SaleGuiService {

    private static final String ROUTE_SUBSCRIBE = "saleGui.subscribe";
    private static final String ROUTE_CREATE_SEARCH_PRODUCT = "saleGui.createSearchProduct";
    private static final String ROUTE_DELETE_SEARCH_PRODUCT = "saleGui.deleteSearchProduct";
    private static final String ROUTE_DELETE_ALL_SEARCH_PRODUCTS = "saleGui.deleteAllSearchProducts";

    private final ConnectionService connectionService;

    /**
     * Se suscribe a los cambios de un SaleGui.
     * @param saleGuiId El id del SaleGui (UUID)
     * @return Un Flux<SaleGuiDto> con las actualizaciones en tiempo real.
     */
    public Flux<SaleGuiDto> subscribeToChanges(UUID saleGuiId) {
        log.debug("Subscribing to SaleGui changes for id: {}", saleGuiId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, saleGuiId, SaleGuiDto.class);
    }

    /**
     * Solicita la creación de un nuevo SearchProductGui para el SaleGui.
     * @param saleGuiId El id del SaleGui.
     * @return Mono<Void> indicando la finalización.
     */
    public Mono<Void> createSearchProduct(UUID saleGuiId) {
        log.debug("Creating new SearchProduct for SaleGui with id: {}", saleGuiId);
        return connectionService.authenticatedRequest(ROUTE_CREATE_SEARCH_PRODUCT, saleGuiId, Void.class);
    }

    /**
     * Solicita eliminar un SearchProductGui a partir de su id.
     * @param saleGuiId El id del SaleGui.
     * @param searchProductId El id del SearchProductGui (UUID).
     * @return Mono<Void> indicando la finalización.
     */
    public Mono<Void> deleteSearchProduct(UUID saleGuiId, UUID searchProductId) {
        log.debug("Deleting SearchProduct with id: {} from SaleGui with id: {}", searchProductId, saleGuiId);
        var request = new DeleteSearchProductRequest(saleGuiId, searchProductId);
        return connectionService.authenticatedRequest(ROUTE_DELETE_SEARCH_PRODUCT, request, Void.class);
    }

    /**
     * Solicita eliminar todos los SearchProductGui del SaleGui.
     * @param saleGuiId El id del SaleGui.
     * @return Mono<Void> indicando la finalización.
     */
    public Mono<Void> deleteAllSearchProducts(UUID saleGuiId) {
        log.debug("Deleting all SearchProducts from SaleGui with id: {}", saleGuiId);
        return connectionService.authenticatedRequest(ROUTE_DELETE_ALL_SEARCH_PRODUCTS, saleGuiId, Void.class);
    }

    public record DeleteSearchProductRequest(UUID saleGuiId, UUID searchProductId) {}
}
