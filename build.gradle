plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.0'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.openjfx.javafxplugin' version '0.1.0'
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

ext {
    javafxVersion = "21.0.4"
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-rsocket'
    implementation 'io.projectreactor:reactor-core'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    testRuntimeOnly 'net.bytebuddy:byte-buddy-agent:1.15.10'

    implementation "org.controlsfx:controlsfx:11.2.1"
    implementation "org.kordamp.ikonli:ikonli-javafx:12.3.1"
    implementation "org.kordamp.ikonli:ikonli-fontawesome5-pack:12.3.1"
}

javafx {
    version = javafxVersion
    modules = ['javafx.controls', 'javafx.fxml', 'javafx.graphics', 'javafx.base']
}

bootJar {
    manifest {
        attributes 'Start-Class': 'corp.jamaro.jamaroescritoriofx.JamaroEscritorioFxApplication'
    }
}

tasks.named('test') {
    useJUnitPlatform()
    doFirst {
        def byteBuddyAgent = configurations.testRuntimeClasspath.find { it.name.contains('byte-buddy-agent') }
        if (byteBuddyAgent) {
            jvmArgs "-javaagent:${byteBuddyAgent}"
        }
    }
    jvmArgs '-XX:+EnableDynamicAgentLoading', 
            '-Djdk.instrument.traceUsage=false',
            '-XX:-PrintWarnings',
            '--add-opens=java.base/java.lang=ALL-UNNAMED',
            '--add-opens=java.base/java.util=ALL-UNNAMED'
}

tasks.withType(JavaCompile) {
    options.compilerArgs += ['-Xlint:deprecation', '-Xlint:unchecked']
}
