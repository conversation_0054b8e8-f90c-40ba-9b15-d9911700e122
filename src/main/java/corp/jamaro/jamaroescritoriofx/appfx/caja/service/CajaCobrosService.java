package corp.jamaro.jamaroescritoriofx.appfx.caja.service;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Servicio cliente para gestionar las operaciones relacionadas con cobros de caja.
 * Se comunica con el servidor a través de RSocket para procesar cobros y obtener
 * información sobre cobros pendientes.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CajaCobrosService {

}
