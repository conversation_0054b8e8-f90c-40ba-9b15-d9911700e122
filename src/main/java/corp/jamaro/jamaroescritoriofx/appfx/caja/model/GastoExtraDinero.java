package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroEntrada;
import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroSalida;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class GastoExtraDinero {
    private UUID id;

    private String motivo;
    private Double monto;

    private User programadoPor;

    private TipoDeGasto tipoDeGasto;
    private String detalles;

    private Instant realizadoEl;

    private Set<DineroSalida> dineroGastado;//las salidas de caja cuando se gasta dinero

    //con dinero devuelto a caja
    private Set<DineroEntrada> dineroDevuelto;// A veces sobra dinero del gasto y vuelve a caja

    private Boolean salioDeCaja;//para saber si ya fue entregado el dinero para el gasto.

    public enum TipoDeGasto {
        PROVEEDOR,
        FLETE,
        TIENDA,
        TRAMITES,
        MERCADO,
        PAPA,
        MAMA,
        OTROS
    }

}
