package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaSaleDetailsController;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.util.CajaDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;

import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Controlador principal para la gestión de caja.
 * Maneja la selección de CajaGui y la visualización de cobros pendientes.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class CajaController extends BaseController {

    private final CajaGuiService cajaGuiService;
    private final CajaDialogUtil cajaDialogUtil;
    private final SpringFXMLLoader springFXMLLoader;

    @FXML
    private StackPane mainStackPane;

    @FXML
    private ProgressIndicator loadingIndicator;

    @FXML
    private MenuBar menuBar;

    @FXML
    private MenuItem menuInicializarEfectivo;

    @FXML
    private MenuItem menuInicializarDigital;

    @FXML
    private MenuItem menuCerrarEfectivo;

    @FXML
    private MenuItem menuCerrarDigital;

    @FXML
    private VBox contentContainer;

    @FXML
    private Label lblNombreCaja;

    @FXML
    private Label lblEstadoEfectivo;

    @FXML
    private Label lblEstadoDigital;

    @FXML
    private TabPane salesTabPane;

    @FXML
    private TableView<SaleDisplayData> contadoTable;

    @FXML
    private TableView<SaleDisplayData> creditoTable;

    @FXML
    private TableView<SaleDisplayData> pedidoTable;

    @FXML
    private TableColumn<SaleDisplayData, String> contadoFechaCol;

    @FXML
    private TableColumn<SaleDisplayData, String> contadoUserCol;

    @FXML
    private TableColumn<SaleDisplayData, String> contadoClienteCol;

    @FXML
    private TableColumn<SaleDisplayData, String> contadoMontoCol;

    @FXML
    private TableColumn<SaleDisplayData, String> creditoFechaCol;

    @FXML
    private TableColumn<SaleDisplayData, String> creditoUserCol;

    @FXML
    private TableColumn<SaleDisplayData, String> creditoClienteCol;

    @FXML
    private TableColumn<SaleDisplayData, String> creditoMontoCol;

    @FXML
    private TableColumn<SaleDisplayData, String> pedidoFechaCol;

    @FXML
    private TableColumn<SaleDisplayData, String> pedidoUserCol;

    @FXML
    private TableColumn<SaleDisplayData, String> pedidoClienteCol;

    @FXML
    private TableColumn<SaleDisplayData, String> pedidoMontoCol;

    @FXML
    private SplitPane spSalesPorCobrar;

    @FXML
    private AnchorPane anchorSaleDetails;

    // Estado del controlador
    private CajaGui currentCajaGui;
    private final ObservableList<Sale> salesPorCobrar = FXCollections.observableArrayList();
    private CajaSaleDetailsController cajaSaleDetailsController;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        log.info("Inicializando CajaController");

        // Configurar estado inicial de los menús
        updateMenuState();

        // Configurar eventos del menú
        setupMenuEvents();

        // Configurar las tablas
        setupTables();

        // Cargar componente SaleDetails
        loadSaleDetailsComponent();

        // Inicializar la selección de CajaGui
        ensureCajaGuiInitialized();
    }

    /**
     * Configura los eventos del menú bar
     */
    private void setupMenuEvents() {
        menuInicializarEfectivo.setOnAction(e -> handleInicializarEfectivo());
        menuInicializarDigital.setOnAction(e -> handleInicializarDigital());
        menuCerrarEfectivo.setOnAction(e -> handleCerrarEfectivo());
        menuCerrarDigital.setOnAction(e -> handleCerrarDigital());
    }

    /**
     * Configura las tablas de ventas
     */
    private void setupTables() {
        // Configurar tabla CONTADO
        setupTableColumns(contadoFechaCol, contadoUserCol, contadoClienteCol, contadoMontoCol);
        setupTableSelectionEvents(contadoTable);

        // Configurar tabla CREDITO
        setupTableColumns(creditoFechaCol, creditoUserCol, creditoClienteCol, creditoMontoCol);
        setupTableSelectionEvents(creditoTable);

        // Configurar tabla PEDIDO
        setupTableColumns(pedidoFechaCol, pedidoUserCol, pedidoClienteCol, pedidoMontoCol);
        setupTableSelectionEvents(pedidoTable);
    }

    /**
     * Configura las columnas de una tabla
     */
    private void setupTableColumns(TableColumn<SaleDisplayData, String> fechaCol,
                                   TableColumn<SaleDisplayData, String> userCol,
                                   TableColumn<SaleDisplayData, String> clienteCol,
                                   TableColumn<SaleDisplayData, String> montoCol) {
        fechaCol.setCellValueFactory(new PropertyValueFactory<>("fecha"));
        userCol.setCellValueFactory(new PropertyValueFactory<>("usuario"));
        clienteCol.setCellValueFactory(new PropertyValueFactory<>("cliente"));
        montoCol.setCellValueFactory(new PropertyValueFactory<>("monto"));

        // Configurar estilos de las columnas
        fechaCol.getStyleClass().add("caja-table-column");
        userCol.getStyleClass().add("caja-table-column");
        clienteCol.getStyleClass().add("caja-table-column");
        montoCol.getStyleClass().add("caja-table-column");

        // Hacer que las columnas se redimensionen automáticamente
        fechaCol.setResizable(true);
        userCol.setResizable(true);
        clienteCol.setResizable(true);
        montoCol.setResizable(true);

        // Deshabilitar reordenamiento y ordenamiento
        fechaCol.setReorderable(false);
        fechaCol.setSortable(false);
        userCol.setReorderable(false);
        userCol.setSortable(false);
        clienteCol.setReorderable(false);
        clienteCol.setSortable(false);
        montoCol.setReorderable(false);
        montoCol.setSortable(false);
    }

    /**
     * Configura los eventos de selección para una tabla
     */
    private void setupTableSelectionEvents(TableView<SaleDisplayData> table) {
        table.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                Sale selectedSale = newSelection.getSale();
                log.info("Seleccionada fila en tabla: {} - Sale ID: {}",
                    newSelection.getFecha(), selectedSale.getId());

                // Limpiar selección en otras tablas
                clearOtherTableSelections(table);

                // Mostrar detalles del Sale seleccionado
                showSaleDetails(selectedSale);
            } else {
                // Si no hay selección, limpiar los detalles
                showSaleDetails(null);
            }
        });
    }

    /**
     * Limpia la selección en las otras tablas
     */
    private void clearOtherTableSelections(TableView<SaleDisplayData> currentTable) {
        if (currentTable != contadoTable) {
            contadoTable.getSelectionModel().clearSelection();
        }
        if (currentTable != creditoTable) {
            creditoTable.getSelectionModel().clearSelection();
        }
        if (currentTable != pedidoTable) {
            pedidoTable.getSelectionModel().clearSelection();
        }
    }

    /**
     * Carga el componente CajaSaleDetails en el anchorSaleDetails
     */
    private void loadSaleDetailsComponent() {
        try {
            log.info("Cargando componente CajaSaleDetails");

            VBox saleDetailsRoot = (VBox) springFXMLLoader.load("fxml/caja/cajaSaleDetails.fxml");
            cajaSaleDetailsController = springFXMLLoader.getController(saleDetailsRoot);

            // Configurar acciones de los botones
            cajaSaleDetailsController.setCobrarAction(this::handleCobrarSale);
            cajaSaleDetailsController.setImprimirAction(this::handleImprimirSale);

            // Agregar el componente al AnchorPane
            anchorSaleDetails.getChildren().clear();
            anchorSaleDetails.getChildren().add(saleDetailsRoot);

            // Hacer que el componente ocupe todo el espacio disponible
            AnchorPane.setTopAnchor(saleDetailsRoot, 0.0);
            AnchorPane.setBottomAnchor(saleDetailsRoot, 0.0);
            AnchorPane.setLeftAnchor(saleDetailsRoot, 0.0);
            AnchorPane.setRightAnchor(saleDetailsRoot, 0.0);

            log.info("Componente CajaSaleDetails cargado correctamente");

        } catch (Exception e) {
            log.error("Error al cargar componente CajaSaleDetails", e);
        }
    }

    /**
     * Actualiza el estado de los elementos del menú según la CajaGui actual
     */
    private void updateMenuState() {
        boolean hasCajaGui = currentCajaGui != null;
        boolean hasEfectivo = hasCajaGui && currentCajaGui.getCajaDineroEfectivo() != null;
        boolean hasDigital = hasCajaGui && currentCajaGui.getCajaDineroDigital() != null;

        menuInicializarEfectivo.setDisable(!hasCajaGui || hasEfectivo);
        menuInicializarDigital.setDisable(!hasCajaGui || hasDigital);
        menuCerrarEfectivo.setDisable(!hasEfectivo);
        menuCerrarDigital.setDisable(!hasDigital);
    }

    /**
     * Se asegura de que haya una CajaGui inicializada, mostrando diálogos hasta que se seleccione una
     */
    private void ensureCajaGuiInitialized() {
        if (currentCajaGui != null) {
            log.debug("CajaGui ya inicializada: {}", currentCajaGui.getNombreCaja());
            return;
        }

        log.info("Iniciando selección de CajaGui");
        initializeCajaGuiSelection();
    }

    /**
     * Inicializa la selección de CajaGui mostrando el diálogo correspondiente
     */
    private void initializeCajaGuiSelection() {
        subscribeMonoWithUiUpdate(
            cajaGuiService.getAllCajaGuiOrderedByCreatedAt().collectList(),
            this::handleCajaGuiListReceived,
            error -> {
                log.error("Error al obtener lista de CajaGui", error);
                // Mostrar error y permitir crear nueva caja
                showCreateCajaDialog();
            }
        );
    }

    /**
     * Maneja la lista de CajaGui recibida del servidor
     */
    private void handleCajaGuiListReceived(List<CajaGui> cajaGuiList) {
        if (cajaGuiList.isEmpty()) {
            log.info("No hay CajaGui disponibles, mostrando diálogo de creación");
            showCreateCajaDialog();
        } else {
            log.info("Mostrando diálogo de selección con {} CajaGui disponibles", cajaGuiList.size());
            showCajaSelectionDialog(cajaGuiList);
        }
    }

    /**
     * Muestra el diálogo de selección de CajaGui
     */
    private void showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        Optional<CajaDialogUtil.CajaSelectionResult> result = cajaDialogUtil.showCajaSelectionDialog(cajaGuiList);

        result.ifPresentOrElse(
            selectionResult -> {
                if (selectionResult.isCreateNew()) {
                    showCreateCajaDialog();
                } else {
                    CajaGui selectedCaja = selectionResult.selectedCajaGui();
                    log.info("CajaGui seleccionada: {} - {}", selectedCaja.getId(), selectedCaja.getNombreCaja());
                    initializeCajaGui(selectedCaja);
                }
            },
            () -> {
                log.info("Selección de CajaGui cancelada, reintentando...");
                // Volver a mostrar el diálogo después de un breve delay
                runOnUiThread(() -> {
                    try {
                        Thread.sleep(500); // Breve pausa para evitar spam de diálogos
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    ensureCajaGuiInitialized();
                });
            }
        );
    }

    /**
     * Muestra el diálogo de creación de nueva CajaGui
     */
    private void showCreateCajaDialog() {
        Optional<CajaDialogUtil.CreateCajaData> result = cajaDialogUtil.showCreateCajaDialog();

        result.ifPresentOrElse(
            createData -> {
                log.info("Creando nueva CajaGui: {}", createData.nombreCaja());
                createNewCajaGui(createData.nombreCaja(), createData.guiConfig());
            },
            () -> {
                log.info("Creación de CajaGui cancelada, reintentando...");
                // Volver a intentar la inicialización
                runOnUiThread(() -> {
                    try {
                        Thread.sleep(500); // Breve pausa
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    ensureCajaGuiInitialized();
                });
            }
        );
    }

    /**
     * Crea una nueva CajaGui
     */
    private void createNewCajaGui(String nombreCaja, String guiConfig) {
        subscribeMonoWithUiUpdate(
            cajaGuiService.createCajaGui(nombreCaja, guiConfig),
            this::initializeCajaGui,
            error -> {
                log.error("Error al crear CajaGui", error);
                // Mostrar error y volver a intentar la inicialización
                runOnUiThread(() -> {
                    try {
                        Thread.sleep(1000); // Pausa más larga después de error
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    ensureCajaGuiInitialized();
                });
            }
        );
    }

    /**
     * Inicializa la CajaGui seleccionada y se suscribe a sus cambios
     */
    private void initializeCajaGui(CajaGui cajaGui) {
        log.info("Inicializando CajaGui: {} - {}", cajaGui.getId(), cajaGui.getNombreCaja());

        this.currentCajaGui = cajaGui;

        updateMenuState();
        updateCajaInfoUI();

        // Suscribirse a cambios de la CajaGui
        subscribeToCurrentCajaGui();

        // Suscribirse a ventas por cobrar
        subscribeToSalesPorCobrar();
    }

    /**
     * Se suscribe a los cambios de la CajaGui actual
     */
    private void subscribeToCurrentCajaGui() {
        if (currentCajaGui == null) {
            log.warn("No se puede suscribir a CajaGui: currentCajaGui es null");
            return;
        }

        // Usar subscripción sin timeout para CajaGui ya que puede no emitir actualizaciones frecuentes
        subscribeFluxWithUiUpdateNoTimeout(
            cajaGuiService.subscribeToCurrentCajaGui(currentCajaGui.getId()),
            updatedCajaGui -> {
                log.debug("Recibida actualización de CajaGui: {}", updatedCajaGui.getNombreCaja());
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error en suscripción a CajaGui", error)
        );
    }

    /**
     * Se suscribe a las ventas por cobrar
     */
    private void subscribeToSalesPorCobrar() {
        log.info("[DEBUG_LOG] Iniciando suscripción a ventas por cobrar");
        // Usar subscripción sin timeout para Sales por cobrar ya que puede no emitir actualizaciones frecuentes
        subscribeFluxWithUiUpdateNoTimeout(
            cajaGuiService.subscribeToSalesPorCobrar(),
            salesList -> {
                log.info("[DEBUG_LOG] CajaController recibió {} ventas por cobrar", salesList.size());
                if (salesList != null && !salesList.isEmpty()) {
                    for (int i = 0; i < salesList.size(); i++) {
                        Sale sale = salesList.get(i);
                        log.debug("[DEBUG_LOG] Sale {}: ID={}, totalRestante={}, tipoVenta={}, cliente={}", 
                            i + 1, 
                            sale.getId(), 
                            sale.getTotalRestante(), 
                            sale.getTipoVenta(),
                            sale.getCliente() != null ? sale.getCliente().getId() : "null");
                    }
                }
                
                log.debug("[DEBUG_LOG] Actualizando ObservableList con {} elementos", salesList.size());
                salesPorCobrar.setAll(salesList);
                log.debug("[DEBUG_LOG] ObservableList actualizada, tamaño actual: {}", salesPorCobrar.size());
                
                log.debug("[DEBUG_LOG] Llamando updateSalesUI()");
                updateSalesUI();
                log.debug("[DEBUG_LOG] updateSalesUI() completado");
            },
            error -> {
                log.error("[DEBUG_LOG] Error en suscripción a ventas por cobrar: {}", error.getMessage(), error);
                log.error("Error en suscripción a ventas por cobrar", error);
            }
        );
    }

    // Métodos para manejar eventos del menú

    /**
     * Maneja la inicialización de CajaDineroEfectivo
     */
    private void handleInicializarEfectivo() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar efectivo: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroEfectivo");
        cajaDialogUtil.showInitializeCajaDineroEfectivoDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionEfectivo);
    }

    /**
     * Maneja la inicialización de CajaDineroDigital
     */
    private void handleInicializarDigital() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar digital: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroDigital");
        cajaDialogUtil.showInitializeCajaDineroDigitalDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionDigital);
    }

    /**
     * Maneja el cierre de CajaDineroEfectivo
     */
    private void handleCerrarEfectivo() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroEfectivo() == null) {
            log.warn("No se puede cerrar efectivo: no hay CajaDineroEfectivo disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroEfectivo");
        cajaDialogUtil.showCloseCajaDineroEfectivoDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroEfectivo().getId()
        ).ifPresent(this::procesarCierreEfectivo);
    }

    /**
     * Maneja el cierre de CajaDineroDigital
     */
    private void handleCerrarDigital() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroDigital() == null) {
            log.warn("No se puede cerrar digital: no hay CajaDineroDigital disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroDigital");
        cajaDialogUtil.showCloseCajaDineroDigitalDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroDigital().getId()
        ).ifPresent(this::procesarCierreDigital);
    }

    // Métodos para procesar las operaciones con el servidor

    /**
     * Procesa la inicialización de CajaDineroEfectivo
     */
    private void procesarInicializacionEfectivo(CajaDialogUtil.InitializeCajaDineroEfectivoData data) {
        log.info("Procesando inicialización de CajaDineroEfectivo");

        subscribeMonoWithUiUpdate(
            cajaGuiService.initializeCajaDineroEfectivo(
                data.cajaGuiId(), data.nombre(), data.montoInicialEfectivo(),
                data.diezCentimos(), data.veinteCentimos(), data.cincuentaCentimos(),
                data.unSol(), data.dosSoles(), data.cincoSoles(),
                data.diezSoles(), data.veinteSoles(), data.cincuentaSoles(),
                data.cienSoles(), data.doscientosSoles()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo inicializada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al inicializar CajaDineroEfectivo", error)
        );
    }

    /**
     * Procesa la inicialización de CajaDineroDigital
     */
    private void procesarInicializacionDigital(CajaDialogUtil.InitializeCajaDineroDigitalData data) {
        log.info("Procesando inicialización de CajaDineroDigital");

        subscribeMonoWithUiUpdate(
            cajaGuiService.initializeCajaDineroDigital(
                data.cajaGuiId(), data.cuentaDigitalAsignada(), data.montoInicialDigital()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroDigital inicializada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al inicializar CajaDineroDigital", error)
        );
    }

    /**
     * Procesa el cierre de CajaDineroEfectivo
     */
    private void procesarCierreEfectivo(CajaDialogUtil.CloseCajaDineroEfectivoData data) {
        log.info("Procesando cierre de CajaDineroEfectivo");

        subscribeMonoWithUiUpdate(
            cajaGuiService.closeCajaDineroEfectivo(
                data.cajaGuiId(), data.cajaDineroEfectivoId(),
                data.cierreDiezCentimos(), data.cierreVeinteCentimos(), data.cierreCincuentaCentimos(),
                data.cierreUnSol(), data.cierreDosSoles(), data.cierreCincoSoles(),
                data.cierreDiezSoles(), data.cierreVeinteSoles(), data.cierreCincuentaSoles(),
                data.cierreCienSoles(), data.cierreDoscientosSoles()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo cerrada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al cerrar CajaDineroEfectivo", error)
        );
    }

    /**
     * Procesa el cierre de CajaDineroDigital
     */
    private void procesarCierreDigital(CajaDialogUtil.CloseCajaDineroDigitalData data) {
        log.info("Procesando cierre de CajaDineroDigital");

        subscribeMonoWithUiUpdate(
            cajaGuiService.closeCajaDineroDigital(
                data.cajaGuiId(), data.cajaDineroDigitalId(), data.cierreDigitalDeclarado()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroDigital cerrada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al cerrar CajaDineroDigital", error)
        );
    }

    // Métodos para actualizar la UI

    /**
     * Actualiza la información de la caja en la UI
     */
    private void updateCajaInfoUI() {
        if (currentCajaGui == null) {
            lblNombreCaja.setText("No seleccionada");
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoDigital.setText("No inicializada");
            return;
        }

        lblNombreCaja.setText(currentCajaGui.getNombreCaja());

        // Estado de caja efectivo
        if (currentCajaGui.getCajaDineroEfectivo() == null) {
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroEfectivo().getCerradaEl() == null) {
            lblEstadoEfectivo.setText("Abierta");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoEfectivo.setText("Cerrada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-remaining-label");
        }

        // Estado de caja digital
        if (currentCajaGui.getCajaDineroDigital() == null) {
            lblEstadoDigital.setText("No inicializada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroDigital().getCerradaEl() == null) {
            lblEstadoDigital.setText("Abierta");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoDigital.setText("Cerrada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoDigital.getStyleClass().add("cobro-remaining-label");
        }
    }

    /**
     * Actualiza la lista de ventas por cobrar en la UI
     */
    private void updateSalesUI() {
        log.debug("Iniciando updateSalesUI(), salesPorCobrar.size()={}", salesPorCobrar.size());

        // Limpiar todas las tablas
        contadoTable.getItems().clear();
        creditoTable.getItems().clear();
        pedidoTable.getItems().clear();

        if (salesPorCobrar.isEmpty()) {
            // Actualizar badges con 0
            updateTabBadges(0, 0, 0);
            return;
        }

        // Separar ventas por tipo y convertir a SaleDisplayData
        java.util.List<SaleDisplayData> contadoSales = new java.util.ArrayList<>();
        java.util.List<SaleDisplayData> creditoSales = new java.util.ArrayList<>();
        java.util.List<SaleDisplayData> pedidoSales = new java.util.ArrayList<>();

        for (Sale sale : salesPorCobrar) {
            SaleDisplayData displayData = createSaleDisplayData(sale);

            switch (sale.getTipoVenta()) {
                case CONTADO -> contadoSales.add(displayData);
                case CREDITO -> creditoSales.add(displayData);
                case PEDIDO -> pedidoSales.add(displayData);
                default -> {
                    // PROFORMA u otros tipos van a CONTADO por defecto
                    contadoSales.add(displayData);
                }
            }
        }

        // Poblar las tablas
        contadoTable.getItems().addAll(contadoSales);
        creditoTable.getItems().addAll(creditoSales);
        pedidoTable.getItems().addAll(pedidoSales);

        // Actualizar badges en los tabs
        updateTabBadges(contadoSales.size(), creditoSales.size(), pedidoSales.size());

        log.debug("updateSalesUI() completado - CONTADO: {}, CREDITO: {}, PEDIDO: {}",
            contadoSales.size(), creditoSales.size(), pedidoSales.size());
    }

    /**
     * Crea un SaleDisplayData a partir de un Sale
     */
    private SaleDisplayData createSaleDisplayData(Sale sale) {
        // Formatear fecha
        String fecha = sale.getCreatedAt() != null ?
            LocalDateTime.ofInstant(sale.getCreatedAt(), ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) :
            "N/A";

        // Obtener usuario
        String usuario = sale.getIniciadaPor() != null ?
            sale.getIniciadaPor().getUsername() :
            "N/A";

        // Formatear cliente
        String cliente = sale.getCliente() != null ?
            formatClienteDisplay(sale.getCliente()) :
            "General";

        // Formatear monto
        String monto = "S/ " + String.format("%.2f", sale.getTotalRestante());

        return new SaleDisplayData(fecha, usuario, cliente, monto, sale);
    }

    /**
     * Actualiza los badges de los tabs con el número de ventas
     */
    private void updateTabBadges(int contadoCount, int creditoCount, int pedidoCount) {
        salesTabPane.getTabs().get(0).setText("CONTADO (" + contadoCount + ")");
        salesTabPane.getTabs().get(1).setText("CREDITO (" + creditoCount + ")");
        salesTabPane.getTabs().get(2).setText("PEDIDO (" + pedidoCount + ")");
    }

    /**
     * Crea un VBox para mostrar información de una venta (método legacy - ya no se usa)
     */
    private VBox createSaleDisplayBox(Sale sale) {
        VBox saleBox = new VBox(5);
        saleBox.getStyleClass().add("cobro-detail-container");

        // Información básica de la venta
        Label saleIdLabel = new Label("Venta ID: " + sale.getId().toString().substring(0, 8) + "...");
        saleIdLabel.getStyleClass().add("cobro-user-label");

        Label clienteLabel = new Label("Cliente: " +
            (sale.getCliente() != null ? formatClienteDisplay(sale.getCliente()) : "Sin cliente"));
        clienteLabel.getStyleClass().add("cobro-info-label");

        Label montoLabel = new Label("Monto Restante: S/ " +
            String.format("%.2f", sale.getTotalRestante()));
        montoLabel.getStyleClass().add("cobro-remaining-label");

        Label tipoLabel = new Label("Tipo: " + sale.getTipoVenta().toString());
        tipoLabel.getStyleClass().add("cobro-date-label");

        saleBox.getChildren().addAll(saleIdLabel, clienteLabel, montoLabel, tipoLabel);

        return saleBox;
    }

    /**
     * Formatea la información del cliente para mostrar
     */
    private String formatClienteDisplay(corp.jamaro.jamaroescritoriofx.appfx.model.Cliente cliente) {
        if (cliente == null) return "Sin cliente";

        StringBuilder display = new StringBuilder();

        // Agregar nombre o razón social
        String nombre = "";
        if (cliente.getRazonSocial() != null && !cliente.getRazonSocial().isEmpty()) {
            nombre = cliente.getRazonSocial();
        } else if (cliente.getNombre() != null && !cliente.getNombre().isEmpty()) {
            nombre = cliente.getNombre();
            if (cliente.getApellido() != null && !cliente.getApellido().isEmpty()) {
                nombre += " " + cliente.getApellido();
            }
        }

        if (!nombre.isEmpty()) {
            display.append(nombre);
        }

        // Agregar documento si hay nombre
        if (!display.isEmpty()) {
            if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
                display.append(" (").append(cliente.getDni()).append(")");
            } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
                display.append(" (").append(cliente.getRuc()).append(")");
            } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
                display.append(" (").append(cliente.getOtroDocumento()).append(")");
            }
        } else {
            // Si no hay nombre, mostrar solo el documento
            if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
                display.append(cliente.getDni());
            } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
                display.append(cliente.getRuc());
            } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
                display.append(cliente.getOtroDocumento());
            } else {
                display.append("Cliente sin datos");
            }
        }

        return display.toString();
    }

    /**
     * Maneja la acción de cobrar un Sale desde el componente SaleDetails
     */
    private void handleCobrarSale(Sale sale) {
        log.info("Iniciando proceso de cobro para Sale: {}", sale.getId());
        // TODO: Implementar lógica de cobro
        // Aquí se puede abrir un diálogo de cobro o navegar a otra vista
        log.warn("Funcionalidad de cobro no implementada aún");
    }

    /**
     * Maneja la acción de imprimir un Sale desde el componente SaleDetails
     */
    private void handleImprimirSale(Sale sale) {
        log.info("Iniciando proceso de impresión para Sale: {}", sale.getId());
        // TODO: Implementar lógica de impresión
        // Aquí se puede generar un PDF o enviar a impresora
        log.warn("Funcionalidad de impresión no implementada aún");
    }

    /**
     * Actualiza el Sale mostrado en el componente SaleDetails
     */
    public void showSaleDetails(Sale sale) {
        if (cajaSaleDetailsController != null) {
            cajaSaleDetailsController.setSale(sale);
            log.info("Mostrando detalles del Sale: {}", sale != null ? sale.getId() : "null");
        } else {
            log.warn("CajaSaleDetailsController no está inicializado");
        }
    }

    /**
     * Limpia las suscripciones al destruir el controlador
     */
    public void cleanup() {
        log.info("Limpiando suscripciones de CajaController");
        onClose(); // Usa el método del BaseController para limpiar suscripciones
    }

    // Getters para acceso desde la UI (si es necesario)

    public CajaGui getCurrentCajaGui() {
        return currentCajaGui;
    }

    public ObservableList<Sale> getSalesPorCobrar() {
        return salesPorCobrar;
    }

    /**
     * Clase para representar los datos de una venta en la tabla
     */
    public static class SaleDisplayData {
        private final String fecha;
        private final String usuario;
        private final String cliente;
        private final String monto;
        private final Sale sale; // Referencia al Sale original

        public SaleDisplayData(String fecha, String usuario, String cliente, String monto, Sale sale) {
            this.fecha = fecha;
            this.usuario = usuario;
            this.cliente = cliente;
            this.monto = monto;
            this.sale = sale;
        }

        public String getFecha() { return fecha; }
        public String getUsuario() { return usuario; }
        public String getCliente() { return cliente; }
        public String getMonto() { return monto; }
        public Sale getSale() { return sale; }
    }
}
