package corp.jamaro.jamaroescritoriofx.appfx.caja.service;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Servicio cliente para gestionar operaciones de CajaGui mediante conexión RSocket al servidor.
 * 
 * Este servicio actúa como intermediario entre la interfaz de usuario y el servidor,
 * proporcionando métodos para:
 * - Gestionar CajaGui (crear, obtener, suscribirse a actualizaciones)
 * - Inicializar y cerrar cajas de dinero (efectivo y digital)
 * - Buscar ventas completadas e incompletas
 * - Suscribirse a actualizaciones de ventas por cobrar
 * 
 * Sigue el patrón establecido por SearchProductGuiService, utilizando ConnectionService
 * para realizar peticiones autenticadas al servidor mediante RSocket.
 * 
 * El servidor implementa un patrón de suscripción donde los clientes reciben:
 * 1. El estado actual como primer elemento
 * 2. Actualizaciones en tiempo real cuando hay cambios
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CajaGuiService {

    private final ConnectionService connectionService;

    // Rutas RSocket que corresponden a los endpoints del servidor CajaGuiController
    private static final String ROUTE_SUBSCRIBE_SALES_POR_COBRAR = "cajaGui.subscribeToSalesPorCobrar";
    private static final String ROUTE_BUSCAR_SALES_COMPLETADAS = "cajaGui.buscarSalesCompletadas";
    private static final String ROUTE_BUSCAR_SALES_INCOMPLETAS = "cajaGui.buscarSalesIncompletas";
    private static final String ROUTE_SUBSCRIBE_CURRENT_CAJA_GUI = "cajaGui.subscribeToCurrentCajaGui";
    private static final String ROUTE_GET_ALL_CAJA_GUI_ORDERED = "cajaGui.getAllCajaGuiOrderedByCreatedAt";
    private static final String ROUTE_CREATE_CAJA_GUI = "cajaGui.createCajaGui";
    private static final String ROUTE_INITIALIZE_CAJA_DINERO_EFECTIVO = "cajaGui.initializeCajaDineroEfectivo";
    private static final String ROUTE_INITIALIZE_CAJA_DINERO_DIGITAL = "cajaGui.initializeCajaDineroDigital";
    private static final String ROUTE_CLOSE_CAJA_DINERO_EFECTIVO = "cajaGui.closeCajaDineroEfectivo";
    private static final String ROUTE_CLOSE_CAJA_DINERO_DIGITAL = "cajaGui.closeCajaDineroDigital";

    /**
     * Se suscribe a actualizaciones de la lista de Sales pendientes de cobro.
     * 
     * El servidor emitirá:
     * 1. La lista actual de Sales con estaPagadoEntregado = false como primer elemento
     * 2. Actualizaciones en tiempo real cuando cambien los Sales pendientes
     * 
     * Los Sales se ordenan por createdAt de más reciente a más antiguo.
     * Esta suscripción es útil para mantener actualizada la interfaz de ventas por cobrar.
     * 
     * @return Flux que emite listas de Sales pendientes de cobro
     */
    @SuppressWarnings("unchecked")
    public Flux<List<Sale>> subscribeToSalesPorCobrar() {
        log.info("Suscribiéndose a actualizaciones de Sales por cobrar");
        
        return connectionService.authenticatedSubscription(
                ROUTE_SUBSCRIBE_SALES_POR_COBRAR,
                null,
                Object.class
        ).map(obj -> {
            log.debug("[DEBUG_LOG] Recibido objeto del servidor: {}", obj != null ? obj.getClass().getSimpleName() : "null");
            
            try {
                if (obj instanceof List<?>) {
                    List<?> rawList = (List<?>) obj;
                    log.debug("[DEBUG_LOG] Lista recibida con {} elementos", rawList.size());
                    
                    List<Sale> saleList = rawList.stream()
                        .map(item -> {
                            log.debug("[DEBUG_LOG] Convirtiendo elemento: {}", item != null ? item.getClass().getSimpleName() : "null");
                            return this.convertToSale(item);
                        })
                        .filter(sale -> sale != null && sale.getId() != null) // Filtrar Sales válidos
                        .collect(java.util.stream.Collectors.toList());
                    
                    log.info("[DEBUG_LOG] Conversión completada: {} Sales válidas convertidas de {} elementos originales", 
                        saleList.size(), rawList.size());
                    return saleList;
                }
                
                log.warn("[DEBUG_LOG] Objeto recibido no es una lista: {}, retornando lista vacía", obj.getClass());
                return java.util.Collections.<Sale>emptyList();
                
            } catch (Exception e) {
                log.error("[DEBUG_LOG] Error procesando datos del servidor: {}", e.getMessage(), e);
                return java.util.Collections.<Sale>emptyList();
            }
        })
        .doOnSubscribe(s -> log.debug("Suscripción a Sales por cobrar iniciada"))
        .doOnCancel(() -> log.debug("Suscripción a Sales por cobrar cancelada"))
        .doOnNext(salesList -> log.info("[DEBUG_LOG] Emitiendo lista de {} Sales por cobrar", salesList.size()))
        .doOnError(error -> log.error("[DEBUG_LOG] Error en suscripción a Sales por cobrar: {}", error.getMessage(), error));
    }

    /**
     * Convierte un objeto (típicamente LinkedHashMap) a un objeto Sale.
     * Este método maneja la conversión de datos deserializados desde JSON.
     */
    private Sale convertToSale(Object obj) {
        log.debug("[DEBUG_LOG] Iniciando conversión de objeto a Sale");
        
        if (obj == null) {
            log.warn("[DEBUG_LOG] Objeto recibido es null, retornando null");
            return null;
        }
        
        if (obj instanceof java.util.Map) {
            java.util.Map<?, ?> map = (java.util.Map<?, ?>) obj;
            log.debug("[DEBUG_LOG] Objeto es un Map con {} claves: {}", map.size(), map.keySet());
            Sale sale = new Sale();
            
            try {
                // ID es obligatorio - si no existe, retornar null
                if (map.get("id") == null) {
                    log.warn("[DEBUG_LOG] Map no contiene campo 'id', retornando null");
                    return null;
                }
                
                String idStr = map.get("id").toString();
                sale.setId(UUID.fromString(idStr));
                log.debug("[DEBUG_LOG] ID convertido: {}", idStr);
                
                // Convertir campos numéricos con valores por defecto
                sale.setTotalRestante(convertToDouble(map.get("totalRestante"), 0.0));
                log.debug("[DEBUG_LOG] totalRestante convertido: {}", sale.getTotalRestante());
                
                sale.setTotalMontoInicial(convertToDouble(map.get("totalMontoInicial"), 0.0));
                log.debug("[DEBUG_LOG] totalMontoInicial convertido: {}", sale.getTotalMontoInicial());
                
                sale.setTotalMontoAcordado(convertToDouble(map.get("totalMontoAcordado"), 0.0));
                log.debug("[DEBUG_LOG] totalMontoAcordado convertido: {}", sale.getTotalMontoAcordado());
                
                // Convertir boolean con valor por defecto
                if (map.get("estaPagadoEntregado") != null) {
                    sale.setEstaPagadoEntregado(Boolean.parseBoolean(map.get("estaPagadoEntregado").toString()));
                } else {
                    sale.setEstaPagadoEntregado(false);
                }
                log.debug("[DEBUG_LOG] estaPagadoEntregado convertido: {}", sale.getEstaPagadoEntregado());
                
                // Convertir enum con valor por defecto
                if (map.get("tipoVenta") != null) {
                    try {
                        String tipoVentaStr = map.get("tipoVenta").toString();
                        sale.setTipoVenta(Sale.TipoVenta.valueOf(tipoVentaStr));
                    } catch (IllegalArgumentException e) {
                        log.warn("[DEBUG_LOG] TipoVenta inválido: {}, usando PROFORMA", map.get("tipoVenta"));
                        sale.setTipoVenta(Sale.TipoVenta.PROFORMA);
                    }
                } else {
                    sale.setTipoVenta(Sale.TipoVenta.PROFORMA);
                }
                log.debug("[DEBUG_LOG] tipoVenta convertido: {}", sale.getTipoVenta());
                
                // Convertir fecha
                if (map.get("createdAt") != null) {
                    try {
                        String createdAtStr = map.get("createdAt").toString();
                        sale.setCreatedAt(java.time.Instant.parse(createdAtStr));
                    } catch (Exception e) {
                        log.warn("[DEBUG_LOG] Error parseando createdAt: {}, usando Instant.now()", map.get("createdAt"));
                        sale.setCreatedAt(java.time.Instant.now());
                    }
                } else {
                    sale.setCreatedAt(java.time.Instant.now());
                }
                log.debug("[DEBUG_LOG] createdAt convertido: {}", sale.getCreatedAt());
                
                // Convertir objetos complejos (Cliente, User, etc.) si están presentes
                if (map.get("cliente") != null && map.get("cliente") instanceof java.util.Map) {
                    try {
                        sale.setCliente(convertToCliente((java.util.Map<?, ?>) map.get("cliente")));
                        log.debug("[DEBUG_LOG] Cliente convertido: {}", sale.getCliente() != null ? sale.getCliente().getId() : "null");
                    } catch (Exception e) {
                        log.warn("[DEBUG_LOG] Error convirtiendo cliente: {}", e.getMessage());
                        sale.setCliente(null);
                    }
                }
                
                if (map.get("iniciadaPor") != null && map.get("iniciadaPor") instanceof java.util.Map) {
                    try {
                        sale.setIniciadaPor(convertToUser((java.util.Map<?, ?>) map.get("iniciadaPor")));
                        log.debug("[DEBUG_LOG] IniciadaPor convertido: {}", sale.getIniciadaPor() != null ? sale.getIniciadaPor().getUsername() : "null");
                    } catch (Exception e) {
                        log.warn("[DEBUG_LOG] Error convirtiendo iniciadaPor: {}", e.getMessage());
                        sale.setIniciadaPor(null);
                    }
                }
                
                // Inicializar colecciones vacías
                sale.setBienServicioCargados(new java.util.ArrayList<>());
                sale.setBienServicioDevueltos(new java.util.ArrayList<>());
                sale.setDineroCobros(new java.util.HashSet<>());
                sale.setDineroDevoluciones(new java.util.HashSet<>());
                
                log.debug("[DEBUG_LOG] Sale convertido exitosamente: ID={}, totalRestante={}, tipoVenta={}", 
                    sale.getId(), sale.getTotalRestante(), sale.getTipoVenta());
                
                return sale;
                
            } catch (Exception e) {
                log.error("[DEBUG_LOG] Error al convertir objeto a Sale: {}", e.getMessage(), e);
                log.error("[DEBUG_LOG] Datos del Map que causó el error: {}", map);
                return null; // Retornar null en lugar de un Sale fallback
            }
        }
        
        // Si no es un Map, retornar null
        log.warn("[DEBUG_LOG] Objeto recibido no es un Map: {}, retornando null", obj.getClass());
        return null;
    }
    
    /**
     * Convierte un objeto a Double con valor por defecto
     */
    private Double convertToDouble(Object value, Double defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else {
                return Double.parseDouble(value.toString());
            }
        } catch (NumberFormatException e) {
            log.warn("[DEBUG_LOG] Error convirtiendo a Double: {}, usando valor por defecto: {}", value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * Convierte un Map a un objeto Cliente.
     */
    private corp.jamaro.jamaroescritoriofx.appfx.model.Cliente convertToCliente(java.util.Map<?, ?> map) {
        corp.jamaro.jamaroescritoriofx.appfx.model.Cliente cliente = new corp.jamaro.jamaroescritoriofx.appfx.model.Cliente();
        
        if (map.get("id") != null) {
            cliente.setId(UUID.fromString(map.get("id").toString()));
        }
        if (map.get("nombre") != null) {
            cliente.setNombre(map.get("nombre").toString());
        }
        if (map.get("apellido") != null) {
            cliente.setApellido(map.get("apellido").toString());
        }
        if (map.get("razonSocial") != null) {
            cliente.setRazonSocial(map.get("razonSocial").toString());
        }
        if (map.get("dni") != null) {
            cliente.setDni(map.get("dni").toString());
        }
        if (map.get("ruc") != null) {
            cliente.setRuc(map.get("ruc").toString());
        }
        if (map.get("otroDocumento") != null) {
            cliente.setOtroDocumento(map.get("otroDocumento").toString());
        }
        
        return cliente;
    }
    
    /**
     * Convierte un Map a un objeto User.
     */
    private corp.jamaro.jamaroescritoriofx.appfx.model.User convertToUser(java.util.Map<?, ?> map) {
        corp.jamaro.jamaroescritoriofx.appfx.model.User user = new corp.jamaro.jamaroescritoriofx.appfx.model.User();
        
        if (map.get("id") != null) {
            user.setId(UUID.fromString(map.get("id").toString()));
        }
        if (map.get("username") != null) {
            user.setUsername(map.get("username").toString());
        }
        if (map.get("nombre") != null) {
            user.setNombre(map.get("nombre").toString());
        }
        if (map.get("apellidos") != null) {
            user.setApellidos(map.get("apellidos").toString());
        }
        if (map.get("documento") != null) {
            user.setDocumento(map.get("documento").toString());
        }
        
        return user;
    }

    /**
     * Busca Sales completadas (estaPagadoEntregado = true) con filtros opcionales.
     * 
     * Permite filtrar por:
     * - ID específico del Sale
     * - ID del User que inició la venta
     * - Datos de documento del Cliente (DNI, RUC, otroDocumento)
     * - Nombre, apellido o razón social del Cliente (búsqueda parcial)
     * - Código compuesto del Item (búsqueda parcial)
     * - Tipo de venta
     * - Fecha límite
     * 
     * Si no se proporcionan filtros, retorna las últimas 99 ventas completadas.
     * Si hay filtros, retorna máximo 33 resultados.
     * El servidor hace un mapeo completo de cada Sale encontrado.
     * 
     * @param saleId ID específico del Sale (opcional)
     * @param iniciadaPorId ID del User que inició la venta (opcional)
     * @param datoDocumento DNI, RUC u otro documento del Cliente (opcional)
     * @param nombreCliente Nombre, apellido o razón social del Cliente (opcional)
     * @param codCompuesto Código compuesto del Item (opcional)
     * @param tipoVenta Tipo de venta (opcional)
     * @param fechaLimite Fecha límite para filtrar Sales (opcional)
     * @return Flux<Sale> Lista de Sales que coinciden con los filtros
     */
    public Flux<Sale> buscarSalesCompletadas(
            UUID saleId,
            UUID iniciadaPorId,
            String datoDocumento,
            String nombreCliente,
            String codCompuesto,
            String tipoVenta,
            Instant fechaLimite) {

        log.info("Buscando Sales completadas con filtros - saleId: {}, iniciadaPorId: {}, datoDocumento: {}, nombreCliente: {}, codCompuesto: {}, tipoVenta: {}, fechaLimite: {}",
                saleId, iniciadaPorId, datoDocumento, nombreCliente, codCompuesto, tipoVenta, fechaLimite);

        SaleBusquedaRequest request = new SaleBusquedaRequest(
                saleId, iniciadaPorId, datoDocumento, nombreCliente, 
                codCompuesto, tipoVenta, fechaLimite
        );

        return connectionService.authenticatedSubscription(
                ROUTE_BUSCAR_SALES_COMPLETADAS,
                request,
                Sale.class
        ).doOnComplete(() -> log.debug("Búsqueda de Sales completadas finalizada"));
    }

    /**
     * Busca Sales incompletas (estaPagadoEntregado = false) filtradas por User que las inició.
     * 
     * Si iniciadaPorId es null, retorna las últimas 99 ventas incompletas.
     * Si iniciadaPorId no es null, retorna todas las ventas incompletas de ese usuario.
     * Excluye las ventas de tipo PROFORMA.
     * Ordena por fecha de creación de más reciente a más antiguo.
     * El servidor hace un mapeo completo de cada Sale encontrado.
     * 
     * @param iniciadaPorId ID del User que inició la venta (opcional)
     * @return Flux<Sale> Lista de Sales incompletas ordenadas por createdAt DESC
     */
    public Flux<Sale> buscarSalesIncompletas(UUID iniciadaPorId) {
        log.info("Buscando Sales incompletas para usuario: {}", iniciadaPorId);

        SaleIncompletasBusquedaRequest request = new SaleIncompletasBusquedaRequest(iniciadaPorId);

        return connectionService.authenticatedSubscription(
                ROUTE_BUSCAR_SALES_INCOMPLETAS,
                request,
                Sale.class
        ).doOnComplete(() -> log.debug("Búsqueda de Sales incompletas finalizada"));
    }

    /**
     * Se suscribe a actualizaciones de una CajaGui específica por UUID.
     * 
     * El servidor emitirá:
     * 1. La CajaGui especificada como primer elemento (si existe)
     * 2. Actualizaciones en tiempo real cuando se modifique esa CajaGui específica
     * 
     * Esta suscripción es útil para mantener sincronizada la interfaz con el estado
     * actual de una caja específica, incluyendo sus cajas de dinero asociadas.
     * 
     * @param cajaGuiId UUID de la CajaGui a la que suscribirse
     * @return Flux que emite la CajaGui especificada y sus actualizaciones futuras
     */
    public Flux<CajaGui> subscribeToCurrentCajaGui(UUID cajaGuiId) {
        log.info("Suscribiéndose a actualizaciones de CajaGui ID: {}", cajaGuiId);

        if (cajaGuiId == null) {
            return Flux.error(new IllegalArgumentException("El ID de CajaGui es obligatorio"));
        }

        SubscribeToCajaGuiRequest request = new SubscribeToCajaGuiRequest(cajaGuiId);

        return connectionService.authenticatedSubscription(
                ROUTE_SUBSCRIBE_CURRENT_CAJA_GUI,
                request,
                CajaGui.class
        ).doOnNext(cajaGui -> log.debug("Recibida actualización de CajaGui: {} - {}", 
                cajaGui.getId(), cajaGui.getNombreCaja()))
         .doOnCancel(() -> log.debug("Suscripción a CajaGui {} cancelada", cajaGuiId));
    }

    /**
     * Obtiene todas las CajaGui ordenadas por fecha de creación (más recientes primero).
     * 
     * El servidor sigue el patrón del proyecto: consulta simple para IDs, 
     * luego findById para mapeo completo de cada CajaGui con sus relaciones.
     * 
     * @return Flux que emite todas las CajaGui ordenadas por createdAt descendente
     */
    public Flux<CajaGui> getAllCajaGuiOrderedByCreatedAt() {
        log.info("Obteniendo todas las CajaGui ordenadas por fecha de creación");

        return connectionService.authenticatedSubscription(
                ROUTE_GET_ALL_CAJA_GUI_ORDERED,
                null,
                CajaGui.class
        ).doOnNext(cajaGui -> log.debug("CajaGui recibida: {} - {} (creada: {})",
                cajaGui.getId(), cajaGui.getNombreCaja(), cajaGui.getCreatedAt()))
         .doOnComplete(() -> log.debug("Finalizada la obtención de CajaGui ordenadas"));
    }

    /**
     * Crea una nueva CajaGui en el servidor.
     * 
     * El servidor validará que el nombre no esté vacío y asignará automáticamente
     * la fecha de creación. La configuración GUI es opcional y puede contener
     * datos JSON para personalizar la interfaz.
     * 
     * @param nombreCaja Nombre de la caja (obligatorio)
     * @param guiConfig Configuración GUI opcional (JSON)
     * @return Mono<CajaGui> La CajaGui creada
     */
    public Mono<CajaGui> createCajaGui(String nombreCaja, String guiConfig) {
        log.info("Creando nueva CajaGui: {}", nombreCaja);

        CreateCajaGuiRequest request = new CreateCajaGuiRequest(nombreCaja, guiConfig);

        return connectionService.authenticatedRequest(
                ROUTE_CREATE_CAJA_GUI,
                request,
                CajaGui.class
        ).doOnSuccess(cajaGui -> log.info("CajaGui creada exitosamente con ID: {}", cajaGui.getId()));
    }

    /**
     * Inicializa una CajaDineroEfectivo para una CajaGui existente.
     * 
     * El servidor validará que la CajaGui exista y no tenga ya una CajaDineroEfectivo.
     * Puede proporcionar el monto inicial o los detalles de denominaciones, o ambos.
     * Si se proporcionan ambos, el servidor verificará que coincidan dentro de la tolerancia.
     * 
     * @param cajaGuiId ID de la CajaGui existente (obligatorio)
     * @param nombre Nombre para la CajaDineroEfectivo (obligatorio)
     * @param montoInicialEfectivo Monto inicial en efectivo (opcional si se proporcionan detalles)
     * @param diezCentimos Cantidad de monedas de 10 céntimos
     * @param veinteCentimos Cantidad de monedas de 20 céntimos
     * @param cincuentaCentimos Cantidad de monedas de 50 céntimos
     * @param unSol Cantidad de monedas de 1 sol
     * @param dosSoles Cantidad de monedas de 2 soles
     * @param cincoSoles Cantidad de monedas de 5 soles
     * @param diezSoles Cantidad de billetes de 10 soles
     * @param veinteSoles Cantidad de billetes de 20 soles
     * @param cincuentaSoles Cantidad de billetes de 50 soles
     * @param cienSoles Cantidad de billetes de 100 soles
     * @param doscientosSoles Cantidad de billetes de 200 soles
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroEfectivo
     */
    public Mono<CajaGui> initializeCajaDineroEfectivo(
            UUID cajaGuiId,
            String nombre,
            Double montoInicialEfectivo,
            Integer diezCentimos,
            Integer veinteCentimos,
            Integer cincuentaCentimos,
            Integer unSol,
            Integer dosSoles,
            Integer cincoSoles,
            Integer diezSoles,
            Integer veinteSoles,
            Integer cincuentaSoles,
            Integer cienSoles,
            Integer doscientosSoles) {

        log.info("Inicializando CajaDineroEfectivo para CajaGui ID: {}", cajaGuiId);

        InitializeCajaDineroEfectivoRequest request = new InitializeCajaDineroEfectivoRequest(
                cajaGuiId, nombre, montoInicialEfectivo,
                diezCentimos, veinteCentimos, cincuentaCentimos,
                unSol, dosSoles, cincoSoles,
                diezSoles, veinteSoles, cincuentaSoles,
                cienSoles, doscientosSoles
        );

        return connectionService.authenticatedRequest(
                ROUTE_INITIALIZE_CAJA_DINERO_EFECTIVO,
                request,
                CajaGui.class
        ).doOnSuccess(cajaGui -> log.info("CajaDineroEfectivo inicializada exitosamente para CajaGui ID: {}", cajaGuiId));
    }

    /**
     * Inicializa una CajaDineroDigital para una CajaGui existente.
     * 
     * El servidor validará que la CajaGui exista y no tenga ya una CajaDineroDigital.
     * La cuenta digital y el monto inicial son obligatorios.
     * 
     * @param cajaGuiId ID de la CajaGui existente (obligatorio)
     * @param cuentaDigitalAsignada Detalles de la cuenta digital (obligatorio)
     * @param montoInicialDigital Monto inicial digital (obligatorio)
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroDigital
     */
    public Mono<CajaGui> initializeCajaDineroDigital(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital) {

        log.info("Inicializando CajaDineroDigital para CajaGui ID: {}", cajaGuiId);

        InitializeCajaDineroDigitalRequest request = new InitializeCajaDineroDigitalRequest(
                cajaGuiId, cuentaDigitalAsignada, montoInicialDigital
        );

        return connectionService.authenticatedRequest(
                ROUTE_INITIALIZE_CAJA_DINERO_DIGITAL,
                request,
                CajaGui.class
        ).doOnSuccess(cajaGui -> log.info("CajaDineroDigital inicializada exitosamente para CajaGui ID: {}", cajaGuiId));
    }

    /**
     * Cierra una CajaDineroEfectivo calculando los montos de cierre.
     * 
     * El servidor calculará:
     * - Cierre declarado: suma de todas las denominaciones proporcionadas
     * - Cierre calculado: montoInicial + totalEntradas - totalSalidas
     * - Diferencia: cierreCalculado - cierreDeclarado
     * 
     * Después del cierre, la relación entre CajaGui y CajaDineroEfectivo se rompe.
     * 
     * @param cajaGuiId ID de la CajaGui
     * @param cajaDineroEfectivoId ID de la CajaDineroEfectivo a cerrar
     * @param cierreDiezCentimos Cantidad de monedas de 10 céntimos en el cierre
     * @param cierreVeinteCentimos Cantidad de monedas de 20 céntimos en el cierre
     * @param cierreCincuentaCentimos Cantidad de monedas de 50 céntimos en el cierre
     * @param cierreUnSol Cantidad de monedas de 1 sol en el cierre
     * @param cierreDosSoles Cantidad de monedas de 2 soles en el cierre
     * @param cierreCincoSoles Cantidad de monedas de 5 soles en el cierre
     * @param cierreDiezSoles Cantidad de billetes de 10 soles en el cierre
     * @param cierreVeinteSoles Cantidad de billetes de 20 soles en el cierre
     * @param cierreCincuentaSoles Cantidad de billetes de 50 soles en el cierre
     * @param cierreCienSoles Cantidad de billetes de 100 soles en el cierre
     * @param cierreDoscientosSoles Cantidad de billetes de 200 soles en el cierre
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    public Mono<CajaGui> closeCajaDineroEfectivo(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles) {

        log.info("Cerrando CajaDineroEfectivo ID: {} para CajaGui ID: {}", cajaDineroEfectivoId, cajaGuiId);

        CloseCajaDineroEfectivoRequest request = new CloseCajaDineroEfectivoRequest(
                cajaGuiId, cajaDineroEfectivoId,
                cierreDiezCentimos, cierreVeinteCentimos, cierreCincuentaCentimos,
                cierreUnSol, cierreDosSoles, cierreCincoSoles,
                cierreDiezSoles, cierreVeinteSoles, cierreCincuentaSoles,
                cierreCienSoles, cierreDoscientosSoles
        );

        return connectionService.authenticatedRequest(
                ROUTE_CLOSE_CAJA_DINERO_EFECTIVO,
                request,
                CajaGui.class
        ).doOnSuccess(cajaGui -> log.info("CajaDineroEfectivo cerrada exitosamente para CajaGui ID: {}", cajaGuiId));
    }

    /**
     * Cierra una CajaDineroDigital con el monto declarado.
     * 
     * El servidor calculará:
     * - Cierre calculado: montoInicial + totalEntradas - totalSalidas
     * - Diferencia: cierreCalculado - cierreDeclarado
     * 
     * Después del cierre, la relación entre CajaGui y CajaDineroDigital se rompe.
     * 
     * @param cajaGuiId ID de la CajaGui
     * @param cajaDineroDigitalId ID de la CajaDineroDigital a cerrar
     * @param cierreDigitalDeclarado Monto declarado por el usuario para el cierre
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    public Mono<CajaGui> closeCajaDineroDigital(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado) {

        log.info("Cerrando CajaDineroDigital ID: {} para CajaGui ID: {}", cajaDineroDigitalId, cajaGuiId);

        CloseCajaDineroDigitalRequest request = new CloseCajaDineroDigitalRequest(
                cajaGuiId, cajaDineroDigitalId, cierreDigitalDeclarado
        );

        return connectionService.authenticatedRequest(
                ROUTE_CLOSE_CAJA_DINERO_DIGITAL,
                request,
                CajaGui.class
        ).doOnSuccess(cajaGui -> log.info("CajaDineroDigital cerrada exitosamente para CajaGui ID: {}", cajaGuiId));
    }

    // Records para agrupar los parámetros de cada solicitud al servidor
    // Estos records corresponden a los definidos en el CajaGuiController del servidor

    /**
     * Datos para la solicitud de búsqueda de Sales completadas.
     * Todos los campos son opcionales y actúan como filtros.
     */
    public record SaleBusquedaRequest(
            UUID saleId,
            UUID iniciadaPorId,
            String datoDocumento,
            String nombreCliente,
            String codCompuesto,
            String tipoVenta,
            Instant fechaLimite
    ) {}

    /**
     * Datos para la solicitud de búsqueda de Sales incompletas.
     * Si iniciadaPorId es null, se obtienen todas las ventas incompletas.
     */
    public record SaleIncompletasBusquedaRequest(
            UUID iniciadaPorId
    ) {}

    /**
     * Datos para la solicitud de suscripción a una CajaGui específica.
     * El cajaGuiId es obligatorio para identificar la caja a monitorear.
     */
    public record SubscribeToCajaGuiRequest(
            UUID cajaGuiId
    ) {}

    /**
     * Datos para la solicitud de crear una nueva CajaGui.
     * El nombreCaja es obligatorio, guiConfig es opcional.
     */
    public record CreateCajaGuiRequest(
            String nombreCaja,
            String guiConfig
    ) {}

    /**
     * Datos para la solicitud de inicializar una CajaDineroEfectivo.
     * Incluye todos los detalles de denominaciones para el conteo inicial.
     */
    public record InitializeCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            String nombre,
            Double montoInicialEfectivo,
            Integer diezCentimos,
            Integer veinteCentimos,
            Integer cincuentaCentimos,
            Integer unSol,
            Integer dosSoles,
            Integer cincoSoles,
            Integer diezSoles,
            Integer veinteSoles,
            Integer cincuentaSoles,
            Integer cienSoles,
            Integer doscientosSoles
    ) {}

    /**
     * Datos para la solicitud de inicializar una CajaDineroDigital.
     * Requiere la cuenta digital asignada y el monto inicial.
     */
    public record InitializeCajaDineroDigitalRequest(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital
    ) {}

    /**
     * Datos para la solicitud de cerrar una CajaDineroEfectivo.
     * Incluye todos los detalles de denominaciones para el conteo de cierre.
     */
    public record CloseCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles
    ) {}

    /**
     * Datos para la solicitud de cerrar una CajaDineroDigital.
     * Solo requiere el monto declarado por el usuario.
     */
    public record CloseCajaDineroDigitalRequest(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado
    ) {}
}
