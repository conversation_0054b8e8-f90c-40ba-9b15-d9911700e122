package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroEntrada;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class IngresoExtraDinero {
    private UUID id;

    private String motivo;
    private Double monto;//el monto en Soles ya que el sistema trabajar por defecto en soles.

    private User realizadoPor;

    private Instant realizadoEl;

    private Set<DineroEntrada> dineroEntradaIngresado;
}
