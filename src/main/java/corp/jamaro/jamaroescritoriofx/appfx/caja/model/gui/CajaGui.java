package corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CajaDineroDigital;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CajaDineroEfectivo;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class CajaGui {
    private UUID id;

    private String nombreCaja;

    private CajaDineroEfectivo cajaDineroEfectivo;

    private CajaDineroDigital cajaDineroDigital;

    private String guiConfig; // un json con las preferencias del usuario para su gui (color de usuario, tamaña letra, posición del slider, etc.)

    private Instant createdAt;
}
