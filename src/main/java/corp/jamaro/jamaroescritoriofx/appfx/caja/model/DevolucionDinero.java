package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroSalida;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class DevolucionDinero {
    private UUID id;

    private Double montoADevolver;//el monto en Soles

    //aqui podemos ver que user o users devolvieron el dinero ya que dinero tiene ese campo
    private Set<DineroSalida> dineroDevuelto;//es un set ya que puede ser una devolucion mixta yape y efectivo por ejemplo; en un inicio nisiquiera va a existir hasta que se haga la devolucion.

    private User iniciadoPor;
    private Instant creadoEl;
    private Instant devueltoEl;

    private Boolean estaDevuelto = false;// true (devuelto) false (no devuelto) para encontrar facilmente que dineros faltan devolver en caja.
}
