package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroEntrada;
import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroSalida;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CobroDineroProgramado {

    private UUID id;

    private Double montoACobrar;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    private Double montoRestante;//el monto restante a cobrar, se actualiza cada vez que se cobra dinero.

    //aquí podemos ver quien o quienes cobraron el dinero, ya que dinero tiene campo user
    private Set<DineroEntrada> cobros;//pueden ser varios ya que hay varios metodos de pago y puede ser pago mixto, y en credito pueden ser varios pagos en un inicio nisiquiera va a existir hasta que se cobre, se programará un cobro y cuando se cobra recien se crea el dinero con esEntrada true.

    private Set<DineroSalida> vueltos;//los vueltos que se dan pueden ser Efectivo o Digital

    private User iniciadoPor;//usuario que inicia el cobro en un Sale o alguna otra entrada de dinero

    private Instant creadoEl;
    private Instant fechaLimite;
    private Instant terminadoDeCobrarEl;

    private Boolean estaCobrado;// true (cobrado por completo) false (no cobrado por completo) para encontrar facilmente que dinero faltan cobrar en caja.

}
