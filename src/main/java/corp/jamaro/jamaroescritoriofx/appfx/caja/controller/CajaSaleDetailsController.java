package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.function.Consumer;

/**
 * Controlador reutilizable para mostrar detalles de un Sale.
 * Permite configurar acciones personalizadas para los botones desde otros controladores.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class CajaSaleDetailsController extends BaseController {

    // Componentes FXML
    @FXML
    private VBox rootSaleDetails;

    // Labels de información general
    @FXML
    private Label lblCliente;
    @FXML
    private Label lblUsuario;
    @FXML
    private Label lblTipoVenta;
    @FXML
    private Label lblTotalAcordado;
    @FXML
    private Label lblTotalRestante;
    @FXML
    private Label lblEstado;

    // SplitPane y tablas
    @FXML
    private SplitPane spBienesServicios;
    @FXML
    private TableView<BienServicioCargado> tableBienServicioCargado;
    @FXML
    private TableView<BienServicioDevuelto> tableBienServicioDevuelto;

    // Columnas de BienServicioCargado
    @FXML
    private TableColumn<BienServicioCargado, String> colCodCompuesto;
    @FXML
    private TableColumn<BienServicioCargado, String> colMarca;
    @FXML
    private TableColumn<BienServicioCargado, String> colDescripcion;
    @FXML
    private TableColumn<BienServicioCargado, String> colPrecioAcordado;
    @FXML
    private TableColumn<BienServicioCargado, String> colCantidad;
    @FXML
    private TableColumn<BienServicioCargado, String> colMontoAcordado;

    // Columnas de BienServicioDevuelto
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevCodCompuesto;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevMarca;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevDescripcion;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevPrecioAcordado;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevCantidad;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevMontoDevuelto;
    @FXML
    private TableColumn<BienServicioDevuelto, String> colDevMotivo;

    // Botones
    @FXML
    private Button btnCobrar;
    @FXML
    private Button btnImprimir;

    // Estado interno
    private Sale currentSale;
    private NumberFormat currencyFormat;
    private NumberFormat decimalFormat;

    // Acciones configurables
    private Consumer<Sale> cobrarAction;
    private Consumer<Sale> imprimirAction;

    @Override
    public void initialize() {
        log.info("Inicializando CajaSaleDetailsController");
        
        // Configurar formatos
        currencyFormat = NumberFormat.getCurrencyInstance();
        decimalFormat = NumberFormat.getNumberInstance();
        decimalFormat.setMaximumFractionDigits(2);
        decimalFormat.setMinimumFractionDigits(2);

        // Configurar columnas de BienServicioCargado
        setupBienServicioCargadoColumns();
        
        // Configurar columnas de BienServicioDevuelto
        setupBienServicioDevueltoColumns();

        // Configurar eventos de botones
        btnCobrar.setOnAction(e -> handleCobrar());
        btnImprimir.setOnAction(e -> handleImprimir());

        // Inicialmente deshabilitar botones
        btnCobrar.setDisable(true);
        btnImprimir.setDisable(true);

        log.info("CajaSaleDetailsController inicializado correctamente");
    }

    /**
     * Configura las columnas de la tabla de BienServicioCargado
     */
    private void setupBienServicioCargadoColumns() {
        colCodCompuesto.setCellValueFactory(cellData -> {
            BienServicioCargado item = cellData.getValue();
            String codigo = (item.getItem() != null && item.getItem().getCodCompuesto() != null) 
                ? item.getItem().getCodCompuesto() : "-";
            return new SimpleStringProperty(codigo);
        });

        colMarca.setCellValueFactory(cellData -> {
            BienServicioCargado item = cellData.getValue();
            String marca = (item.getItem() != null && item.getItem().getMarca() != null) 
                ? item.getItem().getMarca().getNombre() : "-";
            return new SimpleStringProperty(marca);
        });

        colDescripcion.setCellValueFactory(cellData -> {
            BienServicioCargado item = cellData.getValue();
            String descripcion = (item.getItem() != null && item.getItem().getDescripcion() != null) 
                ? item.getItem().getDescripcion() : 
                (item.getDescripcionDelBienServicio() != null ? item.getDescripcionDelBienServicio() : "-");
            return new SimpleStringProperty(descripcion);
        });

        colPrecioAcordado.setCellValueFactory(cellData -> {
            Double precio = cellData.getValue().getPrecioAcordado();
            String precioStr = precio != null ? currencyFormat.format(precio) : "-";
            return new SimpleStringProperty(precioStr);
        });

        colCantidad.setCellValueFactory(cellData -> {
            Double cantidad = cellData.getValue().getCantidad();
            String cantidadStr = cantidad != null ? decimalFormat.format(cantidad) : "-";
            return new SimpleStringProperty(cantidadStr);
        });

        colMontoAcordado.setCellValueFactory(cellData -> {
            Double monto = cellData.getValue().getMontoAcordado();
            String montoStr = monto != null ? currencyFormat.format(monto) : "-";
            return new SimpleStringProperty(montoStr);
        });
    }

    /**
     * Configura las columnas de la tabla de BienServicioDevuelto
     */
    private void setupBienServicioDevueltoColumns() {
        colDevCodCompuesto.setCellValueFactory(cellData -> {
            BienServicioDevuelto item = cellData.getValue();
            String codigo = (item.getItem() != null && item.getItem().getCodCompuesto() != null) 
                ? item.getItem().getCodCompuesto() : "-";
            return new SimpleStringProperty(codigo);
        });

        colDevMarca.setCellValueFactory(cellData -> {
            BienServicioDevuelto item = cellData.getValue();
            String marca = (item.getItem() != null && item.getItem().getMarca() != null) 
                ? item.getItem().getMarca().getNombre() : "-";
            return new SimpleStringProperty(marca);
        });

        colDevDescripcion.setCellValueFactory(cellData -> {
            BienServicioDevuelto item = cellData.getValue();
            String descripcion = (item.getItem() != null && item.getItem().getDescripcion() != null) 
                ? item.getItem().getDescripcion() : 
                (item.getDescripcionDelBienServicio() != null ? item.getDescripcionDelBienServicio() : "-");
            return new SimpleStringProperty(descripcion);
        });

        colDevPrecioAcordado.setCellValueFactory(cellData -> {
            Double precio = cellData.getValue().getPrecioAcordadoDevolver();
            String precioStr = precio != null ? currencyFormat.format(precio) : "-";
            return new SimpleStringProperty(precioStr);
        });

        colDevCantidad.setCellValueFactory(cellData -> {
            Double cantidad = cellData.getValue().getCantidad();
            String cantidadStr = cantidad != null ? decimalFormat.format(cantidad) : "-";
            return new SimpleStringProperty(cantidadStr);
        });

        colDevMontoDevuelto.setCellValueFactory(cellData -> {
            Double monto = cellData.getValue().getMontoDevuelto();
            String montoStr = monto != null ? currencyFormat.format(monto) : "-";
            return new SimpleStringProperty(montoStr);
        });

        colDevMotivo.setCellValueFactory(cellData -> {
            String motivo = cellData.getValue().getMotivo();
            return new SimpleStringProperty(motivo != null ? motivo : "-");
        });
    }

    /**
     * Actualiza la vista con los datos de un Sale
     */
    public void setSale(Sale sale) {
        this.currentSale = sale;
        
        if (sale == null) {
            clearSaleData();
            return;
        }

        updateSaleInfo(sale);
        updateTables(sale);
        
        // Habilitar botones si hay un sale válido
        btnCobrar.setDisable(false);
        btnImprimir.setDisable(false);
    }

    /**
     * Limpia los datos mostrados
     */
    private void clearSaleData() {
        lblCliente.setText("-");
        lblUsuario.setText("-");
        lblTipoVenta.setText("-");
        lblTotalAcordado.setText("S/ 0.00");
        lblTotalRestante.setText("S/ 0.00");
        lblEstado.setText("-");
        
        tableBienServicioCargado.setItems(FXCollections.observableArrayList());
        tableBienServicioDevuelto.setItems(FXCollections.observableArrayList());
        
        btnCobrar.setDisable(true);
        btnImprimir.setDisable(true);
    }

    /**
     * Actualiza la información general del Sale
     */
    private void updateSaleInfo(Sale sale) {
        lblCliente.setText(sale.getCliente() != null ? sale.getCliente().getNombre() : "Sin cliente");
        lblUsuario.setText(sale.getIniciadaPor() != null ? sale.getIniciadaPor().getUsername() : "-");
        lblTipoVenta.setText(sale.getTipoVenta() != null ? sale.getTipoVenta().toString() : "-");
        
        lblTotalAcordado.setText(sale.getTotalMontoAcordado() != null ? 
            currencyFormat.format(sale.getTotalMontoAcordado()) : "S/ 0.00");
        lblTotalRestante.setText(sale.getTotalRestante() != null ? 
            currencyFormat.format(sale.getTotalRestante()) : "S/ 0.00");
        
        lblEstado.setText(Boolean.TRUE.equals(sale.getEstaPagadoEntregado()) ? "Pagado/Entregado" : "Pendiente");
    }

    /**
     * Actualiza las tablas con los datos del Sale
     */
    private void updateTables(Sale sale) {
        // Actualizar tabla de BienServicioCargado
        ObservableList<BienServicioCargado> cargados = FXCollections.observableArrayList();
        if (sale.getBienServicioCargados() != null) {
            cargados.addAll(sale.getBienServicioCargados());
        }
        tableBienServicioCargado.setItems(cargados);

        // Actualizar tabla de BienServicioDevuelto
        ObservableList<BienServicioDevuelto> devueltos = FXCollections.observableArrayList();
        if (sale.getBienServicioDevueltos() != null) {
            devueltos.addAll(sale.getBienServicioDevueltos());
        }
        tableBienServicioDevuelto.setItems(devueltos);
        
        // Ocultar tabla de devueltos si está vacía
        if (devueltos.isEmpty()) {
            spBienesServicios.setDividerPositions(1.0);
        } else {
            spBienesServicios.setDividerPositions(0.7);
        }
    }

    /**
     * Configura la acción del botón Cobrar
     */
    public void setCobrarAction(Consumer<Sale> action) {
        this.cobrarAction = action;
    }

    /**
     * Configura la acción del botón Imprimir
     */
    public void setImprimirAction(Consumer<Sale> action) {
        this.imprimirAction = action;
    }

    /**
     * Maneja el evento del botón Cobrar
     */
    private void handleCobrar() {
        if (currentSale != null && cobrarAction != null) {
            cobrarAction.accept(currentSale);
        } else {
            log.warn("No se puede cobrar: Sale o acción no configurada");
        }
    }

    /**
     * Maneja el evento del botón Imprimir
     */
    private void handleImprimir() {
        if (currentSale != null && imprimirAction != null) {
            imprimirAction.accept(currentSale);
        } else {
            log.warn("No se puede imprimir: Sale o acción no configurada");
        }
    }

    /**
     * Obtiene el Sale actual
     */
    public Sale getCurrentSale() {
        return currentSale;
    }
}
