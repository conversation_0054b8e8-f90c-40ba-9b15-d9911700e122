package corp.jamaro.jamaroescritoriofx.appfx.caja.util;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.geometry.HPos;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.UUID;

/**
 * Utilidad para crear y mostrar diálogos específicos relacionados con caja.
 * Centraliza la lógica de diálogos complejos para mantener los controladores más limpios.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CajaDialogUtil {

    private static final String STYLESHEET_PATH = "/css/styles.css";
    private static final String SEARCH_PRODUCT_STYLESHEET_PATH = "/css/searchProduct.css";
    private static final String CAJA_STYLESHEET_PATH = "/css/caja.css";

    private final AlertUtil alertUtil;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    /**
     * Muestra el diálogo de selección de caja con estilo moderno.
     */
    public Optional<CajaSelectionResult> showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        Dialog<CajaSelectionResult> dialog = new Dialog<>();
        dialog.setTitle("Seleccionar Caja");

        // Crear botones
        ButtonType selectButtonType = new ButtonType("Seleccionar", ButtonBar.ButtonData.OK_DONE);
        ButtonType addButtonType = new ButtonType("Agregar", ButtonBar.ButtonData.OTHER);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(selectButtonType, addButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Seleccione la caja con la que desea trabajar:");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 14px; -fx-font-weight: 600;");

        // ListView para las cajas
        ListView<CajaGui> listView = new ListView<>();
        listView.getItems().addAll(cajaGuiList);
        listView.setPrefHeight(200);
        listView.setMaxWidth(Double.MAX_VALUE);

        // Configurar el cell factory para mostrar información de la caja
        listView.setCellFactory(param -> new ListCell<CajaGui>() {
            @Override
            protected void updateItem(CajaGui item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    String efectivoStatus = item.getCajaDineroEfectivo() != null ? "Efectivo: ✓" : "Efectivo: ✗";
                    String digitalStatus = item.getCajaDineroDigital() != null ? "Digital: ✓" : "Digital: ✗";
                    setText(String.format("%s (%s, %s)", item.getNombreCaja(), efectivoStatus, digitalStatus));
                }
            }
        });

        contentBox.getChildren().addAll(titleLabel, listView);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Enable/disable select button based on selection
        Button selectButton = (Button) dialog.getDialogPane().lookupButton(selectButtonType);
        selectButton.setDisable(true);
        listView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> selectButton.setDisable(newSelection == null)
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == selectButtonType) {
                CajaGui selectedCaja = listView.getSelectionModel().getSelectedItem();
                return selectedCaja != null ? new CajaSelectionResult(selectedCaja, false) : null;
            } else if (dialogButton == addButtonType) {
                return new CajaSelectionResult(null, true);
            }
            return null;
        });

        // Configurar focus en el ListView
        Platform.runLater(listView::requestFocus);

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para crear una nueva CajaGui cuando no hay cajas disponibles.
     */
    public Optional<CreateCajaData> showCreateCajaDialog() {
        Dialog<CreateCajaData> dialog = new Dialog<>();
        dialog.setTitle("Crear Nueva Caja");

        // Crear botones
        ButtonType createButtonType = new ButtonType("Crear Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(createButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Crear Nueva Caja");
        titleLabel.getStyleClass().add("cobro-info-label");

        // Campo de nombre
        Label nameLabel = new Label("Nombre de la Caja:");
        nameLabel.getStyleClass().add("cobro-user-label");
        TextField nameField = new TextField();
        nameField.setPromptText("Ingrese el nombre de la caja");
        nameField.setPrefWidth(300);

        // Campo de configuración (opcional)
        Label configLabel = new Label("Configuración GUI (opcional):");
        configLabel.getStyleClass().add("cobro-user-label");
        TextArea configArea = new TextArea();
        configArea.setPromptText("Configuración JSON opcional para personalizar la interfaz");
        configArea.setPrefRowCount(3);
        configArea.setPrefWidth(300);

        contentBox.getChildren().addAll(titleLabel, nameLabel, nameField, configLabel, configArea);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-plus-circle");

        // Enable/disable create button based on name field
        Button createButton = (Button) dialog.getDialogPane().lookupButton(createButtonType);
        createButton.setDisable(true);
        nameField.textProperty().addListener(
            (obs, oldText, newText) -> createButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == createButtonType) {
                String name = nameField.getText().trim();
                String config = configArea.getText().trim();
                if (!name.isEmpty()) {
                    return new CreateCajaData(name, config.isEmpty() ? null : config);
                }
            }
            return null;
        });

        // Configurar focus en el campo de nombre
        Platform.runLater(() -> {
            nameField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para inicializar CajaDineroEfectivo
     */
    public Optional<InitializeCajaDineroEfectivoData> showInitializeCajaDineroEfectivoDialog(UUID cajaGuiId) {
        Dialog<InitializeCajaDineroEfectivoData> dialog = new Dialog<>();
        dialog.setTitle("Inicializar Caja de Efectivo");

        // Crear botones
        ButtonType initButtonType = new ButtonType("Inicializar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(initButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);
        contentBox.setPrefWidth(650);
        contentBox.setMaxWidth(650);

        // Título
        Label titleLabel = new Label("Inicializar Caja de Efectivo");
        titleLabel.getStyleClass().add("cobro-info-label");

        // Campo de nombre
        Label nameLabel = new Label("Nombre de la Caja de Efectivo:");
        nameLabel.getStyleClass().add("cobro-user-label");
        TextField nameField = new TextField();
        nameField.setPromptText("Ej: Caja Principal");
        nameField.setPrefWidth(300);

        // Campo de monto inicial
        Label montoLabel = new Label("Monto Inicial (S/):");
        montoLabel.getStyleClass().add("cobro-user-label");
        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(150);

        // Sección de denominaciones
        Label denomLabel = new Label("Denominaciones (opcional):");
        denomLabel.getStyleClass().add("cobro-user-label");

        GridPane denomGrid = createDenominacionesGrid();

        contentBox.getChildren().addAll(
            titleLabel, new Separator(),
            nameLabel, nameField,
            montoLabel, montoField,
            denomLabel, denomGrid
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Enable/disable init button based on name field
        Button initButton = (Button) dialog.getDialogPane().lookupButton(initButtonType);
        initButton.setDisable(true);
        nameField.textProperty().addListener(
            (obs, oldText, newText) -> initButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == initButtonType) {
                String nombre = nameField.getText().trim();
                if (!nombre.isEmpty()) {
                    try {
                        Double montoInicial = montoField.getText().trim().isEmpty() ?
                            null : Double.parseDouble(montoField.getText().trim());

                        return new InitializeCajaDineroEfectivoData(
                            cajaGuiId, nombre, montoInicial,
                            getIntegerFromField(denomGrid, "diezCentimos"),
                            getIntegerFromField(denomGrid, "veinteCentimos"),
                            getIntegerFromField(denomGrid, "cincuentaCentimos"),
                            getIntegerFromField(denomGrid, "unSol"),
                            getIntegerFromField(denomGrid, "dosSoles"),
                            getIntegerFromField(denomGrid, "cincoSoles"),
                            getIntegerFromField(denomGrid, "diezSoles"),
                            getIntegerFromField(denomGrid, "veinteSoles"),
                            getIntegerFromField(denomGrid, "cincuentaSoles"),
                            getIntegerFromField(denomGrid, "cienSoles"),
                            getIntegerFromField(denomGrid, "doscientosSoles")
                        );
                    } catch (NumberFormatException e) {
                        log.warn("Error al parsear números en inicialización de efectivo", e);
                        return null;
                    }
                }
            }
            return null;
        });

        // Configurar focus en el campo de nombre
        Platform.runLater(() -> nameField.requestFocus());

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para inicializar CajaDineroDigital
     */
    public Optional<InitializeCajaDineroDigitalData> showInitializeCajaDineroDigitalDialog(UUID cajaGuiId) {
        Dialog<InitializeCajaDineroDigitalData> dialog = new Dialog<>();
        dialog.setTitle("Inicializar Caja Digital");

        // Crear botones
        ButtonType initButtonType = new ButtonType("Inicializar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(initButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Inicializar Caja Digital");
        titleLabel.getStyleClass().add("cobro-info-label");

        // Campo de cuenta digital
        Label cuentaLabel = new Label("Cuenta Digital Asignada:");
        cuentaLabel.getStyleClass().add("cobro-user-label");
        TextField cuentaField = new TextField();
        cuentaField.setPromptText("Ej: Yape, Plin, BCP, etc.");
        cuentaField.setPrefWidth(300);

        // Campo de monto inicial
        Label montoLabel = new Label("Monto Inicial Digital (S/):");
        montoLabel.getStyleClass().add("cobro-user-label");
        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(150);

        contentBox.getChildren().addAll(
            titleLabel, new Separator(),
            cuentaLabel, cuentaField,
            montoLabel, montoField
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos para diálogo digital
        applyDialogStyles(dialog, "digital");
        setDialogIcon(dialog, "fas-credit-card");

        // Enable/disable init button based on cuenta field
        Button initButton = (Button) dialog.getDialogPane().lookupButton(initButtonType);
        initButton.setDisable(true);
        cuentaField.textProperty().addListener(
            (obs, oldText, newText) -> initButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == initButtonType) {
                String cuenta = cuentaField.getText().trim();
                if (!cuenta.isEmpty()) {
                    try {
                        Double montoInicial = montoField.getText().trim().isEmpty() ?
                            0.0 : Double.parseDouble(montoField.getText().trim());

                        return new InitializeCajaDineroDigitalData(cajaGuiId, cuenta, montoInicial);
                    } catch (NumberFormatException e) {
                        log.warn("Error al parsear monto inicial digital", e);
                        return null;
                    }
                }
            }
            return null;
        });

        // Configurar focus en el campo de cuenta
        Platform.runLater(() -> cuentaField.requestFocus());

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para cerrar CajaDineroEfectivo
     */
    public Optional<CloseCajaDineroEfectivoData> showCloseCajaDineroEfectivoDialog(UUID cajaGuiId, UUID cajaDineroEfectivoId) {
        Dialog<CloseCajaDineroEfectivoData> dialog = new Dialog<>();
        dialog.setTitle("Cerrar Caja de Efectivo");

        // Crear botones
        ButtonType closeButtonType = new ButtonType("Cerrar Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(closeButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);
        contentBox.setPrefWidth(600);
        contentBox.setMaxWidth(600);
        contentBox.setPrefHeight(500);
        contentBox.setMaxHeight(500);

        // Título
        Label titleLabel = new Label("Cerrar Caja de Efectivo");
        titleLabel.getStyleClass().add("cobro-info-label");

        Label messageLabel = new Label("Ingrese el conteo final de denominaciones:");
        messageLabel.getStyleClass().add("cobro-user-label");

        // Sección de denominaciones para cierre
        GridPane denomGrid = createDenominacionesGrid();

        contentBox.getChildren().addAll(
            titleLabel, messageLabel, new Separator(), denomGrid
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos para diálogo de efectivo
        applyDialogStyles(dialog, "efectivo");
        setDialogIcon(dialog, "fas-cash-register");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == closeButtonType) {
                return new CloseCajaDineroEfectivoData(
                    cajaGuiId, cajaDineroEfectivoId,
                    getIntegerFromField(denomGrid, "diezCentimos"),
                    getIntegerFromField(denomGrid, "veinteCentimos"),
                    getIntegerFromField(denomGrid, "cincuentaCentimos"),
                    getIntegerFromField(denomGrid, "unSol"),
                    getIntegerFromField(denomGrid, "dosSoles"),
                    getIntegerFromField(denomGrid, "cincoSoles"),
                    getIntegerFromField(denomGrid, "diezSoles"),
                    getIntegerFromField(denomGrid, "veinteSoles"),
                    getIntegerFromField(denomGrid, "cincuentaSoles"),
                    getIntegerFromField(denomGrid, "cienSoles"),
                    getIntegerFromField(denomGrid, "doscientosSoles")
                );
            }
            return null;
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para cerrar CajaDineroDigital
     */
    public Optional<CloseCajaDineroDigitalData> showCloseCajaDineroDigitalDialog(UUID cajaGuiId, UUID cajaDineroDigitalId) {
        Dialog<CloseCajaDineroDigitalData> dialog = new Dialog<>();
        dialog.setTitle("Cerrar Caja Digital");

        // Crear botones
        ButtonType closeButtonType = new ButtonType("Cerrar Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(closeButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Cerrar Caja Digital");
        titleLabel.getStyleClass().add("cobro-info-label");

        Label messageLabel = new Label("Ingrese el monto final declarado:");
        messageLabel.getStyleClass().add("cobro-user-label");

        // Campo de monto final
        Label montoLabel = new Label("Monto Final Declarado (S/):");
        montoLabel.getStyleClass().add("cobro-user-label");
        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(150);

        contentBox.getChildren().addAll(
            titleLabel, messageLabel, new Separator(),
            montoLabel, montoField
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos para diálogo digital
        applyDialogStyles(dialog, "digital");
        setDialogIcon(dialog, "fas-credit-card");

        // Enable/disable close button based on monto field
        Button closeButton = (Button) dialog.getDialogPane().lookupButton(closeButtonType);
        closeButton.setDisable(true);
        montoField.textProperty().addListener(
            (obs, oldText, newText) -> {
                try {
                    if (newText != null && !newText.trim().isEmpty()) {
                        Double.parseDouble(newText.trim());
                        closeButton.setDisable(false);
                    } else {
                        closeButton.setDisable(true);
                    }
                } catch (NumberFormatException e) {
                    closeButton.setDisable(true);
                }
            }
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == closeButtonType) {
                try {
                    Double monto = Double.parseDouble(montoField.getText().trim());
                    return new CloseCajaDineroDigitalData(cajaGuiId, cajaDineroDigitalId, monto);
                } catch (NumberFormatException e) {
                    log.warn("Error al parsear monto final digital", e);
                    return null;
                }
            }
            return null;
        });

        // Configurar focus en el campo de monto
        Platform.runLater(() -> montoField.requestFocus());

        return dialog.showAndWait();
    }

    // Métodos de utilidad privados

    /**
     * Crea un GridPane para las denominaciones de efectivo
     */
    private GridPane createDenominacionesGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(30);
        grid.setVgap(15);
        grid.setPadding(new Insets(20));
        grid.setAlignment(Pos.CENTER);

        String[] denominaciones = {
            "0.10", "0.20", "0.50",
            "1.00", "2.00", "5.00",
            "10.00", "20.00", "50.00",
            "100.00", "200.00"
        };

        String[] fieldIds = {
            "diezCentimos", "veinteCentimos", "cincuentaCentimos",
            "unSol", "dosSoles", "cincoSoles",
            "diezSoles", "veinteSoles", "cincuentaSoles",
            "cienSoles", "doscientosSoles"
        };

        // Configurar columnas para 2 grupos de label+field (2 columnas de denominaciones)
        for (int col = 0; col < 4; col++) { // 2 grupos de 2 columnas (label + field)
            ColumnConstraints colConstraints = new ColumnConstraints();
            if (col % 2 == 0) { // Columnas de labels
                colConstraints.setMinWidth(100);
                colConstraints.setPrefWidth(120);
                colConstraints.setHalignment(HPos.RIGHT);
            } else { // Columnas de fields
                colConstraints.setMinWidth(80);
                colConstraints.setPrefWidth(90);
                colConstraints.setHalignment(HPos.LEFT);
            }
            grid.getColumnConstraints().add(colConstraints);
        }

        // Configurar filas (ahora necesitamos 6 filas para 11 denominaciones en 2 columnas)
        for (int row = 0; row < 6; row++) {
            RowConstraints rowConstraints = new RowConstraints();
            rowConstraints.setMinHeight(45);
            rowConstraints.setPrefHeight(45);
            rowConstraints.setVgrow(Priority.NEVER);
            grid.getRowConstraints().add(rowConstraints);
        }

        for (int i = 0; i < denominaciones.length; i++) {
            Label label = new Label("S/ " + denominaciones[i] + ":");
            label.getStyleClass().add("cobro-date-label");
            label.setMinWidth(100);
            label.setAlignment(Pos.CENTER_RIGHT);

            TextField field = new TextField();
            field.setPromptText("0");
            field.setPrefWidth(80);
            field.setMaxWidth(80);
            field.setId(fieldIds[i]);
            field.setAlignment(Pos.CENTER);

            // Solo permitir números
            field.textProperty().addListener((obs, oldText, newText) -> {
                if (!newText.matches("\\d*")) {
                    field.setText(newText.replaceAll("[^\\d]", ""));
                }
            });

            // Calcular posición: 2 columnas de denominaciones
            int row = i / 2;
            int col = (i % 2) * 2; // 0 o 2 (primera o segunda columna de denominaciones)

            grid.add(label, col, row);
            grid.add(field, col + 1, row);
        }

        return grid;
    }

    /**
     * Obtiene un valor Integer de un campo en el GridPane
     */
    private Integer getIntegerFromField(GridPane grid, String fieldId) {
        TextField field = (TextField) grid.lookup("#" + fieldId);
        if (field != null && !field.getText().trim().isEmpty()) {
            try {
                return Integer.parseInt(field.getText().trim());
            } catch (NumberFormatException e) {
                log.warn("Error al parsear campo {}: {}", fieldId, field.getText());
            }
        }
        return null;
    }

    /**
     * Aplica estilos modernos a un diálogo.
     */
    private void applyDialogStyles(Dialog<?> dialog) {
        applyDialogStyles(dialog, "default");
    }

    /**
     * Aplica estilos modernos a un diálogo con tipo específico.
     */
    private void applyDialogStyles(Dialog<?> dialog, String dialogType) {
        // Hacer el diálogo sin decoraciones para un diseño moderno
        dialog.initStyle(StageStyle.UNDECORATED);

        // Configurar tamaño según el tipo de diálogo
        configureDialogSize(dialog, dialogType);

        // Aplicar estilos CSS
        dialog.getDialogPane().getStylesheets().addAll(
                getClass().getResource(STYLESHEET_PATH).toExternalForm(),
                getClass().getResource(SEARCH_PRODUCT_STYLESHEET_PATH).toExternalForm(),
                getClass().getResource(CAJA_STYLESHEET_PATH).toExternalForm()
        );

        // Configurar el Stage cuando se muestre el diálogo
        dialog.setOnShowing(e -> configureDialogStage(dialog, dialogType));

        // Hacer el diálogo arrastrable
        makeDraggable(dialog);
    }

    /**
     * Configura el tamaño del diálogo según su tipo
     */
    private void configureDialogSize(Dialog<?> dialog, String dialogType) {
        switch (dialogType) {
            case "efectivo" -> {
                // Diálogo de efectivo: más alto para las denominaciones
                if (dialog.getDialogPane().getPrefWidth() == Region.USE_COMPUTED_SIZE) {
                    dialog.getDialogPane().setMinWidth(600);
                    dialog.getDialogPane().setPrefWidth(600);
                }
                dialog.getDialogPane().setMinHeight(500);
                dialog.getDialogPane().setPrefHeight(550);
            }
            case "digital" -> {
                // Diálogo digital: más compacto
                dialog.getDialogPane().setMinWidth(450);
                dialog.getDialogPane().setPrefWidth(500);
                dialog.getDialogPane().setMinHeight(250);
                dialog.getDialogPane().setPrefHeight(300);
            }
            default -> {
                // Tamaño por defecto
                if (dialog.getDialogPane().getPrefWidth() == Region.USE_COMPUTED_SIZE) {
                    dialog.getDialogPane().setMinWidth(500);
                    dialog.getDialogPane().setPrefWidth(600);
                }
            }
        }
    }

    /**
     * Configura el Stage del diálogo para asegurar el tamaño correcto
     */
    private void configureDialogStage(Dialog<?> dialog, String dialogType) {
        Platform.runLater(() -> {
            if (dialog.getDialogPane().getScene() != null &&
                dialog.getDialogPane().getScene().getWindow() instanceof Stage stage) {

                // Configurar tamaño según el tipo de diálogo
                switch (dialogType) {
                    case "efectivo" -> {
                        stage.setMinWidth(600);
                        stage.setMinHeight(550);
                        stage.setWidth(650);
                        stage.setHeight(600);
                    }
                    case "digital" -> {
                        stage.setMinWidth(450);
                        stage.setMinHeight(250);
                        stage.setWidth(500);
                        stage.setHeight(300);
                    }
                    default -> {
                        stage.setMinWidth(500);
                        stage.setMinHeight(400);

                        // Si el contenido es más ancho, ajustar el Stage
                        double contentWidth = dialog.getDialogPane().getPrefWidth();
                        if (contentWidth > 500) {
                            stage.setWidth(contentWidth + 50);
                        }
                    }
                }

                // Centrar el diálogo en la pantalla
                stage.centerOnScreen();

                log.debug("Stage configurado [{}] - Width: {}, Height: {}",
                    dialogType, stage.getWidth(), stage.getHeight());
            }
        });
    }

    /**
     * Configura un icono para el diálogo.
     */
    private void setDialogIcon(Dialog<?> dialog, String iconClass) {
        // Implementación básica - se puede expandir con iconos reales
        log.debug("Configurando icono {} para diálogo", iconClass);
    }

    /**
     * Hace que el diálogo sea arrastrable.
     */
    private void makeDraggable(Dialog<?> dialog) {
        final double[] xOffset = {0};
        final double[] yOffset = {0};

        dialog.getDialogPane().setOnMousePressed(event -> {
            xOffset[0] = event.getSceneX();
            yOffset[0] = event.getSceneY();
        });

        dialog.getDialogPane().setOnMouseDragged(event -> {
            dialog.setX(event.getScreenX() - xOffset[0]);
            dialog.setY(event.getScreenY() - yOffset[0]);
        });
    }

    // Records para encapsular datos de los diálogos

    /**
     * Resultado de la selección de caja
     */
    public record CajaSelectionResult(CajaGui selectedCajaGui, boolean createNew) {
        public boolean isCreateNew() {
            return createNew;
        }
    }

    /**
     * Datos para crear una nueva CajaGui
     */
    public record CreateCajaData(String nombreCaja, String guiConfig) {}

    /**
     * Datos para inicializar CajaDineroEfectivo
     */
    public record InitializeCajaDineroEfectivoData(
        UUID cajaGuiId,
        String nombre,
        Double montoInicialEfectivo,
        Integer diezCentimos,
        Integer veinteCentimos,
        Integer cincuentaCentimos,
        Integer unSol,
        Integer dosSoles,
        Integer cincoSoles,
        Integer diezSoles,
        Integer veinteSoles,
        Integer cincuentaSoles,
        Integer cienSoles,
        Integer doscientosSoles
    ) {}

    /**
     * Datos para inicializar CajaDineroDigital
     */
    public record InitializeCajaDineroDigitalData(
        UUID cajaGuiId,
        String cuentaDigitalAsignada,
        Double montoInicialDigital
    ) {}

    /**
     * Datos para cerrar CajaDineroEfectivo
     */
    public record CloseCajaDineroEfectivoData(
        UUID cajaGuiId,
        UUID cajaDineroEfectivoId,
        Integer cierreDiezCentimos,
        Integer cierreVeinteCentimos,
        Integer cierreCincuentaCentimos,
        Integer cierreUnSol,
        Integer cierreDosSoles,
        Integer cierreCincoSoles,
        Integer cierreDiezSoles,
        Integer cierreVeinteSoles,
        Integer cierreCincuentaSoles,
        Integer cierreCienSoles,
        Integer cierreDoscientosSoles
    ) {}

    /**
     * Datos para cerrar CajaDineroDigital
     */
    public record CloseCajaDineroDigitalData(
        UUID cajaGuiId,
        UUID cajaDineroDigitalId,
        Double cierreDigitalDeclarado
    ) {}
}