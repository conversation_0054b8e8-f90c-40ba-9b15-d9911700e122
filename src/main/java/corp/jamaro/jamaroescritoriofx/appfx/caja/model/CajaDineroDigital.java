package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroEntrada;
import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.DineroSalida;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CajaDineroDigital {
    private UUID id;

    private String cuentaDigitalAsignada;//detalles de la cuenta digital asignada, número de cuenta, etc.

    //inicio
    private User abiertaPor;//usuario que abre la caja
    private Instant abiertaEl;
    private Double montoInicialDigital; // en soles

    //Cierre digital
    private User cerradaPor;//usuario que cierra la caja
    private Instant cerradaEl;

    private Double cierreDigitalCalculado; // totalEntradasDigital - totalSalidasDigital + montoInicialDigital
    private Double cierreDigitalDeclarado; // lo que el User que cierra caja informa
    private Double diferenciaCierreDigital;// cierreDigitalCalculado - cierreDigitalDeclarado

    //CAMPOS PARA CALCULO
    private Double totalEntradasDigital;//Suma de DineroEntrada.montoEntrada de todos los DineroEntrada con tipoDeDinero = DIGITAL
    private Double totalSalidasDigital;//Suma de DineroSalida.montoSalida de todos los DineroSalida con tipoDeDinero = DIGITAL

    //Datos para estadísticas extra de interés que no se usan pal cálculo de cierre, pero si para estadísticas.
    private Double pagosVentaContadoDigital;
    private Double pagosVentaCreditoDigital;
    private Double pagosVentaPedidoDigital;
    private Double devolucionesVentaDigital;
    private Double ingresosExtraDigital;
    private Double gastosExtraDigital;
    private Double pagosDineroProgramadoDigital;

    private Set<DineroEntrada> entradasDigital;

    private Set<DineroSalida> salidasDigital;

    private Boolean estaCerrada;
}