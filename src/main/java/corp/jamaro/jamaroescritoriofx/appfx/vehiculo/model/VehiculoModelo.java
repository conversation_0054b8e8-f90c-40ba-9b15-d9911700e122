package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoModelo {
    private UUID id;
    private String modelo; // Toyota Probox, Nissan Sunny
    private Instant creadoActualizado;

    // Custom setter to convert to uppercase
    public void setModelo(String modelo) {
        this.modelo = modelo != null ? modelo.toUpperCase() : null;
    }

    @Override
    public String toString() {
        return modelo != null ? modelo.toUpperCase() : "";
    }
}
