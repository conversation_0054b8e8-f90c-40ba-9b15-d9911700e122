package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoMotor {
    private UUID id;
    private String motor;//4e, 5e, 3l

    private String tipoMotor;//gasolinero, petrolero, etc.

    private Instant creadoActualizado;

    // Custom setter to convert to uppercase
    public void setMotor(String motor) {
        this.motor = motor != null ? motor.toUpperCase() : null;
    }

    // Custom setter to convert to uppercase
    public void setTipoMotor(String tipoMotor) {
        this.tipoMotor = tipoMotor != null ? tipoMotor.toUpperCase() : null;
    }

    @Override
    public String toString() {
        String motorUpper = motor != null ? motor.toUpperCase() : "";
        String tipoMotorUpper = tipoMotor != null ? tipoMotor.toUpperCase() : null;
        return motorUpper + (tipoMotorUpper != null ? " (" + tipoMotorUpper + ")" : "");
    }
}
