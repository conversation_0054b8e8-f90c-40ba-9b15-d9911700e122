package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.beans.property.ReadOnlyObjectWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.SearchableComboBox;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.kordamp.ikonli.javafx.FontIcon;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class VehiculoMantenimientoController extends BaseController {

    private final VehiculoService vehiculoService;

    // FXML Components
    @FXML private CustomTextField txtBuscarVehiculo;
    @FXML private ProgressIndicator progressIndicator;
    @FXML private TextField txtId;
    @FXML private SearchableComboBox<VehiculoMarca> cmbMarca;
    @FXML private SearchableComboBox<VehiculoModelo> cmbModelo;
    @FXML private SearchableComboBox<VehiculoMotor> cmbMotor;
    @FXML private CustomTextField txtCilindrada;
    @FXML private CustomTextField txtVersion;
    @FXML private CustomTextField txtCarroceria;
    @FXML private CustomTextField txtTipoTraccion;
    @FXML private CustomTextField txtTransmision;
    @FXML private Label lblNombrePrincipal;
    @FXML private Button btnNuevo;
    @FXML private Button btnGuardar;
    @FXML private Button btnCancelar;
    @FXML private Button btnAgregarNombre;
    @FXML private Button btnAgregarAnio;
    @FXML private Button btnAgregarArchivo;
    @FXML private Button btnNuevaMarca;
    @FXML private Button btnNuevoModelo;
    @FXML private Button btnNuevoMotor;
    @FXML private TableView<VehiculoNombre> tblNombres;
    @FXML private TableColumn<VehiculoNombre, String> colNombreTexto;
    @FXML private TableColumn<VehiculoNombre, Boolean> colNombrePrincipal;
    @FXML private TableColumn<VehiculoNombre, Void> colNombreAcciones;
    @FXML private FlowPane flowPaneAnios;
    @FXML private ScrollPane imageCarouselScrollPane;
    @FXML private HBox imageCarouselContainer;

    // State variables
    private Vehiculo currentVehiculo;
    private boolean isCreatingNew = false;
    private AutoCompletionBinding<VehiculoNombre> autoCompletionBinding;
    private AutoCompletionBinding<String> cilindradaAutoCompletion;
    private AutoCompletionBinding<String> versionAutoCompletion;
    private AutoCompletionBinding<String> carroceriaAutoCompletion;
    private AutoCompletionBinding<String> tipoTraccionAutoCompletion;
    private AutoCompletionBinding<String> transmisionAutoCompletion;
    private ObservableList<VehiculoNombre> nombresData = FXCollections.observableArrayList();
    private ObservableList<VehiculoAnio> aniosData = FXCollections.observableArrayList();
    private ObservableList<ToBucketFileRelation> archivosData = FXCollections.observableArrayList();

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        setupTables();
        setupEventHandlers();
        setupInitialState();
        loadComboBoxData();
    }

    private void setupTables() {
        setupNombresTable();
        setupAniosFlowPane();
        setupImageCarousel();
    }

    private void setupNombresTable() {
        // Configure columns
        colNombreTexto.setCellValueFactory(new PropertyValueFactory<>("nombre"));
        colNombreTexto.setCellFactory(TextFieldTableCell.forTableColumn());
        colNombreTexto.setOnEditCommit(event -> {
            VehiculoNombre nombre = event.getRowValue();
            nombre.setNombre(event.getNewValue());

            // If this is the principal nombre, update the label
            if (Boolean.TRUE.equals(nombre.getIsPrincipal())) {
                lblNombrePrincipal.setText(event.getNewValue());
            }
        });

        colNombrePrincipal.setCellValueFactory(cellData -> 
            new ReadOnlyObjectWrapper<>(cellData.getValue().getIsPrincipal()));
        colNombrePrincipal.setCellFactory(column -> new TableCell<VehiculoNombre, Boolean>() {
            private final RadioButton radioButton = new RadioButton();
            private final ToggleGroup toggleGroup = new ToggleGroup();

            {
                radioButton.setToggleGroup(toggleGroup);
                radioButton.setOnAction(e -> {
                    VehiculoNombre item = getTableView().getItems().get(getIndex());
                    if (radioButton.isSelected()) {
                        setPrincipalNombre(item);
                    }
                });
            }

            @Override
            protected void updateItem(Boolean item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setGraphic(null);
                } else {
                    radioButton.setSelected(item);
                    setGraphic(radioButton);
                    setAlignment(Pos.CENTER);
                }
            }
        });

        colNombreAcciones.setCellFactory(column -> new TableCell<VehiculoNombre, Void>() {
            private final Button deleteButton = new Button();

            {
                deleteButton.setGraphic(new FontIcon("fas-trash"));
                deleteButton.getStyleClass().addAll("button", "danger-button");
                deleteButton.setOnAction(e -> {
                    VehiculoNombre item = getTableView().getItems().get(getIndex());
                    deleteNombre(item);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, deleteButton);
                    buttons.setAlignment(Pos.CENTER);
                    setGraphic(buttons);
                }
            }
        });

        tblNombres.setItems(nombresData);
    }

    private void setupAniosFlowPane() {
        // Initialize FlowPane for years
        flowPaneAnios.getChildren().clear();
        refreshAniosDisplay();
    }

    private void refreshAniosDisplay() {
        flowPaneAnios.getChildren().clear();

        // Sort years before displaying
        List<VehiculoAnio> sortedAnios = aniosData.stream()
                .sorted(Comparator.comparing(VehiculoAnio::getYear))
                .collect(Collectors.toList());

        for (VehiculoAnio anio : sortedAnios) {
            HBox yearChip = createYearChip(anio);
            flowPaneAnios.getChildren().add(yearChip);
        }
    }

    private HBox createYearChip(VehiculoAnio anio) {
        // Create the year label
        Label yearLabel = new Label(anio.getYear().toString());
        yearLabel.getStyleClass().addAll("button", "secondary-button");
        yearLabel.setStyle("-fx-padding: 5 10 5 10;");

        // Create the delete button with X icon
        Button deleteButton = new Button();
        deleteButton.setGraphic(new FontIcon("fas-times"));
        deleteButton.getStyleClass().addAll("button", "danger-button");
        deleteButton.setStyle("-fx-padding: 2 5 2 5; -fx-font-size: 10px;");
        deleteButton.setOnAction(e -> {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("Confirmar eliminación");
            alert.setHeaderText("¿Está seguro de eliminar este año?");
            alert.setContentText("Año: " + anio.getYear());
            alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                aniosData.remove(anio);
                refreshAniosDisplay();
            }
        });

        // Create HBox container
        HBox yearChip = new HBox(5);
        yearChip.setAlignment(Pos.CENTER);
        yearChip.getChildren().addAll(yearLabel, deleteButton);
        yearChip.setStyle("-fx-background-color: transparent;");

        return yearChip;
    }

    private void setupImageCarousel() {
        // Initialize the image carousel container
        imageCarouselContainer.getChildren().clear();
        refreshImageCarousel();
    }

    private void refreshImageCarousel() {
        imageCarouselContainer.getChildren().clear();

        // Sort images by order before displaying
        List<ToBucketFileRelation> sortedArchivos = archivosData.stream()
                .sorted(Comparator.comparing(ToBucketFileRelation::getOrden))
                .collect(Collectors.toList());

        for (ToBucketFileRelation archivo : sortedArchivos) {
            VBox imageCard = createImageCard(archivo);
            imageCarouselContainer.getChildren().add(imageCard);
        }
    }

    private VBox createImageCard(ToBucketFileRelation archivo) {
        VBox imageCard = new VBox(5);
        imageCard.setAlignment(Pos.CENTER);
        imageCard.getStyleClass().add("image-card");
        imageCard.setStyle("-fx-border-color: #ddd; -fx-border-width: 1; -fx-padding: 10; -fx-background-color: #f9f9f9;");
        imageCard.setPrefWidth(150);
        imageCard.setPrefHeight(180);

        // Placeholder for image (will be replaced with actual image when service is implemented)
        Label imagePlaceholder = new Label();
        imagePlaceholder.setGraphic(new FontIcon("fas-image"));
        imagePlaceholder.getStyleClass().add("image-placeholder");
        imagePlaceholder.setStyle("-fx-font-size: 48px; -fx-text-fill: #ccc;");
        imagePlaceholder.setPrefWidth(120);
        imagePlaceholder.setPrefHeight(120);
        imagePlaceholder.setAlignment(Pos.CENTER);

        // File type label
        Label fileTypeLabel = new Label(archivo.getFileType() != null ? archivo.getFileType() : "Imagen");
        fileTypeLabel.getStyleClass().add("file-type-label");
        fileTypeLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666;");

        // Thumbnail indicator
        Label thumbnailIndicator = new Label();
        if (Boolean.TRUE.equals(archivo.getIsThumbnail())) {
            thumbnailIndicator.setText("★ Principal");
            thumbnailIndicator.setStyle("-fx-font-size: 10px; -fx-text-fill: #ff6b35;");
        } else {
            thumbnailIndicator.setText("");
        }

        // Action buttons
        HBox actionButtons = new HBox(5);
        actionButtons.setAlignment(Pos.CENTER);

        Button viewButton = new Button();
        viewButton.setGraphic(new FontIcon("fas-eye"));
        viewButton.getStyleClass().addAll("button", "info-button");
        viewButton.setStyle("-fx-font-size: 10px; -fx-padding: 2 5 2 5;");
        viewButton.setOnAction(e -> {
            // TODO: Implement view image functionality
            log.info("Ver imagen - funcionalidad pendiente");
        });

        Button deleteButton = new Button();
        deleteButton.setGraphic(new FontIcon("fas-trash"));
        deleteButton.getStyleClass().addAll("button", "danger-button");
        deleteButton.setStyle("-fx-font-size: 10px; -fx-padding: 2 5 2 5;");
        deleteButton.setOnAction(e -> deleteArchivo(archivo));

        actionButtons.getChildren().addAll(viewButton, deleteButton);

        imageCard.getChildren().addAll(imagePlaceholder, fileTypeLabel, thumbnailIndicator, actionButtons);

        return imageCard;
    }

    private void setupEventHandlers() {
        setupSearchAutoCompletion();
        setupButtonHandlers();
        setupComboBoxHandlers();
        setupFieldAutoCompletion();
    }

    private void setupSearchAutoCompletion() {
        autoCompletionBinding = TextFields.bindAutoCompletion(txtBuscarVehiculo, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            progressIndicator.setVisible(true);

            List<VehiculoNombre> suggestions = vehiculoService.buscarNombresVehiculoPorRegex(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            Platform.runLater(() -> progressIndicator.setVisible(false));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        autoCompletionBinding.setOnAutoCompleted(event -> {
            VehiculoNombre selectedNombre = event.getCompletion();
            if (selectedNombre != null && selectedNombre.getId() != null) {
                loadVehiculoByNombreId(selectedNombre.getId());
                txtBuscarVehiculo.clear();
            }
        });
    }

    private void setupButtonHandlers() {
        btnNuevo.setOnAction(e -> startCreatingNew());
        btnGuardar.setOnAction(e -> saveVehiculo());
        btnCancelar.setOnAction(e -> cancelOperation());
        btnAgregarNombre.setOnAction(e -> showAddNombreDialog());
        btnAgregarAnio.setOnAction(e -> showAddAnioDialog());
        btnAgregarArchivo.setOnAction(e -> showAddArchivoDialog());
        btnNuevaMarca.setOnAction(e -> showAddMarcaDialog());
        btnNuevoModelo.setOnAction(e -> showAddModeloDialog());
        btnNuevoMotor.setOnAction(e -> showAddMotorDialog());
    }

    private void setupComboBoxHandlers() {
        // Load marca data when needed
        cmbMarca.setOnShowing(e -> loadMarcas());
        cmbModelo.setOnShowing(e -> loadModelos());
        cmbMotor.setOnShowing(e -> loadMotores());
    }

    private void setupFieldAutoCompletion() {
        // Setup autocompletion for Cilindrada
        cilindradaAutoCompletion = TextFields.bindAutoCompletion(txtCilindrada, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            List<String> suggestions = vehiculoService.obtenerSugerenciasCilindrada(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        // Setup autocompletion for Version
        versionAutoCompletion = TextFields.bindAutoCompletion(txtVersion, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            List<String> suggestions = vehiculoService.obtenerSugerenciasVersion(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        // Setup autocompletion for Carroceria
        carroceriaAutoCompletion = TextFields.bindAutoCompletion(txtCarroceria, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            List<String> suggestions = vehiculoService.obtenerSugerenciasCarroceria(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        // Setup autocompletion for Tipo Traccion
        tipoTraccionAutoCompletion = TextFields.bindAutoCompletion(txtTipoTraccion, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            List<String> suggestions = vehiculoService.obtenerSugerenciasTipoTraccion(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        // Setup autocompletion for Transmision
        transmisionAutoCompletion = TextFields.bindAutoCompletion(txtTransmision, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            List<String> suggestions = vehiculoService.obtenerSugerenciasTransmision(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });
    }

    private void setupInitialState() {
        clearAll();
        enableSearchMode();
    }

    private void loadComboBoxData() {
        // Initial load of combo box data
        loadMarcas();
        loadModelos();
        loadMotores();
    }

    private void loadMarcas() {
        subscribeFluxWithUiUpdate(
            vehiculoService.obtenerTodasLasMarcas(),
            marca -> {
                if (!cmbMarca.getItems().contains(marca)) {
                    cmbMarca.getItems().add(marca);
                }
            },
            logError("cargando marcas")
        );
    }

    private void loadModelos() {
        subscribeFluxWithUiUpdate(
            vehiculoService.obtenerTodosLosModelos(),
            modelo -> {
                if (!cmbModelo.getItems().contains(modelo)) {
                    cmbModelo.getItems().add(modelo);
                }
            },
            logError("cargando modelos")
        );
    }

    private void loadMotores() {
        subscribeFluxWithUiUpdate(
            vehiculoService.obtenerTodosLosMotores(),
            motor -> {
                if (!cmbMotor.getItems().contains(motor)) {
                    cmbMotor.getItems().add(motor);
                }
            },
            logError("cargando motores")
        );
    }

    private void loadVehiculoByNombreId(UUID nombreVehiculoId) {
        subscribeMonoWithUiUpdate(
            vehiculoService.obtenerVehiculoPorNombreVehiculoId(nombreVehiculoId),
            this::loadVehiculoData,
            logAndShowError("cargando vehículo", this::showErrorMessage)
        );
    }

    private void loadVehiculoData(Vehiculo vehiculo) {
        this.currentVehiculo = vehiculo;
        this.isCreatingNew = false;

        // Load basic info
        txtId.setText(vehiculo.getId().toString());
        txtId.setEditable(false);
        txtId.setDisable(true);

        // Load vehicle properties
        if (vehiculo.getVehiculoMarca() != null) {
            cmbMarca.setValue(vehiculo.getVehiculoMarca());
        }
        if (vehiculo.getVehiculoModelo() != null) {
            cmbModelo.setValue(vehiculo.getVehiculoModelo());
        }
        if (vehiculo.getVehiculoMotor() != null) {
            cmbMotor.setValue(vehiculo.getVehiculoMotor());
        }

        txtCilindrada.setText(vehiculo.getCilindrada());
        txtVersion.setText(vehiculo.getVersion());
        txtCarroceria.setText(vehiculo.getCarroceria());
        txtTipoTraccion.setText(vehiculo.getTipoTraccion());
        txtTransmision.setText(vehiculo.getTransmision());

        // Load nombres
        nombresData.clear();
        if (vehiculo.getNombres() != null) {
            List<VehiculoNombre> sortedNombres = vehiculo.getNombres().stream()
                    .sorted(Comparator.comparing(VehiculoNombre::getCreadoEl))
                    .collect(Collectors.toList());
            nombresData.addAll(sortedNombres);

            // Update principal nombre label
            vehiculo.getNombres().stream()
                    .filter(n -> Boolean.TRUE.equals(n.getIsPrincipal()))
                    .findFirst()
                    .ifPresent(principal -> lblNombrePrincipal.setText(principal.getNombre()));
        }

        // Load años
        aniosData.clear();
        if (vehiculo.getVehiculoAnios() != null) {
            List<VehiculoAnio> sortedAnios = vehiculo.getVehiculoAnios().stream()
                    .sorted(Comparator.comparing(VehiculoAnio::getYear))
                    .collect(Collectors.toList());
            aniosData.addAll(sortedAnios);
        }
        refreshAniosDisplay();

        // Load archivos
        archivosData.clear();
        if (vehiculo.getFiles() != null) {
            List<ToBucketFileRelation> sortedArchivos = vehiculo.getFiles().stream()
                    .sorted(Comparator.comparing(ToBucketFileRelation::getOrden))
                    .collect(Collectors.toList());
            archivosData.addAll(sortedArchivos);
        }
        refreshImageCarousel();

        enableEditMode();
    }

    private void startCreatingNew() {
        clearAll();
        this.isCreatingNew = true;
        this.currentVehiculo = new Vehiculo();

        // Generate UUID for new vehiculo (not editable as per requirements)
        UUID newId = UUID.randomUUID();
        currentVehiculo.setId(newId);
        txtId.setText(newId.toString());
        txtId.setEditable(false);
        txtId.setDisable(false);

        enableCreateMode();
    }

    private void saveVehiculo() {
        if (currentVehiculo == null) {
            showErrorMessage("No hay datos para guardar");
            return;
        }

        // Validate required fields
        if (nombresData.isEmpty()) {
            showErrorMessage("Debe agregar al menos un Nombre para el Vehículo");
            return;
        }

        // Update current vehiculo with form data
        currentVehiculo.setVehiculoMarca(cmbMarca.getValue());
        currentVehiculo.setVehiculoModelo(cmbModelo.getValue());
        currentVehiculo.setVehiculoMotor(cmbMotor.getValue());

        // Convert text fields to uppercase
        String cilindrada = txtCilindrada.getText().trim();
        currentVehiculo.setCilindrada(cilindrada.isEmpty() ? null : cilindrada.toUpperCase());

        String version = txtVersion.getText().trim();
        currentVehiculo.setVersion(version.isEmpty() ? null : version.toUpperCase());

        String carroceria = txtCarroceria.getText().trim();
        currentVehiculo.setCarroceria(carroceria.isEmpty() ? null : carroceria.toUpperCase());

        String tipoTraccion = txtTipoTraccion.getText().trim();
        currentVehiculo.setTipoTraccion(tipoTraccion.isEmpty() ? null : tipoTraccion.toUpperCase());

        String transmision = txtTransmision.getText().trim();
        currentVehiculo.setTransmision(transmision.isEmpty() ? null : transmision.toUpperCase());
        currentVehiculo.setNombres(new HashSet<>(nombresData));
        currentVehiculo.setVehiculoAnios(new HashSet<>(aniosData));
        currentVehiculo.setFiles(new HashSet<>(archivosData));
        currentVehiculo.setCreadoActualizado(Instant.now());

        if (isCreatingNew) {
            subscribeMonoWithUiUpdate(
                vehiculoService.crearVehiculo(currentVehiculo),
                this::onSaveSuccess,
                logAndShowError("creando vehículo", this::showErrorMessage)
            );
        } else {
            subscribeMonoWithUiUpdate(
                vehiculoService.actualizarVehiculo(currentVehiculo),
                this::onSaveSuccess,
                logAndShowError("actualizando vehículo", this::showErrorMessage)
            );
        }
    }

    private void onSaveSuccess(Vehiculo savedVehiculo) {
        log.info("Vehículo guardado exitosamente: {}", savedVehiculo.getId());
        showSuccessMessage("Vehículo guardado exitosamente");
        
        // Call the callback if it exists (for agnostic method pattern)
        if (onVehiculoCreatedCallback != null) {
            onVehiculoCreatedCallback.accept(savedVehiculo);
            onVehiculoCreatedCallback = null; // Clear callback after use
        }
        
        // Clear form after successful save
        cancelOperation();
    }

    private void cancelOperation() {
        clearAll();
        enableSearchMode();
    }

    private void showAddNombreDialog() {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Agregar Nombre");
        dialog.setHeaderText("Ingrese un nuevo nombre para el vehículo");
        dialog.setContentText("Nombre:");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(nombre -> {
            if (!nombre.trim().isEmpty()) {
                VehiculoNombre nuevoNombre = new VehiculoNombre();
                nuevoNombre.setId(UUID.randomUUID());
                // Convert to camel case
                nuevoNombre.setNombre(toCamelCase(nombre.trim()));
                nuevoNombre.setIsPrincipal(nombresData.isEmpty()); // First one is principal
                nuevoNombre.setCreadoEl(Instant.now());

                nombresData.add(nuevoNombre);

                if (nuevoNombre.getIsPrincipal()) {
                    lblNombrePrincipal.setText(nuevoNombre.getNombre());
                }
            }
        });
    }

    private void showAddAnioDialog() {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Agregar Año(s)");
        dialog.setHeaderText("Agregar años al vehículo");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        // Create form
        VBox content = new VBox(10);

        RadioButton rbSingleYear = new RadioButton("Año individual");
        RadioButton rbYearRange = new RadioButton("Rango de años");
        ToggleGroup toggleGroup = new ToggleGroup();
        rbSingleYear.setToggleGroup(toggleGroup);
        rbYearRange.setToggleGroup(toggleGroup);
        rbSingleYear.setSelected(true);

        TextField txtSingleYear = new TextField();
        txtSingleYear.setPromptText("Ej: 2020");

        HBox rangeBox = new HBox(10);
        TextField txtFromYear = new TextField();
        txtFromYear.setPromptText("Desde");
        txtFromYear.setPrefWidth(80);
        Label lblTo = new Label("al");
        TextField txtToYear = new TextField();
        txtToYear.setPromptText("Hasta");
        txtToYear.setPrefWidth(80);
        rangeBox.getChildren().addAll(txtFromYear, lblTo, txtToYear);
        rangeBox.setDisable(true);

        // Toggle between single year and range
        rbSingleYear.setOnAction(e -> {
            txtSingleYear.setDisable(false);
            rangeBox.setDisable(true);
        });

        rbYearRange.setOnAction(e -> {
            txtSingleYear.setDisable(true);
            rangeBox.setDisable(false);
        });

        content.getChildren().addAll(
            rbSingleYear, txtSingleYear,
            rbYearRange, rangeBox
        );

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.setResultConverter(buttonType -> {
            if (buttonType == ButtonType.OK) {
                if (rbSingleYear.isSelected()) {
                    return txtSingleYear.getText().trim();
                } else {
                    return txtFromYear.getText().trim() + "-" + txtToYear.getText().trim();
                }
            }
            return null;
        });

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(this::processYearInput);
    }

    private void processYearInput(String input) {
        try {
            int currentYear = java.time.Year.now().getValue();

            if (input.contains("-")) {
                // Range of years
                String[] parts = input.split("-");
                if (parts.length != 2) {
                    showErrorMessage("Formato de rango inválido. Use: año1-año2");
                    return;
                }

                int fromYear = Integer.parseInt(parts[0].trim());
                int toYear = Integer.parseInt(parts[1].trim());

                if (fromYear > toYear) {
                    showErrorMessage("El año inicial debe ser menor o igual al año final");
                    return;
                }

                if (fromYear < 1900 || toYear > currentYear + 1) {
                    showErrorMessage("Los años deben estar entre 1900 y " + (currentYear + 1));
                    return;
                }

                // Add all years in range
                for (int year = fromYear; year <= toYear; year++) {
                    final int currentYearInLoop = year;
                    // Check if year already exists
                    boolean exists = aniosData.stream()
                            .anyMatch(anio -> anio.getYear().equals(currentYearInLoop));

                    if (!exists) {
                        VehiculoAnio nuevoAnio = new VehiculoAnio();
                        nuevoAnio.setYear(currentYearInLoop);
                        aniosData.add(nuevoAnio);
                    }
                }

                sortAniosByYear();

            } else {
                // Single year
                int year = Integer.parseInt(input);
                if (year < 1900 || year > currentYear + 1) {
                    showErrorMessage("El año debe estar entre 1900 y " + (currentYear + 1));
                    return;
                }

                // Check if year already exists
                boolean exists = aniosData.stream()
                        .anyMatch(anio -> anio.getYear().equals(year));

                if (exists) {
                    showErrorMessage("El año " + year + " ya existe");
                    return;
                }

                VehiculoAnio nuevoAnio = new VehiculoAnio();
                nuevoAnio.setYear(year);
                aniosData.add(nuevoAnio);
                sortAniosByYear();
            }

        } catch (NumberFormatException e) {
            showErrorMessage("Por favor ingrese años válidos");
        }
    }

    private void showAddArchivoDialog() {
        Dialog<ToBucketFileRelation> dialog = new Dialog<>();
        dialog.setTitle("Agregar Archivo");
        dialog.setHeaderText("Agregar un nuevo archivo");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        // Create form
        TextField tipoField = new TextField();
        tipoField.setPromptText("Tipo de archivo (imagen, video, pdf, etc.)");

        CheckBox thumbnailCheck = new CheckBox("Es miniatura");

        dialog.getDialogPane().setContent(new VBox(10, 
            new Label("Tipo de Archivo:"), tipoField,
            thumbnailCheck));

        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.setResultConverter(buttonType -> {
            if (buttonType == ButtonType.OK && !tipoField.getText().trim().isEmpty()) {
                ToBucketFileRelation archivo = new ToBucketFileRelation();
                archivo.setId(UUID.randomUUID().toString());
                archivo.setFileType(tipoField.getText().trim());
                archivo.setIsThumbnail(thumbnailCheck.isSelected());
                archivo.setOrden(archivosData.size() + 1);
                // TODO: Set bucketFile when file upload is implemented
                return archivo;
            }
            return null;
        });

        Optional<ToBucketFileRelation> result = dialog.showAndWait();
        result.ifPresent(archivo -> {
            archivosData.add(archivo);
            sortArchivosByOrden();
        });
    }

    private void showAddMarcaDialog() {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Agregar Marca");
        dialog.setHeaderText("Ingrese una nueva marca de vehículo");
        dialog.setContentText("Marca:");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(marcaText -> {
            if (!marcaText.trim().isEmpty()) {
                VehiculoMarca nuevaMarca = new VehiculoMarca();
                // No asignamos UUID ya que el servidor lo hará cuando mandemos el Vehiculo completo
                // The setter automatically converts to uppercase
                nuevaMarca.setMarca(marcaText.trim());
                nuevaMarca.setCreadoActualizado(java.time.Instant.now());

                cmbMarca.getItems().add(nuevaMarca);
                cmbMarca.setValue(nuevaMarca);
            }
        });
    }

    private void showAddModeloDialog() {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Agregar Modelo");
        dialog.setHeaderText("Ingrese un nuevo modelo de vehículo");
        dialog.setContentText("Modelo:");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(modeloText -> {
            if (!modeloText.trim().isEmpty()) {
                VehiculoModelo nuevoModelo = new VehiculoModelo();
                // No asignamos UUID ya que el servidor lo hará cuando mandemos el Vehiculo completo
                // The setter automatically converts to uppercase
                nuevoModelo.setModelo(modeloText.trim());
                nuevoModelo.setCreadoActualizado(java.time.Instant.now());

                cmbModelo.getItems().add(nuevoModelo);
                cmbModelo.setValue(nuevoModelo);
            }
        });
    }

    private void showAddMotorDialog() {
        Dialog<VehiculoMotor> dialog = new Dialog<>();
        dialog.setTitle("Agregar Motor");
        dialog.setHeaderText("Agregar un nuevo motor");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        // Create form
        TextField motorField = new TextField();
        motorField.setPromptText("Motor (ej: 4e, 5e, 3l)");

        TextField tipoMotorField = new TextField();
        tipoMotorField.setPromptText("Tipo de motor (ej: gasolinero, petrolero)");

        dialog.getDialogPane().setContent(new VBox(10, 
            new Label("Motor:"), motorField,
            new Label("Tipo de Motor:"), tipoMotorField));

        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.setResultConverter(buttonType -> {
            if (buttonType == ButtonType.OK && !motorField.getText().trim().isEmpty()) {
                VehiculoMotor nuevoMotor = new VehiculoMotor();
                // No asignamos UUID ya que el servidor lo hará cuando mandemos el Vehiculo completo
                // The setters automatically convert to uppercase
                nuevoMotor.setMotor(motorField.getText().trim());
                nuevoMotor.setTipoMotor(tipoMotorField.getText().trim().isEmpty() ? null : tipoMotorField.getText().trim());
                nuevoMotor.setCreadoActualizado(java.time.Instant.now());
                return nuevoMotor;
            }
            return null;
        });

        Optional<VehiculoMotor> result = dialog.showAndWait();
        result.ifPresent(motor -> {
            cmbMotor.getItems().add(motor);
            cmbMotor.setValue(motor);
        });
    }

    private void setPrincipalNombre(VehiculoNombre selectedNombre) {
        // Set all to false first
        nombresData.forEach(nombre -> nombre.setIsPrincipal(false));
        // Set selected as principal
        selectedNombre.setIsPrincipal(true);
        // Update label
        lblNombrePrincipal.setText(selectedNombre.getNombre());
        // Refresh table
        tblNombres.refresh();
    }

    private void deleteNombre(VehiculoNombre nombre) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmar eliminación");
        alert.setHeaderText("¿Está seguro de eliminar este nombre?");
        alert.setContentText("Nombre: " + nombre.getNombre());

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            nombresData.remove(nombre);

            // If it was principal, set first one as principal
            if (Boolean.TRUE.equals(nombre.getIsPrincipal()) && !nombresData.isEmpty()) {
                VehiculoNombre firstNombre = nombresData.get(0);
                firstNombre.setIsPrincipal(true);
                lblNombrePrincipal.setText(firstNombre.getNombre());
                tblNombres.refresh();
            } else if (nombresData.isEmpty()) {
                lblNombrePrincipal.setText("");
            }
        }
    }


    private void deleteArchivo(ToBucketFileRelation archivo) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmar eliminación");
        alert.setHeaderText("¿Está seguro de eliminar este archivo?");
        alert.setContentText("Tipo: " + archivo.getFileType());

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            archivosData.remove(archivo);
            // Reorder remaining archivos
            for (int i = 0; i < archivosData.size(); i++) {
                archivosData.get(i).setOrden(i + 1);
            }
            refreshImageCarousel();
        }
    }

    private void sortAniosByYear() {
        FXCollections.sort(aniosData, Comparator.comparing(VehiculoAnio::getYear));
        refreshAniosDisplay();
    }

    private void sortArchivosByOrden() {
        FXCollections.sort(archivosData, Comparator.comparing(ToBucketFileRelation::getOrden));
        refreshImageCarousel();
    }

    private void clearAll() {
        currentVehiculo = null;
        isCreatingNew = false;

        txtId.clear();
        txtId.setEditable(false);
        txtId.setDisable(true);
        txtBuscarVehiculo.clear();
        lblNombrePrincipal.setText("");

        cmbMarca.setValue(null);
        cmbModelo.setValue(null);
        cmbMotor.setValue(null);
        txtCilindrada.clear();
        txtVersion.clear();
        txtCarroceria.clear();
        txtTipoTraccion.clear();
        txtTransmision.clear();

        nombresData.clear();
        aniosData.clear();
        archivosData.clear();
        refreshAniosDisplay();
        refreshImageCarousel();
    }

    private void enableSearchMode() {
        txtBuscarVehiculo.setDisable(false);
        btnNuevo.setDisable(false);
        btnGuardar.setDisable(true);
        btnCancelar.setDisable(true);
        btnAgregarNombre.setDisable(true);
        btnAgregarAnio.setDisable(true);
        btnAgregarArchivo.setDisable(true);
        btnNuevaMarca.setDisable(true);
        btnNuevoModelo.setDisable(true);
        btnNuevoMotor.setDisable(true);

        cmbMarca.setDisable(true);
        cmbModelo.setDisable(true);
        cmbMotor.setDisable(true);
        txtCilindrada.setDisable(true);
        txtVersion.setDisable(true);
        txtCarroceria.setDisable(true);
        txtTipoTraccion.setDisable(true);
        txtTransmision.setDisable(true);

        tblNombres.setDisable(true);
        flowPaneAnios.setDisable(true);
        imageCarouselScrollPane.setDisable(true);
    }

    private void enableEditMode() {
        txtBuscarVehiculo.setDisable(true);
        btnNuevo.setDisable(true);
        btnGuardar.setDisable(false);
        btnCancelar.setDisable(false);
        btnAgregarNombre.setDisable(false);
        btnAgregarAnio.setDisable(false);
        btnAgregarArchivo.setDisable(false);
        btnNuevaMarca.setDisable(false);
        btnNuevoModelo.setDisable(false);
        btnNuevoMotor.setDisable(false);

        cmbMarca.setDisable(false);
        cmbModelo.setDisable(false);
        cmbMotor.setDisable(false);
        txtCilindrada.setDisable(false);
        txtVersion.setDisable(false);
        txtCarroceria.setDisable(false);
        txtTipoTraccion.setDisable(false);
        txtTransmision.setDisable(false);

        tblNombres.setDisable(false);
        flowPaneAnios.setDisable(false);
        imageCarouselScrollPane.setDisable(false);
    }

    private void enableCreateMode() {
        enableEditMode(); // Same as edit mode for vehicles
    }

    private void showErrorMessage(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText("Ha ocurrido un error");
        alert.setContentText(message);
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        alert.showAndWait();
    }

    private void showSuccessMessage(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Éxito");
        alert.setHeaderText("Operación completada");
        alert.setContentText(message);
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        alert.showAndWait();
    }

    /**
     * Converts a string to camel case.
     * Example: "toyota corolla" -> "Toyota Corolla"
     */
    private String toCamelCase(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }

        String[] words = input.toLowerCase().split("\\s+");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            if (i > 0) {
                result.append(" ");
            }
            if (words[i].length() > 0) {
                result.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    result.append(words[i].substring(1));
                }
            }
        }

        return result.toString();
    }

    /**
     * Agnostic method to create a vehicle with a given name and callback.
     * Similar to ProductoItemSearchedController pattern.
     * When the vehicle is successfully created, the callback is invoked with the created vehicle.
     *
     * @param vehiculoName The name for the new vehicle
     * @param onVehiculoCreated Callback function to be called when vehicle is successfully created
     */
    public void createVehiculoWithName(String vehiculoName, java.util.function.Consumer<Vehiculo> onVehiculoCreated) {
        // Store the callback for later use
        this.onVehiculoCreatedCallback = onVehiculoCreated;
        
        // Start creating a new vehicle
        startCreatingNew();
        
        // Add the provided name as the first vehicle name
        if (vehiculoName != null && !vehiculoName.trim().isEmpty()) {
            VehiculoNombre nombre = new VehiculoNombre();
            nombre.setId(UUID.randomUUID());
            nombre.setNombre(toCamelCase(vehiculoName.trim()));
            nombre.setIsPrincipal(true);
            
            nombresData.add(nombre);
            
            // Update the principal name label
            lblNombrePrincipal.setText(nombre.getNombre());
        }
    }

    // Callback function to be called when vehicle is successfully created
    private java.util.function.Consumer<Vehiculo> onVehiculoCreatedCallback;
}
