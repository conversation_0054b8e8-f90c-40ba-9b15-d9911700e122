package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service;

import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.Vehiculo;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoModelo;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMotor;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class VehiculoService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_NOMBRES = "vehiculo.search.nombres";
    private static final String ROUTE_GET_BY_NOMBRE_VEHICULO = "vehiculo.get.by-nombreVehiculo";
    private static final String ROUTE_CREATE = "vehiculo.create";
    private static final String ROUTE_UPDATE = "vehiculo.update";
    private static final String ROUTE_GET_ALL_MARCAS = "vehiculo.get.all-marcas";
    private static final String ROUTE_GET_ALL_MODELOS = "vehiculo.get.all-modelos";
    private static final String ROUTE_GET_ALL_MOTORES = "vehiculo.get.all-motores";
    private static final String ROUTE_GET_CILINDRADA_SUGGESTIONS = "vehiculo.get.cilindrada-suggestions";
    private static final String ROUTE_GET_VERSION_SUGGESTIONS = "vehiculo.get.version-suggestions";
    private static final String ROUTE_GET_CARROCERIA_SUGGESTIONS = "vehiculo.get.carroceria-suggestions";
    private static final String ROUTE_GET_TIPO_TRACCION_SUGGESTIONS = "vehiculo.get.tipo-traccion-suggestions";
    private static final String ROUTE_GET_TRANSMISION_SUGGESTIONS = "vehiculo.get.transmision-suggestions";

    private final ConnectionService connectionService;

    /**
     * Busca los VehiculoNombre cuyo campo nombre coincida con una expresión regular dada.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los VehiculoNombre que cumplen el criterio.
     */
    public Flux<VehiculoNombre> buscarNombresVehiculoPorRegex(String userInput) {
        log.debug("Buscando VehiculoNombre por regex: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_NOMBRES, userInput, VehiculoNombre.class);
    }

    /**
     * Recibe el id de un VehiculoNombre y obtiene el Vehiculo con el que tiene relación.
     * Primero hace una consulta cypher simple en el repository y luego un findById para obtener el objeto completo.
     *
     * @param nombreVehiculoId UUID del VehiculoNombre.
     * @return Un Mono con el Vehiculo encontrado o vacío si no existe.
     */
    public Mono<Vehiculo> obtenerVehiculoPorNombreVehiculoId(UUID nombreVehiculoId) {
        log.debug("Obteniendo Vehiculo por VehiculoNombre id: {}", nombreVehiculoId);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_NOMBRE_VEHICULO, nombreVehiculoId, Vehiculo.class);
    }

    /**
     * Crea un nuevo Vehiculo. Recibe un Vehiculo que el cliente ya construyó 
     * con un id asignado. Comprueba que este aún no exista, si no existe 
     * procede a hacer un save.
     *
     * @param vehiculo El Vehiculo a crear.
     * @return Un Mono con el Vehiculo creado o error si ya existe.
     */
    public Mono<Vehiculo> crearVehiculo(Vehiculo vehiculo) {
        log.debug("Creando nuevo Vehiculo con id: {}", vehiculo.getId());
        return connectionService.authenticatedRequest(ROUTE_CREATE, vehiculo, Vehiculo.class);
    }

    /**
     * Actualiza un Vehiculo existente. Valida que exista.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Compara los ids de los nombres del antiguo con el nuevo y elimina los que ya no existan
     * - Compara los ids de los años y elimina los que ya no existan
     * - Compara los ids de los archivos y elimina las relaciones de los que ya no existan
     * - Luego hace el save
     *
     * @param vehiculoActualizado El Vehiculo con los datos actualizados.
     * @return Un Mono con el Vehiculo actualizado.
     */
    public Mono<Vehiculo> actualizarVehiculo(Vehiculo vehiculoActualizado) {
        log.debug("Actualizando Vehiculo con id: {}", vehiculoActualizado.getId());
        return connectionService.authenticatedRequest(ROUTE_UPDATE, vehiculoActualizado, Vehiculo.class);
    }

    /**
     * Obtiene todas las VehiculoMarca disponibles.
     *
     * @return Un Flux que emite todas las VehiculoMarca.
     */
    public Flux<VehiculoMarca> obtenerTodasLasMarcas() {
        log.debug("Obteniendo todas las VehiculoMarca");
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL_MARCAS, null, VehiculoMarca.class);
    }

    /**
     * Obtiene todos los VehiculoModelo disponibles.
     *
     * @return Un Flux que emite todos los VehiculoModelo.
     */
    public Flux<VehiculoModelo> obtenerTodosLosModelos() {
        log.debug("Obteniendo todos los VehiculoModelo");
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL_MODELOS, null, VehiculoModelo.class);
    }

    /**
     * Obtiene todos los VehiculoMotor disponibles.
     *
     * @return Un Flux que emite todos los VehiculoMotor.
     */
    public Flux<VehiculoMotor> obtenerTodosLosMotores() {
        log.debug("Obteniendo todos los VehiculoMotor");
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL_MOTORES, null, VehiculoMotor.class);
    }

    /**
     * Obtiene sugerencias de cilindrada basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de cilindrada.
     */
    public Flux<String> obtenerSugerenciasCilindrada(String userInput) {
        log.debug("Obteniendo sugerencias de cilindrada para: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_GET_CILINDRADA_SUGGESTIONS, userInput, String.class);
    }

    /**
     * Obtiene sugerencias de versión basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de versión.
     */
    public Flux<String> obtenerSugerenciasVersion(String userInput) {
        log.debug("Obteniendo sugerencias de versión para: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_GET_VERSION_SUGGESTIONS, userInput, String.class);
    }

    /**
     * Obtiene sugerencias de carrocería basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de carrocería.
     */
    public Flux<String> obtenerSugerenciasCarroceria(String userInput) {
        log.debug("Obteniendo sugerencias de carrocería para: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_GET_CARROCERIA_SUGGESTIONS, userInput, String.class);
    }

    /**
     * Obtiene sugerencias de tipo de tracción basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de tipo de tracción.
     */
    public Flux<String> obtenerSugerenciasTipoTraccion(String userInput) {
        log.debug("Obteniendo sugerencias de tipo de tracción para: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_GET_TIPO_TRACCION_SUGGESTIONS, userInput, String.class);
    }

    /**
     * Obtiene sugerencias de transmisión basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de transmisión.
     */
    public Flux<String> obtenerSugerenciasTransmision(String userInput) {
        log.debug("Obteniendo sugerencias de transmisión para: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_GET_TRANSMISION_SUGGESTIONS, userInput, String.class);
    }
}
