package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class Vehiculo {
    private UUID id;
    private Set<VehiculoNombre> nombres;
    private VehiculoMarca vehiculoMarca;
    private VehiculoModelo vehiculoModelo;
    private Set<VehiculoAnio> vehiculoAnios;
    private VehiculoMotor vehiculoMotor;

    private String cilindrada;// (e.g., 1000, 1500, 1.5, 2.0).

    private String version;//(GL, XLE, Sport, basic, full, etc.).

    private String carroceria;// (sedán, SUV, hatchback, camioneta, etc.).

    private String tipoTraccion;//Tracción delantera, trasera, 4x4, AWD, etc.

    private String transmision;//mecánica, automática, cvt, etc

    private Set<ToBucketFileRelation> files;

    private Instant creadoActualizado;

}
