package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class VehiculoMarca {
    private UUID id;
    private String marca; // Toyota, Nissan, etc.

    private Instant creadoActualizado;

    // Custom setter to convert to uppercase
    public void setMarca(String marca) {
        this.marca = marca != null ? marca.toUpperCase() : null;
    }

    @Override
    public String toString() {
        return marca != null ? marca.toUpperCase() : "";
    }
}
