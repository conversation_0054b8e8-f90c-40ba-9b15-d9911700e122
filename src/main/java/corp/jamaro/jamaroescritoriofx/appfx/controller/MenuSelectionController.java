package corp.jamaro.jamaroescritoriofx.appfx.controller;

import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controlador para la pantalla de selección de menú después del login.
 * Permite al usuario elegir entre diferentes opciones de navegación.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class MenuSelectionController extends BaseController implements Initializable {

    private final NavigationService navigationService;
    private final AlertUtil alertUtil;

    @FXML
    private Button btnUniversalSale;

    @FXML
    private Button btnCajaGui;

    @FXML
    private Button btnMantenimiento;

    private User currentUser;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.debug("Inicializando MenuSelectionController");
    }

    /**
     * Inicializa el controlador con el usuario que ha iniciado sesión.
     * @param user El usuario que ha iniciado sesión.
     */
    public void initUser(User user) {
        this.currentUser = user;
        log.debug("Usuario inicializado en MenuSelectionController: {}", user.getUsername());
    }

    /**
     * Maneja el evento de clic en el botón de Universal Sale.
     * @param event El evento de acción.
     */
    @FXML
    void handleUniversalSale(ActionEvent event) {
        log.info("Navegando a Universal Sale");
        navigateTo(FXMLEnum.UNIVERSAL_SALE);
    }

    /**
     * Maneja el evento de clic en el botón de Caja GUI.
     * @param event El evento de acción.
     */
    @FXML
    void handleCajaGui(ActionEvent event) {
        log.info("Navegando a Caja GUI");
        navigateTo(FXMLEnum.CAJA_PRINCIPAL);
    }

    /**
     * Maneja el evento de clic en el botón de Mantenimiento.
     * @param event El evento de acción.
     */
    @FXML
    void handleMantenimiento(ActionEvent event) {
        log.info("Navegando a Mantenimiento Principal");
        navigateTo(FXMLEnum.MANTENIMIENTO_PRINCIPAL);
    }

    /**
     * Método común para navegar a la vista seleccionada.
     * @param destination La vista de destino.
     */
    private void navigateTo(FXMLEnum destination) {
        navigationService.navigateTo(destination, controller -> {
                // Si es necesario, se puede pasar el usuario al controlador de destino
                // Ejemplo:
                // if (controller instanceof SomeController someCtrl) {
                //     someCtrl.initUser(currentUser);
                // }
            })
            .doOnSuccess(unused -> log.info("Navegación a {} exitosa.", destination))
            .doOnError(error -> {
                log.error("Error al navegar a {}: {}", destination, error.getMessage());
                alertUtil.showError(error);
            })
            .subscribe(); // Iniciamos la suscripción
    }
}
