package corp.jamaro.jamaroescritoriofx.appfx.controller;

import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controlador para la pantalla principal de mantenimiento.
 * Permite al usuario elegir entre diferentes tipos de mantenimiento.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class MantenimientoPrincipalController extends BaseController implements Initializable {

    private final NavigationService navigationService;
    private final AlertUtil alertUtil;

    @FXML
    private Button btnProductos;

    @FXML
    private Button btnAtributos;

    @FXML
    private Button btnGrupos;

    @FXML
    private Button btnFiltros;

    @FXML
    private Button btnCodigosFabrica;

    @FXML
    private Button btnVehiculos;

    @FXML
    private Button btnVolver;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.debug("Inicializando MantenimientoPrincipalController");
    }

    /**
     * Maneja el evento de clic en el botón de Productos.
     * @param event El evento de acción.
     */
    @FXML
    void handleProductos(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Productos");
        navigateTo(FXMLEnum.PRODUCTO_MANTENIMIENTO);
    }

    /**
     * Maneja el evento de clic en el botón de Vehículos.
     * @param event El evento de acción.
     */
    @FXML
    void handleVehiculos(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Vehículos");
        navigateTo(FXMLEnum.CREAR_VEHICULO);
    }

    /**
     * Maneja el evento de clic en el botón de Atributos.
     * @param event El evento de acción.
     */
    @FXML
    void handleAtributos(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Atributos");
        // TODO: Implementar navegación a mantenimiento de atributos
        alertUtil.showInfo("Mantenimiento de Atributos - Próximamente disponible");
    }

    /**
     * Maneja el evento de clic en el botón de Grupos.
     * @param event El evento de acción.
     */
    @FXML
    void handleGrupos(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Grupos");
        navigateTo(FXMLEnum.GRUPO_MANTENIMIENTO);
    }

    /**
     * Maneja el evento de clic en el botón de Filtros.
     * @param event El evento de acción.
     */
    @FXML
    void handleFiltros(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Filtros");
        // TODO: Implementar navegación a mantenimiento de filtros
        alertUtil.showInfo("Mantenimiento de Filtros - Próximamente disponible");
    }

    /**
     * Maneja el evento de clic en el botón de Códigos de Fábrica.
     * @param event El evento de acción.
     */
    @FXML
    void handleCodigosFabrica(ActionEvent event) {
        log.info("Navegando a Mantenimiento de Códigos de Fábrica");
        // TODO: Implementar navegación a mantenimiento de códigos de fábrica
        alertUtil.showInfo("Mantenimiento de Códigos de Fábrica - Próximamente disponible");
    }

    /**
     * Maneja el evento de clic en el botón de Volver.
     * @param event El evento de acción.
     */
    @FXML
    void handleVolver(ActionEvent event) {
        log.info("Volviendo al Menú Principal");
        navigateTo(FXMLEnum.MENU_SELECTION);
    }

    /**
     * Método común para navegar a la vista seleccionada.
     * @param destination La vista de destino.
     */
    private void navigateTo(FXMLEnum destination) {
        navigationService.navigateTo(destination, controller -> {
                // Si es necesario, se puede pasar información al controlador de destino
            })
            .doOnSuccess(unused -> log.info("Navegación a {} exitosa.", destination))
            .doOnError(error -> {
                log.error("Error al navegar a {}: {}", destination, error.getMessage());
                alertUtil.showError(error);
            })
            .subscribe(); // Iniciamos la suscripción
    }
}
