package corp.jamaro.jamaroescritoriofx.appfx.controller;

import javafx.application.Platform;
import javafx.fxml.Initializable;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.core.Disposables;

import java.time.Duration;
import java.util.function.Consumer;

/**
 * Controlador base que centraliza la lógica reactiva y de UI.
 */
@Slf4j
public abstract class BaseController implements Initializable {

    private Disposable.Composite disposables = Disposables.composite();

    /**
     * Registra una suscripción para poder limpiarla luego.
     */
    protected void registerSubscription(Disposable disposable) {
        disposables.add(disposable);
    }

    /**
     * Cancela todas las suscripciones para prevenir fugas de memoria.
     * Debe llamarse cuando la vista ya no se use.
     */
    public void onClose() {
        disposables.dispose();
        log.debug("Disposables liberados para {}", getClass().getSimpleName());
    }

    /**
     * Limpia todas las suscripciones actuales sin cerrar la vista.
     * Útil para cambios de contexto donde se necesita reinicializar suscripciones.
     */
    protected void clearSubscriptions() {
        disposables.dispose();
        disposables = Disposables.composite();
        log.debug("Suscripciones limpiadas para {}", getClass().getSimpleName());
    }

    /**
     * Ejecuta una tarea en el hilo de la UI.
     */
    protected void runOnUiThread(Runnable task) {
        Platform.runLater(task);
    }

    /**
     * Helper para suscribirse a un Mono en el scheduler boundedElastic.
     */
    protected <T> void subscribeOnBoundedElastic(
            Mono<T> mono, 
            Consumer<T> onSuccess, 
            Consumer<Throwable> onError
    ) {
        Disposable disposable = mono
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(onSuccess, onError);
        registerSubscription(disposable);
    }

    /**
     * Helper para suscribirse a un Flux en el scheduler boundedElastic.
     */
    protected <T> void subscribeFluxOnBoundedElastic(
            Flux<T> flux, 
            Consumer<T> onNext, 
            Consumer<Throwable> onError
    ) {
        Disposable disposable = flux
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(onNext, onError);
        registerSubscription(disposable);
    }

    /**
     * Helper para suscribirse a un Mono con timeout y manejo de UI.
     * Ejecuta onSuccess en el hilo de UI y maneja errores de forma consistente.
     */
    protected <T> void subscribeMonoWithUiUpdate(
            Mono<T> mono,
            Consumer<T> onSuccess,
            Consumer<Throwable> onError,
            Duration timeout
    ) {
        Disposable disposable = mono
            .timeout(timeout)
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(
                result -> runOnUiThread(() -> onSuccess.accept(result)),
                error -> {
                    log.error("Error en operación reactiva: {}", error.getMessage(), error);
                    runOnUiThread(() -> onError.accept(error));
                }
            );
        registerSubscription(disposable);
    }

    /**
     * Helper para suscribirse a un Mono con timeout y manejo de UI (timeout por defecto de 10 segundos).
     */
    protected <T> void subscribeMonoWithUiUpdate(
            Mono<T> mono,
            Consumer<T> onSuccess,
            Consumer<Throwable> onError
    ) {
        subscribeMonoWithUiUpdate(mono, onSuccess, onError, Duration.ofSeconds(10));
    }

    /**
     * Helper para suscribirse a un Flux con timeout y manejo de UI.
     */
    protected <T> void subscribeFluxWithUiUpdate(
            Flux<T> flux,
            Consumer<T> onNext,
            Consumer<Throwable> onError,
            Duration timeout
    ) {
        Disposable disposable = flux
            .timeout(timeout)
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(
                result -> runOnUiThread(() -> onNext.accept(result)),
                error -> {
                    log.error("Error en suscripción Flux: {}", error.getMessage(), error);
                    runOnUiThread(() -> onError.accept(error));
                }
            );
        registerSubscription(disposable);
    }

    /**
     * Helper para suscribirse a un Flux con timeout por defecto de 30 segundos.
     */
    protected <T> void subscribeFluxWithUiUpdate(
            Flux<T> flux,
            Consumer<T> onNext,
            Consumer<Throwable> onError
    ) {
        subscribeFluxWithUiUpdate(flux, onNext, onError, Duration.ofSeconds(30));
    }

    /**
     * Helper para suscribirse a un Flux sin timeout para suscripciones de larga duración.
     * Útil para suscripciones que pueden no emitir elementos por largos períodos.
     */
    protected <T> void subscribeFluxWithUiUpdateNoTimeout(
            Flux<T> flux,
            Consumer<T> onNext,
            Consumer<Throwable> onError
    ) {
        Disposable disposable = flux
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(
                result -> runOnUiThread(() -> onNext.accept(result)),
                error -> {
                    log.error("Error en suscripción Flux sin timeout: {}", error.getMessage(), error);
                    runOnUiThread(() -> onError.accept(error));
                }
            );
        registerSubscription(disposable);
    }

    /**
     * Helper para loguear errores de forma consistente.
     */
    protected Consumer<Throwable> logError(String context) {
        return error -> log.error("Error {}: {}", context, error.getMessage(), error);
    }

    /**
     * Helper para crear un Consumer de error que muestra un mensaje al usuario y loguea el error.
     */
    protected Consumer<Throwable> logAndShowError(String context, Consumer<String> showErrorMessage) {
        return error -> {
            log.error("Error {}: {}", context, error.getMessage(), error);
            showErrorMessage.accept(error.getMessage());
        };
    }
}
