package corp.jamaro.jamaroescritoriofx.appfx.util.components;

import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import org.kordamp.ikonli.javafx.FontIcon;

public class FXLabelUtil {
    /**
     * Crea un componente de comando que muestra dos textos con estilos distintos y un icono para eliminar.
     * El prefijo (ej. "Descripción:") se muestra con el color -fx-light-grey, mientras que el valor
     * ingresado se muestra con -fx-light-color.
     *
     * @param labelPrefix Prefijo o nombre del campo (ej. "Descripción").
     * @param value Valor ingresado a mostrar.
     * @param onRemove Acción a ejecutar al hacer clic en el icono de cerrar.
     * @return Un HBox conteniendo el prefijo, el valor y el icono de cierre.
     */
    public static HBox createStaticCommandLabel(String labelPrefix, String value, Runnable onRemove) {
        // Label para el prefijo con color gris claro
        Label prefixLabel = new Label(labelPrefix + ": ");
        prefixLabel.setStyle("-fx-text-fill: -fx-light-grey;");
        // Label para el valor ingresado con color light definido
        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-text-fill: -fx-light-color;");
        // Icono de cierre con estilo definido
        FontIcon closeIcon = new FontIcon("fas-times");
        closeIcon.setIconSize(12);
        closeIcon.getStyleClass().add("font-icon-light");
        Label closeLabel = new Label();
        closeLabel.setGraphic(closeIcon);
        closeLabel.setStyle("-fx-cursor: hand;");
        // Contenedor con espaciado y alineación
        HBox container = new HBox(5, prefixLabel, valueLabel, closeLabel);
        container.setAlignment(Pos.CENTER_LEFT);
        closeLabel.setOnMouseClicked(e -> onRemove.run());
        return container;
    }
}
