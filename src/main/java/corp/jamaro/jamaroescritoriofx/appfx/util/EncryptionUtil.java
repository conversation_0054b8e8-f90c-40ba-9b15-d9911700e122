package corp.jamaro.jamaroescritoriofx.appfx.util;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Utilidad para encriptar y desencriptar datos numéricos usando un mapeo de letras simple.
 * Mapeo: E=1, U=2, C=3, A=4, L=5, I=6, P=7, T=8, H=9, O=0
 */
public class EncryptionUtil {
    
    // Mapeo de números a letras
    private static final Map<Character, Character> NUMBER_TO_LETTER = new HashMap<>();
    // Mapeo de letras a números
    private static final Map<Character, Character> LETTER_TO_NUMBER = new HashMap<>();
    
    static {
        // Inicializar mapeos
        NUMBER_TO_LETTER.put('1', 'E');
        NUMBER_TO_LETTER.put('2', 'U');
        NUMBER_TO_LETTER.put('3', 'C');
        NUMBER_TO_LETTER.put('4', 'A');
        NUMBER_TO_LETTER.put('5', 'L');
        NUMBER_TO_LETTER.put('6', 'I');
        NUMBER_TO_LETTER.put('7', 'P');
        NUMBER_TO_LETTER.put('8', 'T');
        NUMBER_TO_LETTER.put('9', 'H');
        NUMBER_TO_LETTER.put('0', 'O');
        
        // Mapeo inverso
        LETTER_TO_NUMBER.put('E', '1');
        LETTER_TO_NUMBER.put('U', '2');
        LETTER_TO_NUMBER.put('C', '3');
        LETTER_TO_NUMBER.put('A', '4');
        LETTER_TO_NUMBER.put('L', '5');
        LETTER_TO_NUMBER.put('I', '6');
        LETTER_TO_NUMBER.put('P', '7');
        LETTER_TO_NUMBER.put('T', '8');
        LETTER_TO_NUMBER.put('H', '9');
        LETTER_TO_NUMBER.put('O', '0');
    }
    
    /**
     * Encripta un número usando el mapeo de letras.
     * Ejemplo: 12.3 -> EU.C
     * 
     * @param number el número a encriptar
     * @return el número encriptado en mayúsculas, o cadena vacía si el número es null
     */
    public static String encrypt(Double number) {
        if (number == null) {
            return "";
        }
        
        // Convertir el número a string con formato de 2 decimales usando Locale.US para asegurar punto decimal
        String numberStr = String.format(Locale.US, "%.2f", number);
        
        return encrypt(numberStr);
    }
    
    /**
     * Encripta una cadena numérica usando el mapeo de letras.
     * Ejemplo: "12.3" -> "EU.C"
     * 
     * @param numberStr la cadena numérica a encriptar
     * @return la cadena encriptada en mayúsculas, o cadena vacía si la entrada es null o vacía
     */
    public static String encrypt(String numberStr) {
        if (numberStr == null || numberStr.isEmpty()) {
            return "";
        }
        
        StringBuilder encrypted = new StringBuilder();
        
        for (char c : numberStr.toCharArray()) {
            if (NUMBER_TO_LETTER.containsKey(c)) {
                encrypted.append(NUMBER_TO_LETTER.get(c));
            } else if (c == '.') {
                encrypted.append('.');
            } else if (c == '-') {
                encrypted.append('-');
            } else {
                // Para cualquier otro carácter, mantenerlo como está
                encrypted.append(c);
            }
        }
        
        return encrypted.toString().toUpperCase();
    }
    
    /**
     * Desencripta una cadena encriptada de vuelta a números.
     * Ejemplo: "EU.C" -> "12.3"
     * 
     * @param encryptedStr la cadena encriptada a desencriptar
     * @return la cadena numérica desencriptada, o cadena vacía si la entrada es null o vacía
     */
    public static String decrypt(String encryptedStr) {
        if (encryptedStr == null || encryptedStr.isEmpty()) {
            return "";
        }
        
        StringBuilder decrypted = new StringBuilder();
        
        for (char c : encryptedStr.toUpperCase().toCharArray()) {
            if (LETTER_TO_NUMBER.containsKey(c)) {
                decrypted.append(LETTER_TO_NUMBER.get(c));
            } else if (c == '.') {
                decrypted.append('.');
            } else if (c == '-') {
                decrypted.append('-');
            } else {
                // Para cualquier otro carácter, mantenerlo como está
                decrypted.append(c);
            }
        }
        
        return decrypted.toString();
    }
    
    /**
     * Desencripta una cadena encriptada y la convierte a Double.
     * Ejemplo: "EU.C" -> 12.3
     * 
     * @param encryptedStr la cadena encriptada a desencriptar
     * @return el número desencriptado, o null si no se puede convertir
     */
    public static Double decryptToDouble(String encryptedStr) {
        String decryptedStr = decrypt(encryptedStr);
        
        if (decryptedStr.isEmpty()) {
            return null;
        }
        
        try {
            return Double.parseDouble(decryptedStr);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}