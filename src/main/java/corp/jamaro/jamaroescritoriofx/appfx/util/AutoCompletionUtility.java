package corp.jamaro.jamaroescritoriofx.appfx.util;

import javafx.scene.control.TextField;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.TextFields;

import java.text.Normalizer;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Clase de utilidad para configurar autocompletado en un TextField de JavaFX.
 *
 * @param <T> El tipo de objeto para el cual se implementa el autocompletado.
 */
public class AutoCompletionUtility<T> {

    private AutoCompletionBinding<T> autoCompletionBinding;

    /**
     * Configura el autocompletado para un TextField dado.
     *
     * @param textField       El campo de texto donde se aplicará el autocompletado.
     * @param items           La lista de elementos que se utilizarán para las sugerencias.
     * @param displayFunction Función para extraer el texto que se mostrará en las sugerencias.
     * @param onCompletion    Callback que se ejecuta cuando se selecciona una sugerencia.
     */
    public void bindAutoCompletion(TextField textField,
                                   List<T> items,
                                   Function<T, String> displayFunction,
                                   Consumer<T> onCompletion) {
        // Descartar el binding existente si ya existe
        if (autoCompletionBinding != null) {
            autoCompletionBinding.dispose();
        }

        // Configurar el binding de autocompletado utilizando ControlsFX
        autoCompletionBinding = TextFields.bindAutoCompletion(textField, param -> {
            String userInput = param.getUserText();
            if (userInput == null || userInput.trim().isEmpty()) {
                return items; // Retornar toda la lista si no hay texto
            } else {
                // Comprobar si es comodín (caracter inicial especial)
                // Esta lógica dependerá del controlador, por lo que aquí asumiremos
                // una función interna o bien se podría exponer un méthodo estático.
                if (esComodin(userInput)) {
                    // Si es comodín, retornar lista vacía (no se sugieren categorías)
                    return Collections.emptyList();
                }

                // Si no es comodín, filtrar las categorías normalmente
                String[] tokens = normalize(userInput).split("\\s+");

                List<T> filtered = items.stream()
                        .filter(item -> {
                            String normalizedItem = normalize(displayFunction.apply(item));
                            return Arrays.stream(tokens)
                                    .allMatch(token -> normalizedItem.contains(token));
                        })
                        .collect(Collectors.toList());

                // Ordenar las categorías por relevancia
                filtered.sort((item1, item2) -> {
                    String normalizedItem1 = normalize(displayFunction.apply(item1));
                    String normalizedItem2 = normalize(displayFunction.apply(item2));

                    boolean startsWithItem1 = normalizedItem1.startsWith(tokens[0]);
                    boolean startsWithItem2 = normalizedItem2.startsWith(tokens[0]);

                    if (startsWithItem1 && !startsWithItem2) {
                        return -1;
                    } else if (!startsWithItem1 && startsWithItem2) {
                        return 1;
                    }

                    int relevance1 = calculateRelevance(normalizedItem1, tokens);
                    int relevance2 = calculateRelevance(normalizedItem2, tokens);

                    if (relevance1 != relevance2) {
                        return Integer.compare(relevance2, relevance1);
                    }

                    return normalizedItem1.compareTo(normalizedItem2);
                });

                return filtered;
            }
        });

        // Definir la acción a realizar cuando se selecciona una opción de autocompletado
        autoCompletionBinding.setOnAutoCompleted(event -> onCompletion.accept(event.getCompletion()));
    }

    /**
     * Verifica si un texto es un comodín, es decir, si inicia con uno de los caracteres especiales.
     * Esta es una función interna a modo de ejemplo, puede adaptarse según tus necesidades.
     */
    private boolean esComodin(String input) {
        if (input.isEmpty()) return false;
        List<Character> COMODIN_CHARS = Arrays.asList('/', '*', '|', '?', '-', '+', '#');
        return COMODIN_CHARS.contains(input.charAt(0));
    }

    /**
     * Calcula la relevancia de un elemento basado en las coincidencias con los tokens.
     */
    private int calculateRelevance(String normalizedText, String[] tokens) {
        int relevance = 0;
        String[] words = normalizedText.split("\\s+");

        for (String token : tokens) {
            boolean tokenFound = false;

            for (String word : words) {
                if (word.startsWith(token)) {
                    relevance += 2;
                    tokenFound = true;
                    break;
                }
            }

            if (!tokenFound) {
                for (String word : words) {
                    if (word.contains(token)) {
                        relevance += 1;
                        break;
                    }
                }
            }
        }

        return relevance;
    }

    /**
     * Normaliza un texto eliminando diacríticos y pasándolo a minúsculas.
     */
    private String normalize(String text) {
        return Normalizer.normalize(text, Normalizer.Form.NFD)
                .replaceAll("\\p{M}", "")
                .toLowerCase();
    }

    /**
     * Destruye el binding de autocompletado cuando ya no se necesita.
     */
    public void dispose() {
        if (autoCompletionBinding != null) {
            autoCompletionBinding.dispose();
        }
    }
}
