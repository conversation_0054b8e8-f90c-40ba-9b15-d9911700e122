package corp.jamaro.jamaroescritoriofx.appfx.util;

import javafx.application.Platform;
import javafx.scene.control.ProgressIndicator;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Clase de utilidad para suscribirse a operaciones reactivos (Mono o Flux)
 * mostrando un indicador de carga mientras se procesa la información.
 */
@Slf4j
@UtilityClass
public class LoadingUtil {

    /**
     * Suscribe a un Mono mostrando el indicador de carga durante la operación.
     *
     * @param indicator El ProgressIndicator que se mostrará en la UI.
     * @param operation El Mono que representa la operación asíncrona.
     * @param onSuccess Acción a ejecutar cuando la operación es exitosa.
     * @param onError   Acción a ejecutar cuando ocurre un error.
     * @param <T>       Tipo de dato emitido por el Mono.
     * @return Disposable para la suscripción.
     */
    public static <T> Disposable subscribeWithLoading(ProgressIndicator indicator,
                                                      Mono<T> operation,
                                                      Consumer<T> onSuccess,
                                                      Consumer<Throwable> onError) {
        // Mostrar el indicador en el hilo de JavaFX
        Platform.runLater(() -> {
            log.debug("Mostrando indicador de carga para Mono.");
            indicator.setVisible(true);
        });

        // Retornamos el Disposable que crea .subscribe(...)
        return operation
                .doFinally(signalType -> Platform.runLater(() -> {
                    log.debug("Ocultando indicador de carga para Mono (signal: {}).", signalType);
                    indicator.setVisible(false);
                }))
                .subscribe(
                        result -> {
                            log.debug("Operación Mono completada con éxito.");
                            onSuccess.accept(result);
                        },
                        error -> {
                            log.error("Error durante la operación Mono.", error);
                            onError.accept(error);
                        }
                );
    }

    /**
     * Suscribe a un Flux mostrando el indicador de carga durante la operación.
     * En esta versión se oculta el indicador tan pronto se reciba el primer elemento.
     *
     * @param indicator  El ProgressIndicator que se mostrará en la UI.
     * @param operation  El Flux que representa la operación asíncrona.
     * @param onNext     Acción a ejecutar para cada elemento emitido.
     * @param onError    Acción a ejecutar cuando ocurre un error.
     * @param onComplete Acción a ejecutar al finalizar el flujo.
     * @param <T>        Tipo de dato emitido por el Flux.
     * @return Disposable para la suscripción.
     */
    public static <T> Disposable subscribeWithLoading(ProgressIndicator indicator,
                                                      Flux<T> operation,
                                                      Consumer<T> onNext,
                                                      Consumer<Throwable> onError,
                                                      Runnable onComplete) {
        // Mostrar el indicador en el hilo de JavaFX
        Platform.runLater(() -> {
            log.debug("Mostrando indicador de carga para Flux.");
            indicator.setVisible(true);
        });

        // Usamos un AtomicBoolean para asegurar que el indicador se oculte sólo una vez
        AtomicBoolean firstElementReceived = new AtomicBoolean(false);

        // Retornamos el Disposable que crea .subscribe(...)
        return operation
                .doFinally(signalType -> {
                    // Si el flujo termina sin emitir ningún elemento, ocultamos el indicador
                    if (firstElementReceived.compareAndSet(false, true)) {
                        Platform.runLater(() -> {
                            log.debug("Ocultando indicador de carga para Flux en doFinally (sin elementos, signal: {}).", signalType);
                            indicator.setVisible(false);
                        });
                    }
                })
                .subscribe(
                        item -> {
                            // Ocultar el indicador en el primer elemento recibido
                            if (firstElementReceived.compareAndSet(false, true)) {
                                Platform.runLater(() -> {
                                    log.debug("Ocultando indicador de carga para Flux en onNext.");
                                    indicator.setVisible(false);
                                });
                            }
                            log.debug("Emitiendo item en Flux: {}", item);
                            onNext.accept(item);
                        },
                        error -> {
                            log.error("Error durante la operación Flux.", error);
                            onError.accept(error);
                        },
                        () -> {
                            log.debug("Flujo Flux completado.");
                            onComplete.run();
                        }
                );
    }
}
