package corp.jamaro.jamaroescritoriofx.appfx.dinero.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.enums.TipoDeDinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class DineroEntrada {
    private UUID id;

    private User tramitadoPor;

    private Double montoEntrada;//es el monto real en soles, con este dato hace calculos el sistema

    private TipoDeDinero tipoDeDinero;

    private String detalles; // no siempre es necesario detalles de tipo de pago por ejemplo si es digital, yape, transferencia, etc, si es efectivo series de moneda entre otros

    private Instant createdAt=Instant.now();

}
