package corp.jamaro.jamaroescritoriofx.appfx;

import corp.jamaro.jamaroescritoriofx.JamaroEscritorioFxApplication;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * Clase encargada de iniciar la aplicación JavaFX integrada con Spring Boot.
 * Inicializa el contexto de Spring y establece la vista inicial.
 */
@Slf4j
public class JamaroFrontDesktop extends Application {

    private ConfigurableApplicationContext applicationContext;

    @Override
    public void init() {
        // Inicializa el contexto de Spring
        applicationContext = new SpringApplicationBuilder(JamaroEscritorioFxApplication.class).run();
    }

    @Override
    public void start(Stage stage) {
        // Obtiene el servicio de navegación desde el contexto de Spring
        NavigationService navigationService = applicationContext.getBean(NavigationService.class);
        navigationService.setStage(stage);

        // Navega a la vista inicial LOGIN_RFID
        navigationService.navigateTo(FXMLEnum.LOGIN_RFID).subscribe();

        // Configura el manejo del cierre de la ventana principal
        stage.setOnCloseRequest(event -> {
            disposeResources();
            Platform.exit();
            System.exit(0);
        });
    }

    @Override
    public void stop() {
        // Cierra el contexto de Spring y sale de la plataforma JavaFX
        disposeResources();
        if (applicationContext != null) {
            applicationContext.close();
        }
        Platform.exit();
    }

    /**
     * Libera recursos antes de cerrar la aplicación.
     */
    private void disposeResources() {
        // Agregar limpieza de recursos si es necesario
    }
}
