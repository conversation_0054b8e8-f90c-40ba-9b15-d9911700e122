package corp.jamaro.jamaroescritoriofx.appfx.model.collaborative;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class UserConnected {
    private String id;

    private User user;

    private Instant lastConnectionAt;
    private Instant lastDisconnectionAt;

    private String clientType;
    private String clientVersion;

    private Boolean isOnline;//true or false

    //Agregar aqui un campo de User Preferences que será un json que guardará cosas como Orden de presentacion en la GUI, Posiciones de Separadores X y Y etc. Pero por ahora solo el Orden de Presentacion que es un Entero
    // Campo para guardar las preferencias de GUI en formato JSON (por ejemplo, {"presentationOrder": 1})
    private String userGuiPreferences;
}
