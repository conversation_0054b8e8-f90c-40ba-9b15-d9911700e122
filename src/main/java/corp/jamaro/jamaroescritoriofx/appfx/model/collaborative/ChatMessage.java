package corp.jamaro.jamaroescritoriofx.appfx.model.collaborative;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class ChatMessage {
    private UUID id;
    private User user;  // User que envía el mensaje
    private Instant sentAt = Instant.now();     // Momento en que se envió
    private String content;     // Texto del mensaje
  //  private ToBucketFileRelation attachFile;

}
