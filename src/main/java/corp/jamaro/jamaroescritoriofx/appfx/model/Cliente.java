package corp.jamaro.jamaroescritoriofx.appfx.model;

import lombok.Data;

import java.util.UUID;

@Data
public class Cliente {
    private UUID id;

    private String nombre;
    private String apellido;
    private String razonSocial;

    private String dni;
    private String ruc;
    private String otroDocumento;

    private String direccion;
    private String telefono;
    private String email;

    private Boolean tieneCredito;
    private Boolean esMayorista;

    private Boolean esProveedor;

    private Boolean estado;

    private String metadata;

    //agregar un enum para tipo de cliente
    private TipoCliente tipoCliente;

    public enum TipoCliente {
        NATURAL,
        JURIDICO
    }


}