package corp.jamaro.jamaroescritoriofx.appfx.model;

import lombok.Data;
import lombok.Getter;

import java.util.UUID;

@Data
public class User {
    private UUID id;

    private String username;
    private String nombre;
    private String apellidos;
    private String documento;
    

    @Override
    public String toString() {
        return nombre + " - " + (documento != null ? documento : "documento no especificado");
    }

}
