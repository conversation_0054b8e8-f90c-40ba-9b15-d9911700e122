package corp.jamaro.jamaroescritoriofx.appfx.model.file;

import lombok.Data;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Data
public class BucketFile {
    private UUID id;
    private String objectName; // Nombre único del objeto dentro del bucket
    private String contentType;// Tipo MIME del objeto, por ejemplo, "image/jpeg"
    private long size; // Tamaño del objeto en bytes
    private String etag; // Identificador único del contenido del objeto
    private Instant creadoActualizado = Instant.now();
    private Map<String,String> metadata;
    private String presignedUrl;

}
