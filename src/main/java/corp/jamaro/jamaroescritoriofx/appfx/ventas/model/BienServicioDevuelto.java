package corp.jamaro.jamaroescritoriofx.appfx.ventas.model;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class BienServicioDevuelto {
    private UUID id;

    private String devueltoPor; // username que devolvió este item

    private Item item;//puede ser nulo cuando se devuelve un pedido

    // normalmente será la misma descripcion que el BienServicioCargado ya que es a partir de este que se devuelve
    // pero se puede editar
    private String descripcionDelBienServicio;

    private String detalles;//detalles de la devolucion producto dañado, faltan algunas cosas, etc.

    private Double cantidad;
    private Double precioAcordadoDevolver;

    private Double montoDevuelto;//el monto total devuelto por la cantidad de este Item. (precioAcordadoDevolver * cantidad)

    private String motivo;

    private Boolean isDineroDevuelto;// true (devuelto) false (no devuelto) para encontrar facilmente que dinero faltan devolver en caja.

    private Instant createdAt;


}
