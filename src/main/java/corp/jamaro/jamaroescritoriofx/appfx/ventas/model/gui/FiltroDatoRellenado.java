package corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui;


import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoBusqueda;
import lombok.Data;

import java.util.UUID;

@Data
public class FiltroDatoRellenado {

    private String id;

    private Filtro filtro;

    // Indica la fila en la “matriz” de filtros.
    private Integer fila;

    // Indica la columna en la “matriz” de filtros.
    private Integer columna;

    // Dato ingresado (o vacío) para este filtro.
    private String dato;

    private TipoBusqueda tipoBusqueda;
}
