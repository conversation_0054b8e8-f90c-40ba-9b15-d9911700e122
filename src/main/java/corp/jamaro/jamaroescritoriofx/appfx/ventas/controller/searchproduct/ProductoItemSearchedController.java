package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Atributo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.CodigoFabrica;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.util.EncryptionUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.beans.property.SimpleStringProperty;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import org.kordamp.ikonli.fontawesome5.FontAwesomeSolid;
import org.kordamp.ikonli.javafx.FontIcon;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class ProductoItemSearchedController extends BaseController {

    private final SearchProductGuiService searchProductGuiService;

    private Producto producto;
    private SearchProductGui searchProductGui;


    // Propiedad para controlar si el panel está expandido o contraído
    private final BooleanProperty expanded = new SimpleBooleanProperty(false);

    // Indicador visual de expansión/contracción
    private FontIcon expandIcon;

    @FXML
    private HBox hbFiltrosAtributos;

    @FXML
    private Label lblCodProductoOld;

    @FXML
    private Label lblDescripcion;

    @FXML
    private TableView<Item> tvItems;

    @FXML
    private TableColumn<Item, String> marcaColumn;

    @FXML
    private TableColumn<Item, String> codCompuestoColumn;

    @FXML
    private TableColumn<Item, String> precioVentaBaseColumn;

    @FXML
    private TableColumn<Item, String> precioVentaPromocionColumn;

    @FXML
    private TableColumn<Item, String> precioVentaPublicoColumn;

    @FXML
    private TableColumn<Item, String> stockTotalColumn;

    @FXML
    private TableColumn<Item, String> ubicacionColumn;

    @FXML
    private AnchorPane root;

    @FXML
    private VBox mainVBox;

    @FXML
    private VBox vbCodigosFabrica;

    @FXML
    private VBox vbVehiculos;

    private SearchProductGuiController.ItemClickHandler itemClickHandler;
    private SearchProductGuiController.ItemDoubleClickHandler itemDoubleClickHandler;

    /**
     * Establece el manejador para eventos de clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemClickHandler(SearchProductGuiController.ItemClickHandler handler) {
        this.itemClickHandler = handler;
    }

    /**
     * Establece el manejador para eventos de doble clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemDoubleClickHandler(SearchProductGuiController.ItemDoubleClickHandler handler) {
        this.itemDoubleClickHandler = handler;
    }

    @Override
    public void initialize(java.net.URL url, java.util.ResourceBundle resourceBundle) {
        // Configurar las columnas del TableView
        configureTableColumns();

        // Crear el icono de expansión
        expandIcon = new FontIcon(FontAwesomeSolid.CHEVRON_DOWN);
        expandIcon.setIconSize(18);
        expandIcon.getStyleClass().add("font-icon-light");

        // Configurar el evento de clic directamente en el root para mejorar la respuesta
        root.setOnMouseClicked(this::handleCellClick);

        // Agregar cursor de mano para indicar que es clickeable
        root.setCursor(javafx.scene.Cursor.HAND);

        // Inicialmente ocultar el TableView (ahora se hace programáticamente para que sea visible en Scene Builder)
        tvItems.setVisible(false);
        tvItems.setManaged(false);
        tvItems.setPrefHeight(0);

        // Usar política de redimensionamiento que llene todo el ancho disponible
        // CONSTRAINED_RESIZE_POLICY_FLEX_LAST_COLUMN distribuye el espacio automáticamente
        tvItems.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY_FLEX_LAST_COLUMN);


        tvItems.setFixedCellSize(25); // Establecer altura fija para las celdas

        // Hacer que las columnas no sean reordenables
        tvItems.getColumns().forEach(column -> column.setReorderable(false));

        // Configurar el row factory para aplicar estilos especiales a filas con stock cero o negativo
        tvItems.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<Item>() {
                @Override
                protected void updateItem(Item item, boolean empty) {
                    super.updateItem(item, empty);

                    if (item == null || empty) {
                        setStyle("");
                        getStyleClass().removeAll("zero-stock-row");
                        return;
                    }

                    // Verificar si el stock es cero o negativo
                    if (item.getStockTotal() != null && item.getStockTotal() <= 0) {
                        // Aplicar clase CSS para filas con stock cero o negativo
                        if (!getStyleClass().contains("zero-stock-row")) {
                            getStyleClass().add("zero-stock-row");
                        }
                    } else {
                        // Remover la clase si el stock es positivo
                        getStyleClass().removeAll("zero-stock-row");
                    }
                }
            };
            return row;
        });

        // Aplicar estilos CSS
        root.getStyleClass().add("producto-item");
        tvItems.getStyleClass().add("items-table-view");

        // Aplicar la clase CSS para vbVehiculos según las preferencias del usuario
        vbVehiculos.getStyleClass().add("vb-vehiculos");

        // Evitar que el root tome el foco para prevenir robo de foco al hacer hover
        root.setFocusTraversable(false);

        // Configurar eventos de clic y doble clic en la tabla de items
        tvItems.setOnMouseClicked(event -> {
            Item selectedItem = tvItems.getSelectionModel().getSelectedItem();
            if (selectedItem != null && producto != null) {
                if (event.getClickCount() == 2 && itemDoubleClickHandler != null) {
                    // Doble clic en item
                    log.debug("Doble clic en item: {} del producto: {}",
                             selectedItem.getCodCompuesto(), producto.getCodProductoOld());
                    itemDoubleClickHandler.handle(selectedItem, producto);
                    event.consume();
                } else if (event.getClickCount() == 1 && itemClickHandler != null) {
                    // Clic simple en item
                    log.debug("Clic simple en item: {} del producto: {}",
                             selectedItem.getCodCompuesto(), producto.getCodProductoOld());
                    itemClickHandler.handle(selectedItem, producto);
                    event.consume();
                }
            }
        });

        // Configurar listener para redistribución inteligente de columnas
        setupColumnResizeListener();
    }

    /**
     * Configura las columnas del TableView
     */
    private void configureTableColumns() {
        // Columna Marca (Camel Case)
        marcaColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            String marcaText = item.getMarca() != null && item.getMarca().getNombre() != null ?
                    toCamelCase(item.getMarca().getNombre()) : "Sin marca";
            return new SimpleStringProperty(marcaText);
        });
        setColumnBasicProperties(marcaColumn);

        // Columna Código Compuesto
        codCompuestoColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            String codCompuesto = item.getCodCompuesto() != null ? item.getCodCompuesto().toUpperCase() : "";
            return new SimpleStringProperty(codCompuesto);
        });
        setColumnBasicProperties(codCompuestoColumn);

        // Las columnas de atributos se crean dinámicamente en createDynamicColumns()

        // Columnas de precios - datos encriptados
        configurePriceColumn(precioVentaBaseColumn, Item::getPrecioVentaBase);
        configurePriceColumn(precioVentaPromocionColumn, Item::getPrecioVentaPromocion);
        configurePriceColumn(precioVentaPublicoColumn, Item::getPrecioVentaPublico);

        // Columna Stock Total (Q)
        stockTotalColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getStockTotal() == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(item.getStockTotal().toString());
        });
        setColumnBasicProperties(stockTotalColumn);

        // Columna Ubicación - solo mostrar ubicaciones con isPrincipal = true
        ubicacionColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getUbicaciones() == null || item.getUbicaciones().isEmpty()) {
                return new SimpleStringProperty("");
            }
            
            // Filtrar ubicaciones con isPrincipal = true y tomar la primera
            String ubicacionText = item.getUbicaciones().stream()
                    .filter(u -> u.getIsPrincipal() != null && u.getIsPrincipal())
                    .filter(u -> u.getNombre() != null && !u.getNombre().trim().isEmpty())
                    .map(u -> u.getNombre().toUpperCase())
                    .findFirst()
                    .orElse("");
            
            return new SimpleStringProperty(ubicacionText);
        });
        setColumnBasicProperties(ubicacionColumn);
    }

    /**
     * Configura una columna de precio con encriptación
     */
    private void configurePriceColumn(TableColumn<Item, String> column, java.util.function.Function<Item, Double> priceGetter) {
        column.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            Double price = priceGetter.apply(item);
            if (price == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(EncryptionUtil.encrypt(price));
        });
        setColumnBasicProperties(column);
    }

    /**
     * Configura el listener para redistribución inteligente de columnas
     */
    private void setupColumnResizeListener() {
        // Usar un listener más robusto que maneje la redistribución de manera inteligente
        tvItems.widthProperty().addListener((obs, oldWidth, newWidth) -> {
            if (newWidth.doubleValue() > 0 && tvItems.isVisible() && !tvItems.getItems().isEmpty()) {
                // Usar runOnUiThread del BaseController para mejor manejo de hilos
                runOnUiThread(() -> {
                    // Pequeño delay para asegurar que el layout esté estable
                    Platform.runLater(this::forceUniformColumnDistribution);
                });
            }
        });
    }


    /**
     * Establece las propiedades básicas comunes para todas las columnas
     */
    private void setColumnBasicProperties(TableColumn<Item, String> column) {
        column.setReorderable(false);
    }

    /**
     * Crea un label vacío para mantener la estructura cuando no hay contenido
     */
    private Label createEmptyStructuralLabel() {
        Label emptyLabel = new Label("");
        emptyLabel.setMinHeight(1); // Altura mínima reducida
        return emptyLabel;
    }

    /**
     * Convierte un texto a Camel Case (primera letra de cada palabra en mayúscula)
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = true;

        for (char c : text.toLowerCase().toCharArray()) {
            if (Character.isSpaceChar(c) || c == '-' || c == '_') {
                nextUpperCase = true;
            } else if (nextUpperCase) {
                c = Character.toUpperCase(c);
                nextUpperCase = false;
            }

            if (!Character.isSpaceChar(c) && c != '-' && c != '_') {
                result.append(c);
            } else if (!result.isEmpty()) {
                result.append(' ');
            }
        }

        return result.toString();
    }

    /**
     * Configura el controlador con un producto
     */
    public void setProducto(Producto producto) {
        this.producto = producto;
        updateProductoUI();
        // No cargamos los items aquí, solo cuando se expande

        // Resetear el estado de expansión
        expanded.set(false);

        // Si hay un controlador expandido actualmente, asegurarse de que se contraiga
        if (tvItems.isVisible()) {
            tvItems.setVisible(false);
            tvItems.setManaged(false);
            tvItems.getItems().clear();
        }
    }

    /**
     * Configura el controlador con el SearchProductGui
     */
    public void setSearchProductGui(SearchProductGui searchProductGui) {
        this.searchProductGui = searchProductGui;
        // Si ya tenemos un producto, actualizar la UI para reflejar el nuevo orden de atributos
        if (this.producto != null) {
            updateProductoUI();
        }
    }

    /**
     * Método público para manejar los clics desde la celda del ListView principal
     */
    public void handleCellClick(MouseEvent event) {
        // Solo responder a clics izquierdos (PRIMARY), ignorar clics derechos (SECONDARY)
        if (event.getButton() != javafx.scene.input.MouseButton.PRIMARY) {
            // No consumir el evento para permitir que el clic derecho sea manejado por el ListView
            log.debug("Clic no-izquierdo detectado en producto {}, ignorando para expansión",
                     producto != null ? producto.getCodProductoOld() : "null");
            return;
        }

        // Consumir el evento para evitar que se propague
        event.consume();

        // Capturar el producto actual como efectivamente final para el log
        final Producto currentProducto = this.producto;

        // Llamar al método privado para expandir/contraer en el hilo de UI
        runOnUiThread(() -> toggleExpanded());

        // Registrar el evento para depuración
        log.debug("Clic izquierdo detectado en producto {}, cambiando estado de expansión a {}",
                 currentProducto != null ? currentProducto.getCodProductoOld() : "null",
                 !expanded.get());
    }

    /**
     * Devuelve el estado actual de expansión del producto
     */
    public boolean isExpanded() {
        return expanded.get();
    }

    /**
     * Devuelve el ID o código del producto para identificación
     */
    public String getProductoId() {
        return producto != null ? producto.getCodProductoOld() : "desconocido";
    }

    /**
     * Establece el estado de expansión del producto directamente sin hacer toggle
     * @param expand true para expandir, false para contraer
     */
    public void setExpanded(boolean expand) {
        // Capturar el ID del producto para usar en lambdas
        final String productoId = getProductoId();

        // Registrar el intento de cambio de estado
        log.debug("Intento de cambiar estado de expansión para producto {} de {} a {} (programaticamente)",
                 productoId, expanded.get(), expand);

        // Incluso si el estado es el mismo, forzar la actualización de la UI
        // Esto ayuda en casos donde la UI no refleja correctamente el estado actual
        expanded.set(expand);

        // Actualizar la UI según el nuevo estado
        updateExpandedState();

        // Forzar una actualización adicional del layout después de un breve retraso
        // Esto ayuda a asegurar que los cambios se apliquen correctamente
        Platform.runLater(() -> {
            log.debug("Forzando actualización adicional del layout para producto {}", productoId);
            root.requestLayout();
            root.applyCss();
            root.layout();
        });
    }

    /**
     * Maneja la expansión/contracción del panel
     */
    private void toggleExpanded() {
        if (producto == null) {
            log.warn("No se puede expandir/contraer: producto es null");
            return;
        }

        // Capturar el producto actual como efectivamente final para el log
        final Producto currentProducto = this.producto;

        // Cambiar el estado de expansión
        boolean newValue = !expanded.get();
        expanded.set(newValue);

        log.debug("Cambiando estado de expansión para producto {} a {}", currentProducto.getCodProductoOld(), newValue);

        // Actualizar la UI según el nuevo estado
        updateExpandedState();
    }

    /**
     * Actualiza la UI según el estado de expansión actual
     */
    private void updateExpandedState() {
        final boolean isExpanded = expanded.get();
        final String productoId = getProductoId(); // Capture for lambda

        log.debug("Actualizando UI para producto {} a estado expandido={}", productoId, isExpanded);

        // Aplicar inmediatamente los cambios de estilo
        if (isExpanded) {
            // Expandir
            if (!root.getStyleClass().contains("producto-item-expanded")) {
                root.getStyleClass().add("producto-item-expanded");
            }
            expandIcon.setIconCode(FontAwesomeSolid.CHEVRON_UP);

            // Hacer visible el TableView inmediatamente para dar feedback visual
            tvItems.setVisible(true);
            tvItems.setManaged(true);

            // Establecer una altura mínima mientras se cargan los items
            if (tvItems.getItems().isEmpty()) {
                tvItems.setPrefHeight(50);
                tvItems.setMinHeight(50);
            }

            // Solo cargar los items cuando se expande por primera vez
            if (tvItems.getItems().isEmpty()) {
                // Cargar los items (esto actualizará la altura cuando termine)
                loadItems();
            } else {
                // Si ya hay items, ajustar la altura inmediatamente
                adjustTableViewHeight();
            }
        } else {
            // Contraer
            root.getStyleClass().remove("producto-item-expanded");
            expandIcon.setIconCode(FontAwesomeSolid.CHEVRON_DOWN);

            // Ocultar el TableView inmediatamente
            tvItems.setVisible(false);
            tvItems.setManaged(false);
            tvItems.setPrefHeight(0);
            tvItems.setMinHeight(0);
            tvItems.setMaxHeight(0);
        }

        // Forzar la actualización del layout inmediatamente usando runOnUiThread del BaseController
        runOnUiThread(() -> {
            root.requestLayout();
            root.applyCss();
            root.layout();
            log.debug("Layout actualizado para producto {}", productoId);

            // Forzar una actualización adicional después de un breve retraso
            // Esto es especialmente importante cuando hay scroll involucrado
            Platform.runLater(() -> {
                if (isExpanded) {
                    // Si estamos expandiendo, asegurar que el TableView sea visible y tenga la altura correcta
                    tvItems.setVisible(true);
                    tvItems.setManaged(true);
                    if (!tvItems.getItems().isEmpty()) {
                        adjustTableViewHeight();
                    }
                    log.debug("Actualización adicional para producto expandido {}", productoId);
                }
            });
        });
    }

    /**
     * Ajusta la altura del TableView para mostrar todos los items con un cálculo más preciso
     */
    private void adjustTableViewHeight() {
        if (!expanded.get() || tvItems.getItems().isEmpty()) return;

        // Calcular la altura necesaria para mostrar todos los items
        // Usar el valor de fixedCellSize establecido anteriormente
        double itemHeight = tvItems.getFixedCellSize();
        int itemCount = tvItems.getItems().size();

        // Ajustar para la estructura de columnas (ahora solo un nivel)
        // Reducir el header height para optimizar espacio
        double headerHeight = 30;
        double totalHeight = itemCount * itemHeight + headerHeight;

        // Establecer la altura exacta del TableView para mostrar todos los items sin scroll
        tvItems.setPrefHeight(totalHeight);
        tvItems.setMaxHeight(totalHeight);
        tvItems.setMinHeight(totalHeight);

        // Desactivar el scroll
        tvItems.setMouseTransparent(false); // Asegurar que los eventos de mouse lleguen al TableView
        tvItems.setFocusTraversable(false); // Evitar que el TableView tome el foco

        // Solicitar un nuevo layout inmediatamente
        root.requestLayout();

        // Forzar una actualización del layout para asegurar que se aplique la altura
        Platform.runLater(() -> {
            tvItems.layout();
            root.layout();
        });

        log.debug("Altura calculada para TableView: {} para {} items", totalHeight, itemCount);
    }

    /**
     * Actualiza la UI con la información del producto
     */
    private void updateProductoUI() {
        if (producto == null) return;

        // Código y descripción
        lblCodProductoOld.setText(producto.getCodProductoOld() != null ? producto.getCodProductoOld() : "");
        // Usar clases CSS en lugar de estilos inline
        lblCodProductoOld.getStyleClass().add("producto-codigo");

        // Apply camel case to product description as per user preference
        lblDescripcion.setText(producto.getDescripcion() != null ? toCamelCase(producto.getDescripcion()) : "");
        // Usar clases CSS en lugar de estilos inline
        lblDescripcion.getStyleClass().add("producto-descripcion");

        // Códigos de fábrica
        vbCodigosFabrica.getChildren().clear();
        if (producto.getCodigosFabrica() != null && !producto.getCodigosFabrica().isEmpty()) {
            producto.getCodigosFabrica().stream()
                .map(CodigoFabrica::getCodigo)
                .filter(Objects::nonNull)
                .forEach(codigo -> {
                    Label label = new Label(codigo.toUpperCase());
                    label.setAlignment(Pos.CENTER);
                    label.setMaxWidth(Double.MAX_VALUE);
                    label.setWrapText(true);
                    label.getStyleClass().add("codigo-fabrica-label");
                    vbCodigosFabrica.getChildren().add(label);
                });
        } else {
            // Si no hay códigos de fábrica, agregar un espacio en blanco para mantener la estructura
            vbCodigosFabrica.getChildren().add(createEmptyStructuralLabel());
        }

        // Atributos
        hbFiltrosAtributos.getChildren().clear();
        if (producto.getAtributos() != null && !producto.getAtributos().isEmpty()) {
            // Ordenar atributos según la nueva lógica
            List<Atributo> sortedAtributos = sortAtributos(producto.getAtributos());

            // Mostrar los atributos ordenados
            sortedAtributos.forEach(atributo -> {
                HBox atributoBox = createAtributoBox(atributo);
                hbFiltrosAtributos.getChildren().add(atributoBox);
            });
        }

        // Vehículos
        vbVehiculos.getChildren().clear();
        if (producto.getVehiculos() != null && !producto.getVehiculos().isEmpty()) {
            String vehiculosText = producto.getVehiculos().stream()
                .flatMap(v -> v.getNombres().stream())
                .map(VehiculoNombre::getNombre)
                .filter(Objects::nonNull)
                .map(this::toCamelCase) // Convertir a Camel Case según preferencia del usuario
                .collect(Collectors.joining(", "));

            Label label = new Label(vehiculosText);
            label.setAlignment(Pos.CENTER_LEFT); // Alineación a la izquierda
            label.setMaxWidth(Double.MAX_VALUE);
            label.setWrapText(true);
            // Ya no necesitamos establecer el tamaño de fuente aquí, se hace a través de CSS
            label.getStyleClass().add("vehiculos-label"); // Añadir clase CSS para estilos adicionales
            vbVehiculos.getChildren().add(label);
        } else {
            // Si no hay vehículos, agregar un espacio en blanco para mantener la estructura
            vbVehiculos.getChildren().add(createEmptyStructuralLabel());
        }

        // Forzar una actualización del layout para que se recalcule la altura
        Platform.runLater(() -> {
            root.requestLayout();
            root.applyCss();
            root.layout();
        });

        // Agregar el icono de expansión
        HBox expandIconContainer = new HBox(expandIcon);
        expandIconContainer.setAlignment(Pos.CENTER_RIGHT); // Mantener el icono a la derecha
        expandIconContainer.setPadding(new Insets(2, 10, 2, 0)); // Reducir el padding vertical
        expandIconContainer.setMaxWidth(Double.MAX_VALUE); // Permitir que ocupe todo el ancho
        HBox.setHgrow(expandIconContainer, Priority.ALWAYS); // Hacer que crezca para empujar el icono a la derecha
        vbVehiculos.getChildren().add(expandIconContainer);
    }

    /**
     * Crea un HBox para mostrar un atributo con su nombre y valor
     */
    private HBox createAtributoBox(Atributo atributo) {
        Label nombreLabel = new Label(atributo.getFiltro().getNombreFiltro() + ":");
        nombreLabel.getStyleClass().add("atributo-nombre");

        Label valorLabel = new Label(atributo.getDato());
        valorLabel.getStyleClass().add("atributo-valor");

        HBox box = new HBox(5, nombreLabel, valorLabel);
        box.setAlignment(Pos.CENTER);
        box.getStyleClass().add("atributo-box");
        box.setPadding(new Insets(2, 4, 2, 4)); // Reducir el padding para hacer el componente más compacto
        return box;
    }

    /**
     * Carga los items del producto
     */
    private void loadItems() {
        if (producto == null || producto.getId() == null) {
            log.warn("No se pueden cargar items: producto o ID es null");
            return;
        }

        log.debug("Iniciando carga de items para producto {}", getProductoId());

        // Mostrar un indicador de carga o mensaje mientras se cargan los items
        // Esto ayuda a dar feedback visual inmediato al usuario
        Label loadingLabel = new Label("Cargando items...");
        loadingLabel.getStyleClass().add("loading-label");
        tvItems.setPlaceholder(loadingLabel);

        // Asegurar que el TableView sea visible mientras se cargan los items
        tvItems.setVisible(true);
        tvItems.setManaged(true);

        // Capture producto.getId() as effectively final for lambda
        final UUID productoId = producto.getId();

        subscribeOnBoundedElastic(
            searchProductGuiService.getItemsByProductoId(productoId).collectList(),
            items -> Platform.runLater(() -> {
                // Verificar si el producto sigue expandido (podría haber cambiado mientras se cargaban los items)
                if (!expanded.get()) {
                    log.debug("El producto {} ya no está expandido, ignorando resultados de carga", getProductoId());
                    return;
                }

                // Limpiar items existentes
                tvItems.getItems().clear();

                // Solo agregar items que no sean nulos
                if (items != null && !items.isEmpty()) {
                    log.debug("Procesando {} items cargados para producto {}", items.size(), getProductoId());

                    // Ordenar los items por stock total de mayor a menor
                    List<Item> sortedItems = items.stream()
                        .sorted(Comparator.comparing(item -> {
                            // Usar 0.0 como valor predeterminado si stockTotal es null
                            return item.getStockTotal() != null ? item.getStockTotal() : 0.0;
                        }, Comparator.reverseOrder()))
                        .collect(Collectors.toList());

                    // Crear columnas dinámicas basadas en los Filtros de los items
                    createDynamicColumns(sortedItems);

                    // Agregar los items ordenados al TableView
                    tvItems.getItems().addAll(sortedItems);
                    log.info("Cargados {} items para el producto {}, ordenados por stock de mayor a menor",
                             sortedItems.size(), getProductoId());

                    // Ajustar la altura del TableView inmediatamente
                    adjustTableViewHeight();

                    // Asegurar que el TableView sea visible y tenga los estilos correctos
                    tvItems.setVisible(true);
                    tvItems.setManaged(true);
                    if (!root.getStyleClass().contains("producto-item-expanded")) {
                        root.getStyleClass().add("producto-item-expanded");
                    }

                    // Aplicar distribución uniforme después de que todo esté renderizado
                    scheduleUniformDistribution();
                } else {
                    // Si no hay items, mostrar un mensaje pero mantener el panel expandido
                    log.info("No hay items para el producto {}", getProductoId());
                    Label noItemsLabel = new Label("No hay items disponibles");
                    noItemsLabel.getStyleClass().add("no-items-label");
                    tvItems.setPlaceholder(noItemsLabel);

                    // Ajustar la altura para mostrar el mensaje
                    tvItems.setPrefHeight(50);
                    tvItems.setMaxHeight(50);
                    tvItems.setMinHeight(50);
                }

                // Forzar una actualización del layout
                runOnUiThread(() -> {
                    root.requestLayout();
                    root.applyCss();
                    root.layout();
                    log.debug("Layout actualizado después de cargar items para producto {}", getProductoId());
                });
            }),
            error -> {
                log.error("Error al cargar items para producto {}: {}", getProductoId(), error.getMessage(), error);

                // Manejar el error en la UI
                Platform.runLater(() -> {
                    if (expanded.get()) { // Solo si sigue expandido
                        Label errorLabel = new Label("Error al cargar items");
                        errorLabel.getStyleClass().add("error-label");
                        tvItems.setPlaceholder(errorLabel);
                        tvItems.getItems().clear();
                    }
                });
            }
        );
    }




    /**
     * Crea columnas dinámicas basadas en los Filtros únicos encontrados en los items
     */
    private void createDynamicColumns(List<Item> items) {
        // Obtener todos los Filtros únicos de todos los items
        Set<Filtro> uniqueFiltros = new LinkedHashSet<>();
        
        for (Item item : items) {
            if (item.getAtributos() != null) {
                for (Atributo atributo : item.getAtributos()) {
                    if (atributo.getFiltro() != null && atributo.getFiltro().getNombreFiltro() != null) {
                        uniqueFiltros.add(atributo.getFiltro());
                    }
                }
            }
        }

        // Ordenar los Filtros usando la misma lógica que sortAtributos
        List<Filtro> sortedFiltros = sortFiltros(uniqueFiltros);

        // Limpiar columnas dinámicas existentes (mantener solo las columnas fijas)
        tvItems.getColumns().removeIf(column -> 
            column != marcaColumn && column != codCompuestoColumn && 
            column != precioVentaBaseColumn && column != precioVentaPromocionColumn &&
            column != precioVentaPublicoColumn && column != stockTotalColumn && 
            column != ubicacionColumn);

        // Crear columnas dinámicas para cada Filtro con configuración inicial uniforme
        int insertIndex = 2; // Después de marca y codCompuesto

        for (Filtro filtro : sortedFiltros) {
            TableColumn<Item, String> dynamicColumn = new TableColumn<>(filtro.getNombreFiltro());
            setColumnBasicProperties(dynamicColumn);

            // Configurar como redimensionable con configuración inicial
            dynamicColumn.setResizable(true);
            dynamicColumn.setMinWidth(90.0);  // Ancho mínimo
            dynamicColumn.setMaxWidth(Double.MAX_VALUE); // Sin límite máximo

            // Configurar el cell value factory para esta columna
            dynamicColumn.setCellValueFactory(cellData -> {
                Item item = cellData.getValue();
                if (item.getAtributos() != null) {
                    for (Atributo atributo : item.getAtributos()) {
                        if (atributo.getFiltro() != null &&
                            atributo.getFiltro().getId().equals(filtro.getId()) &&
                            atributo.getDato() != null && !atributo.getDato().trim().isEmpty()) {
                            return new SimpleStringProperty(atributo.getDato());
                        }
                    }
                }
                return new SimpleStringProperty("");
            });

            // Insertar la columna en la posición correcta
            tvItems.getColumns().add(insertIndex++, dynamicColumn);
        }

        log.debug("Creadas {} columnas dinámicas para Filtros", sortedFiltros.size());

        // Aplicar distribución uniforme después de crear todas las columnas
        // Usar Platform.runLater para asegurar que el layout esté completo
        runOnUiThread(() -> {
            Platform.runLater(this::forceUniformColumnDistribution);
        });
    }

    /**
     * Ordena los Filtros usando la misma lógica que sortAtributos
     */
    private List<Filtro> sortFiltros(Set<Filtro> filtros) {
        // Si no hay SearchProductGui o no tiene FiltroDatoRellenado, usar orden alfabético
        if (searchProductGui == null || searchProductGui.getFiltroDatoRellenados() == null || searchProductGui.getFiltroDatoRellenados().isEmpty()) {
            return filtros.stream()
                .sorted((f1, f2) -> f1.getNombreFiltro().compareToIgnoreCase(f2.getNombreFiltro()))
                .collect(Collectors.toList());
        }

        // Usar la misma lógica de ordenamiento que sortAtributos pero para Filtros
        List<Filtro> result = new ArrayList<>();
        Set<UUID> processedFiltroIds = new HashSet<>();

        // Ordenar FiltroDatoRellenado por fila y columna
        List<FiltroDatoRellenado> sortedFiltroDatos = searchProductGui.getFiltroDatoRellenados().stream()
            .filter(f -> f.getFiltro() != null)
            .sorted(Comparator.comparing(FiltroDatoRellenado::getFila)
                    .thenComparing(FiltroDatoRellenado::getColumna))
            .collect(Collectors.toList());

        // Procesar filtros en el orden de FiltroDatoRellenado
        for (FiltroDatoRellenado filtroDato : sortedFiltroDatos) {
            if (processedFiltroIds.contains(filtroDato.getFiltro().getId())) {
                continue;
            }
            
            for (Filtro filtro : filtros) {
                if (filtro.getId().equals(filtroDato.getFiltro().getId())) {
                    result.add(filtro);
                    processedFiltroIds.add(filtro.getId());
                    break;
                }
            }
        }

        // Agregar los filtros restantes ordenados alfabéticamente
        filtros.stream()
            .filter(f -> !processedFiltroIds.contains(f.getId()))
            .sorted((f1, f2) -> f1.getNombreFiltro().compareToIgnoreCase(f2.getNombreFiltro()))
            .forEach(result::add);

        return result;
    }


    /**
     * Ordena los atributos según la nueva lógica:
     * 1. Primero los que coinciden con FiltroDatoRellenado, ordenados por fila y columna
     * 2. Luego el resto ordenados alfabéticamente
     * Evita duplicados de Filtros que ya aparecen en filas anteriores
     */
    private List<Atributo> sortAtributos(Set<Atributo> atributos) {
        // Si no hay SearchProductGui o no tiene FiltroDatoRellenado, usar orden alfabético
        if (searchProductGui == null || searchProductGui.getFiltroDatoRellenados() == null || searchProductGui.getFiltroDatoRellenados().isEmpty()) {
            return atributos.stream()
                .filter(a -> a.getFiltro() != null && a.getDato() != null && !a.getDato().isEmpty())
                .sorted((a1, a2) -> {
                    String nombre1 = a1.getFiltro().getNombreFiltro();
                    String nombre2 = a2.getFiltro().getNombreFiltro();
                    return nombre1.compareToIgnoreCase(nombre2);
                })
                .collect(Collectors.toList());
        }

        // Filtrar atributos válidos
        List<Atributo> validAtributos = atributos.stream()
            .filter(a -> a.getFiltro() != null && a.getDato() != null && !a.getDato().isEmpty())
            .collect(Collectors.toList());

        // Ordenar FiltroDatoRellenado por fila y columna
        List<FiltroDatoRellenado> sortedFiltroDatos = searchProductGui.getFiltroDatoRellenados().stream()
            .filter(f -> f.getFiltro() != null)
            .sorted(Comparator.comparing(FiltroDatoRellenado::getFila)
                    .thenComparing(FiltroDatoRellenado::getColumna))
            .collect(Collectors.toList());

        // Conjunto para rastrear los filtros ya procesados (para evitar duplicados)
        Set<UUID> processedFiltroIds = new HashSet<>();

        // Lista para el resultado final ordenado
        List<Atributo> result = new ArrayList<>();

        // Mapa para agrupar FiltroDatoRellenado por fila
        Map<Integer, List<FiltroDatoRellenado>> filaToFiltroDatos = sortedFiltroDatos.stream()
            .collect(Collectors.groupingBy(FiltroDatoRellenado::getFila));

        // Procesar cada fila en orden
        filaToFiltroDatos.keySet().stream().sorted().forEach(fila -> {
            List<FiltroDatoRellenado> filaDatos = filaToFiltroDatos.get(fila);

            // Para cada FiltroDatoRellenado en esta fila
            for (FiltroDatoRellenado filtroDato : filaDatos) {
                // Si ya procesamos este filtro, omitirlo (evitar duplicados)
                if (processedFiltroIds.contains(filtroDato.getFiltro().getId())) {
                    continue;
                }

                // Buscar atributos que coincidan con este filtro
                for (Atributo atributo : validAtributos) {
                    if (atributo.getFiltro().getId().equals(filtroDato.getFiltro().getId())) {
                        result.add(atributo);
                        // Marcar este filtro como procesado
                        processedFiltroIds.add(filtroDato.getFiltro().getId());
                        break;
                    }
                }
            }
        });

        // Agregar los atributos restantes ordenados alfabéticamente
        validAtributos.stream()
            .filter(a -> !processedFiltroIds.contains(a.getFiltro().getId()))
            .sorted((a1, a2) -> {
                String nombre1 = a1.getFiltro().getNombreFiltro();
                String nombre2 = a2.getFiltro().getNombreFiltro();
                return nombre1.compareToIgnoreCase(nombre2);
            })
            .forEach(result::add);

        return result;
    }

    /**
     * Fuerza una distribución uniforme de las columnas dinámicas
     * Este método implementa una lógica más robusta para asegurar distribución uniforme
     */
    private void forceUniformColumnDistribution() {
        // Obtener solo las columnas dinámicas (las que son redimensionables)
        List<TableColumn<Item, String>> dynamicColumns = tvItems.getColumns().stream()
            .filter(col -> col.isResizable())
            .filter(col -> col instanceof TableColumn)
            .map(col -> {
                @SuppressWarnings("unchecked")
                TableColumn<Item, String> typedCol = (TableColumn<Item, String>) col;
                return typedCol;
            })
            .collect(Collectors.toList());

        if (dynamicColumns.isEmpty()) {
            return;
        }

        // Obtener el ancho real actual de la tabla
        double actualTableWidth = tvItems.getWidth();
        if (actualTableWidth <= 0) {
            // Si no tenemos un ancho real, usar el del contenedor padre
            actualTableWidth = root.getWidth();
        }

        if (actualTableWidth <= 0) {
            return; // No podemos calcular sin un ancho válido
        }

        // Calcular el ancho ocupado por columnas fijas
        // Si las columnas no tienen ancho aún, usar sus anchos preferidos
        double fixedColumnsWidth =
            (marcaColumn.getWidth() > 0 ? marcaColumn.getWidth() : marcaColumn.getPrefWidth()) +
            (codCompuestoColumn.getWidth() > 0 ? codCompuestoColumn.getWidth() : codCompuestoColumn.getPrefWidth()) +
            (precioVentaBaseColumn.getWidth() > 0 ? precioVentaBaseColumn.getWidth() : precioVentaBaseColumn.getPrefWidth()) +
            (precioVentaPromocionColumn.getWidth() > 0 ? precioVentaPromocionColumn.getWidth() : precioVentaPromocionColumn.getPrefWidth()) +
            (precioVentaPublicoColumn.getWidth() > 0 ? precioVentaPublicoColumn.getWidth() : precioVentaPublicoColumn.getPrefWidth()) +
            (stockTotalColumn.getWidth() > 0 ? stockTotalColumn.getWidth() : stockTotalColumn.getPrefWidth()) +
            (ubicacionColumn.getWidth() > 0 ? ubicacionColumn.getWidth() : ubicacionColumn.getPrefWidth());

        // Reservar espacio para scrollbar y padding
        double reservedSpace = 20.0;

        // Calcular el ancho disponible para columnas dinámicas
        double availableWidth = actualTableWidth - fixedColumnsWidth - reservedSpace;

        // Asegurar que hay suficiente espacio
        if (availableWidth < dynamicColumns.size() * 80.0) {
            availableWidth = dynamicColumns.size() * 80.0; // Mínimo 80px por columna
        }

        // Calcular el ancho uniforme para cada columna dinámica
        double uniformWidth = availableWidth / dynamicColumns.size();

        // Aplicar el ancho uniforme a todas las columnas dinámicas
        for (TableColumn<Item, String> column : dynamicColumns) {
            column.setPrefWidth(uniformWidth);
            column.setMinWidth(80.0);
            column.setMaxWidth(Double.MAX_VALUE);
        }

        // Forzar actualización del layout
        tvItems.requestLayout();

        // Log solo información esencial para debugging
        if (log.isTraceEnabled()) {
            log.trace("Distribución uniforme aplicada: {} columnas dinámicas con ancho {} cada una (tabla: {}px, fijas: {}px)",
                     dynamicColumns.size(), uniformWidth, actualTableWidth, fixedColumnsWidth);
        }
    }

    /**
     * Programa la distribución uniforme con múltiples intentos para asegurar que se aplique correctamente
     */
    private void scheduleUniformDistribution() {
        // Primer intento inmediato
        runOnUiThread(this::forceUniformColumnDistribution);

        // Segundo intento después de un breve delay para asegurar que el layout esté estable
        runOnUiThread(() -> {
            Platform.runLater(() -> {
                Platform.runLater(this::forceUniformColumnDistribution);
            });
        });

        // Tercer intento con un delay mayor para casos donde el renderizado toma más tiempo
        runOnUiThread(() -> {
            Platform.runLater(() -> {
                Platform.runLater(() -> {
                    Platform.runLater(this::forceUniformColumnDistribution);
                });
            });
        });
    }

    /**
     * Calcula el ancho disponible para las columnas dinámicas
     * @return Ancho disponible en píxeles para distribuir entre columnas dinámicas
     */
    private double calculateAvailableWidthForDynamicColumns() {
        // Obtener el ancho total disponible
        double totalWidth = getEstimatedTableWidth();

        // Calcular el ancho ocupado por las columnas fijas
        double fixedColumnsWidth =
            marcaColumn.getPrefWidth() +           // 90.0
            codCompuestoColumn.getPrefWidth() +    // 108.0
            precioVentaBaseColumn.getPrefWidth() + // 54.0
            precioVentaPromocionColumn.getPrefWidth() + // 54.0
            precioVentaPublicoColumn.getPrefWidth() +   // 54.0
            stockTotalColumn.getPrefWidth() +      // 54.0
            ubicacionColumn.getPrefWidth();        // 180.0

        // Reservar espacio para scrollbar y padding (aproximadamente 20px)
        double reservedSpace = 20.0;

        // Calcular el ancho disponible para columnas dinámicas
        double availableWidth = totalWidth - fixedColumnsWidth - reservedSpace;

        // Asegurar que hay un mínimo de espacio disponible
        return Math.max(200.0, availableWidth);
    }

    /**
     * Estima el ancho total disponible para la tabla
     * @return Ancho estimado en píxeles
     */
    private double getEstimatedTableWidth() {
        // Si la tabla ya está visible y tiene un ancho, usamos ese valor
        if (tvItems.isVisible() && tvItems.getWidth() > 0) {
            return tvItems.getWidth();
        }

        // Si el contenedor raíz tiene un ancho, usamos ese valor
        if (root.getWidth() > 0) {
            return root.getWidth();
        }

        // Si no podemos obtener un valor real, usamos un valor estimado
        return 1000; // Valor por defecto
    }

    /**
     * Limpia los recursos al cerrar la vista
     */
    @Override
    public void onClose() {
        // Llamar al método onClose de la clase base para limpiar las suscripciones
        super.onClose();

        // Limpiar los items para liberar memoria
        if (tvItems != null) {
            tvItems.getItems().clear();
        }

        // Limpiar referencias a objetos
        producto = null;
        searchProductGui = null;

        // Registrar el cierre del controlador
        log.info("ProductoItemSearchedController cerrado y recursos liberados.");
    }


}
