package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.MainSaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.ToSaleGuiRelationDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.MainSaleGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import javafx.animation.PauseTransition;
import javafx.application.Platform;
import javafx.collections.ListChangeListener;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.util.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class MainSaleGuiController extends BaseController {

    private final MainSaleGuiService mainSaleGuiService;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;

    @FXML
    private AnchorPane apMainSale;
    @FXML
    private Button btnAddSale;
    @FXML
    private ProgressIndicator loadingIndicator;
    @FXML
    private StackPane spRootMainSale;
    @FXML
    private TabPane tpSales;

    // Identificador del MainSaleGuiDto
    private UUID mainSaleGuiId;
    // Estado actual recibido desde el servidor (DTO)
    private MainSaleGuiDto currentMainSaleGui;
    // Variables para manejo de reordenamiento (se mantiene para la lógica de reorder)

    private boolean ignoringTabChanges = false;
    private List<UUID> oldTabIds = new ArrayList<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private ScheduledFuture<?> reorderTask;
    private final AtomicReference<Tab> lastReorderedTab = new AtomicReference<>();
    private final AtomicReference<Integer> lastReorderedIndex = new AtomicReference<>();
    // Bandera para indicar que se espera un nuevo tab
    private boolean pendingNewTab = false;
    // Bandera para indicar que se debe hacer focus en txtCodigo del nuevo tab (para Ctrl+F1)
    private boolean pendingFocusOnNewTab = false;
    // Para rastrear selección pendiente por índice (para Ctrl+1-9)
    private Integer pendingTabIndexToSelect = null;

    // PauseTransition para debouncing de la acción de agregar SaleGui (400ms)
    private final PauseTransition addSaleDebounceTransition = new PauseTransition(Duration.millis(400));

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        tpSales.setTabDragPolicy(TabPane.TabDragPolicy.REORDER);
        tpSales.getTabs().addListener(this::handleTabsChange);
        // En vez de llamar directamente a handleAddSale(), se llama al método debouncedAddSale()
        btnAddSale.setOnAction(e -> debouncedAddSale());

        // Configuramos el listener para atajos de teclado globales
        spRootMainSale.setOnKeyPressed(this::handleGlobalKeyboardShortcuts);
        spRootMainSale.setFocusTraversable(true);
        spRootMainSale.requestFocus();
    }

    /**
     * Maneja los atajos de teclado globales para MainSaleGuiController.
     */
    private void handleGlobalKeyboardShortcuts(KeyEvent event) {
        // Ctrl+F1 para crear nueva pestaña de SaleGui y luego hacer focus en txtCodigo
        KeyCombination ctrlF1 = new KeyCodeCombination(KeyCode.F1, KeyCombination.CONTROL_DOWN);
        if (ctrlF1.match(event)) {
            handleAddSaleAndFocus(); // Crear nueva pestaña y hacer focus
            event.consume();
            return;
        }

        // Ctrl+0 para eliminar todos los tabs de SaleGui
        KeyCombination ctrl0 = new KeyCodeCombination(KeyCode.DIGIT0, KeyCombination.CONTROL_DOWN);
        if (ctrl0.match(event)) {
            handleRemoveAllSaleGuiTabs();
            event.consume();
            return;
        }

        // Ctrl+1 a Ctrl+9 para enfocar tabs específicos de SaleGui
        for (int i = 1; i <= 9; i++) {
            KeyCode digitKey = KeyCode.valueOf("DIGIT" + i);
            KeyCombination ctrlDigit = new KeyCodeCombination(digitKey, KeyCombination.CONTROL_DOWN);
            if (ctrlDigit.match(event)) {
                focusSaleGuiTab(i - 1); // Convertir a índice base 0
                event.consume();
                return;
            }
        }
    }

    /**
     * Método que utiliza un PauseTransition para hacer debounce de la acción de agregar SaleGui.
     */
    private void debouncedAddSale() {
        addSaleDebounceTransition.stop();
        addSaleDebounceTransition.setOnFinished(e -> handleAddSale());
        addSaleDebounceTransition.playFromStart();
    }

    /**
     * Configura el ID del MainSaleGuiDto y se suscribe a los cambios.
     */
    public void setMainSaleGuiId(UUID id) {
        this.mainSaleGuiId = id;
        log.info("[CLIENT] setMainSaleGuiId => {}", id);
        Disposable subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                mainSaleGuiService.subscribeToChanges(id),
                this::onMainSaleGuiUpdate,
                this::handleSubscriptionError,
                () -> log.debug("[CLIENT] Suscripción a MainSaleGuiDto completada.")
        );
        registerSubscription(subscription);
    }

    /**
     * Actualiza el estado actual con el DTO recibido y reconstruye la vista.
     */
    private void onMainSaleGuiUpdate(MainSaleGuiDto updated) {
        runOnUiThread(() -> {
            this.currentMainSaleGui = updated;
            log.debug("[CLIENT] Recibido MainSaleGuiDto: {}", updated);
            rebuildTabsFromServer();
        });
    }

    /**
     * Actualiza los tabs basándose en el conjunto de ToSaleGuiRelationDto ordenado por ordenPresentacion.
     * Optimizado para minimizar la recreación de tabs y componentes.
     */
    private void rebuildTabsFromServer() {
        if (currentMainSaleGui == null) return;
        ignoringTabChanges = true;
        int previousCount = tpSales.getTabs().size();

        // Ordenar las relaciones por ordenPresentacion
        List<ToSaleGuiRelationDto> sorted = new ArrayList<>(currentMainSaleGui.getSalesGui());
        sorted.sort(Comparator.comparing(
                ToSaleGuiRelationDto::getOrdenPresentacion,
                Comparator.nullsFirst(Comparator.naturalOrder())
        ));

        // Asignar órdenes a relaciones con orden nulo
        for (ToSaleGuiRelationDto rel : sorted) {
            if (rel.getOrdenPresentacion() == null) {
                log.warn("[CLIENT] Orden nulo detectado para SaleGui id={}. Asignando fallback.", rel.getSaleGuiId());
                rel.setOrdenPresentacion(sorted.indexOf(rel) + 1);
            }
        }

        // Obtener IDs válidos del servidor
        Set<UUID> validIds = sorted.stream()
                .map(ToSaleGuiRelationDto::getSaleGuiId)
                .collect(Collectors.toSet());

        // Crear un mapa de los tabs actuales para acceso rápido
        Map<UUID, Tab> currentTabs = new HashMap<>();
        for (Tab tab : tpSales.getTabs()) {
            ToSaleGuiRelationDto rel = (ToSaleGuiRelationDto) tab.getUserData();
            if (rel != null) {
                currentTabs.put(rel.getSaleGuiId(), tab);
            }
        }

        // Verificar si hay cambios en el conjunto de tabs o en su orden
        boolean tabsChanged = false;
        List<UUID> currentIds = new ArrayList<>(currentTabs.keySet());
        List<UUID> newIds = new ArrayList<>(validIds);

        // Si los conjuntos de IDs son diferentes, definitivamente hay cambios
        if (!new HashSet<>(currentIds).equals(new HashSet<>(newIds))) {
            tabsChanged = true;
            log.debug("[CLIENT] Conjunto de tabs cambiado: {} -> {}", currentIds, newIds);
        } else {
            // Verificar si el orden ha cambiado
            List<UUID> sortedIds = sorted.stream()
                    .map(ToSaleGuiRelationDto::getSaleGuiId)
                    .collect(Collectors.toList());

            if (!currentIds.equals(sortedIds)) {
                tabsChanged = true;
                log.debug("[CLIENT] Orden de tabs cambiado: {} -> {}", currentIds, sortedIds);
            }
        }

        // Si no hay cambios en los tabs ni en su orden, solo actualizamos los datos
        if (!tabsChanged) {
            log.debug("[CLIENT] No hay cambios en tabs ni en su orden, solo actualizando datos");
            for (ToSaleGuiRelationDto rel : sorted) {
                Tab tab = currentTabs.get(rel.getSaleGuiId());
                if (tab != null) {
                    // Actualizar solo los datos del tab
                    tab.setUserData(rel);
                    int orden = (rel.getOrdenPresentacion() != null) ? rel.getOrdenPresentacion() : (sorted.indexOf(rel) + 1);
                    tab.setText("Venta #" + orden);
                }
            }
        } else {
            // Hay cambios, necesitamos reorganizar los tabs
            log.debug("[CLIENT] Reorganizando tabs debido a cambios");

            // Eliminar tabs que ya no son válidos
            tpSales.getTabs().removeIf(tab -> {
                var rel = (ToSaleGuiRelationDto) tab.getUserData();
                return rel != null && !validIds.contains(rel.getSaleGuiId());
            });

            // Crear lista con el nuevo orden
            List<Tab> newOrder = new ArrayList<>();
            for (ToSaleGuiRelationDto rel : sorted) {
                Tab tab = currentTabs.get(rel.getSaleGuiId());
                if (tab == null) {
                    // Crear nuevo tab si no existe
                    tab = createTabForRelation(rel);
                    log.debug("[CLIENT] Creando nuevo tab para SaleGui id={}", rel.getSaleGuiId());
                } else {
                    // Actualizar datos del tab existente
                    tab.setUserData(rel);
                    int orden = (rel.getOrdenPresentacion() != null) ? rel.getOrdenPresentacion() : (sorted.indexOf(rel) + 1);
                    tab.setText("Venta #" + orden);
                }
                newOrder.add(tab);
            }

            // Actualizar el orden de los tabs
            tpSales.getTabs().setAll(newOrder);

            // Seleccionar el nuevo tab si corresponde
            if (pendingNewTab && newOrder.size() > previousCount) {
                Tab newTab = newOrder.get(newOrder.size() - 1);
                tpSales.getSelectionModel().select(newTab);

                // Si también se debe hacer focus en txtCodigo (Ctrl+F1)
                if (pendingFocusOnNewTab) {
                    Platform.runLater(() -> focusTabTxtCodigo(newTab));
                    pendingFocusOnNewTab = false;
                }

                pendingNewTab = false;
            }

            // Verificar si hay selección pendiente por índice (para Ctrl+1-9)
            if (pendingTabIndexToSelect != null && newOrder.size() > pendingTabIndexToSelect) {
                int targetIndex = pendingTabIndexToSelect;
                Platform.runLater(() -> {
                    Tab targetTab = tpSales.getTabs().get(targetIndex);
                    tpSales.getSelectionModel().select(targetTab);
                    focusTabTxtCodigo(targetTab);
                    log.debug("Seleccionado y enfocado tab por índice: {} ({})", targetIndex, targetTab.getText());
                });
                pendingTabIndexToSelect = null; // Limpiar la bandera
            }
        }

        // Actualizar la lista de IDs para comparaciones futuras
        oldTabIds = sorted.stream()
                .map(ToSaleGuiRelationDto::getSaleGuiId)
                .collect(Collectors.toList());

        // Asegurar que siempre haya al menos un SaleGui
        ensureAtLeastOneSaleGui();

        ignoringTabChanges = false;
    }

    /**
     * Busca un Tab cuyo userData (ToSaleGuiRelationDto) tenga saleGuiId igual al indicado.
     */
    private Tab findTabBySaleGuiId(UUID saleGuiId) {
        for (Tab t : tpSales.getTabs()) {
            var rel = (ToSaleGuiRelationDto) t.getUserData();
            if (rel != null && rel.getSaleGuiId().equals(saleGuiId)) {
                return t;
            }
        }
        return null;
    }

    /**
     * Crea un nuevo Tab para la relación, pasando el objeto ToSaleGuiRelationDto al SaleGuiController.
     */
    private Tab createTabForRelation(ToSaleGuiRelationDto rel) {
        Tab tab = new Tab("Venta #" + rel.getOrdenPresentacion());
        tab.setClosable(true);
        tab.setUserData(rel);
        Parent saleView = springFXMLLoader.load(FXMLEnum.SALE_GUI);
        SaleGuiController ctrl = springFXMLLoader.getController(saleView);
        ctrl.setToSaleGuiRelation(rel);

        // Configurar callback para cerrar el SaleGui cuando se complete una venta
        ctrl.setOnSaleGuiCloseRequested(() -> {
            log.info("Recibida solicitud de cierre del SaleGui desde SaleGuiController");
            closeSaleGuiTab(tab);
        });

        tab.setContent(saleView);
        tab.setOnCloseRequest(evt -> removeSaleFromServer(rel.getSaleGuiId(), evt::consume));
        return tab;
    }

    private void handleTabsChange(ListChangeListener.Change<? extends Tab> change) {
        if (ignoringTabChanges) return;
        List<UUID> beforeIds = new ArrayList<>(oldTabIds);
        while (change.next()) {
            log.debug("[CLIENT] TabChange => wasPermutated={}, removed={}, added={}",
                    change.wasPermutated(), change.getRemoved(), change.getAddedSubList());
        }
        List<UUID> afterIds = tpSales.getTabs().stream()
                .map(t -> ((ToSaleGuiRelationDto) t.getUserData()).getSaleGuiId())
                .collect(Collectors.toList());
        oldTabIds = afterIds;
        if (beforeIds.size() != afterIds.size()) return;
        if (!sameElements(beforeIds, afterIds)) return;
        if (!Objects.equals(beforeIds, afterIds)) {
            log.info("[CLIENT] Detectado reorder => antes={} | después={}", beforeIds, afterIds);
            detectLargestMove(beforeIds, afterIds);
            scheduleReorderDebounce();
        }
    }

    private boolean sameElements(List<UUID> a, List<UUID> b) {
        if (a.size() != b.size()) return false;
        var sa = new ArrayList<>(a);
        var sb = new ArrayList<>(b);
        Collections.sort(sa);
        Collections.sort(sb);
        return sa.equals(sb);
    }

    private void detectLargestMove(List<UUID> beforeIds, List<UUID> afterIds) {
        int largestDelta = 0;
        Tab tabWithLargestDelta = null;
        int finalIndex = -1;
        for (int i = 0; i < afterIds.size(); i++) {
            UUID newId = afterIds.get(i);
            int oldIndex = beforeIds.indexOf(newId);
            if (oldIndex != i) {
                int delta = Math.abs(oldIndex - i);
                if (delta > largestDelta) {
                    largestDelta = delta;
                    finalIndex = i;
                    tabWithLargestDelta = tpSales.getTabs().get(i);
                }
            }
        }
        if (tabWithLargestDelta != null && finalIndex != -1) {
            lastReorderedTab.set(tabWithLargestDelta);
            lastReorderedIndex.set(finalIndex);
        }
    }

    private void scheduleReorderDebounce() {
        if (reorderTask != null) {
            reorderTask.cancel(false);
        }
        reorderTask = scheduler.schedule(this::executeReorder, 200, TimeUnit.MILLISECONDS);
    }

    private void executeReorder() {
        Tab tab = lastReorderedTab.getAndSet(null);
        Integer finalIndex = lastReorderedIndex.getAndSet(null);
        if (tab == null || finalIndex == null) return;
        runOnUiThread(() -> {
            var rel = (ToSaleGuiRelationDto) tab.getUserData();
            if (rel == null) return;
            int realIndex = tpSales.getTabs().indexOf(tab);
            if (realIndex < 0) return;
            int finalPos = realIndex + 1;
            int oldPos = rel.getOrdenPresentacion();
            if (finalPos == oldPos) {
                log.debug("[CLIENT] No hubo cambio real de posición; se ignora reorder.");
                return;
            }
            UUID saleGuiId = rel.getSaleGuiId();
            log.info("[CLIENT] Reordenando SaleGui {} => newPos={}", saleGuiId, finalPos);
            mainSaleGuiService.reorderSaleGui(mainSaleGuiId, saleGuiId, finalPos)
                    .subscribe(
                            unused -> log.debug("[CLIENT] Reordenamiento en servidor OK"),
                            error -> runOnUiThread(() -> {
                                alertUtil.showError("No se pudo reordenar: " + error.getMessage());
                                rebuildTabsFromServer();
                            })
                    );
        });
    }

    private void removeSaleFromServer(UUID saleGuiId, Runnable onErrorCancelClose) {
        if (currentMainSaleGui == null) {
            onErrorCancelClose.run();
            return;
        }
        mainSaleGuiService.removeSaleGui(mainSaleGuiId, saleGuiId)
                .subscribe(
                        unused -> log.debug("[CLIENT] SaleGui {} removido OK", saleGuiId),
                        error -> runOnUiThread(() -> {
                            alertUtil.showError("No se pudo remover la venta: " + error.getMessage());
                            onErrorCancelClose.run();
                        })
                );
    }

    /**
     * Cierra un tab de SaleGui específico y asegura que siempre haya al menos uno.
     */
    private void closeSaleGuiTab(Tab tab) {
        if (tab == null) {
            log.warn("Intento de cerrar un tab nulo");
            return;
        }

        ToSaleGuiRelationDto rel = (ToSaleGuiRelationDto) tab.getUserData();
        if (rel == null) {
            log.warn("No se puede cerrar el tab: no tiene datos de relación");
            return;
        }

        log.info("Cerrando SaleGui tab para saleGuiId: {}", rel.getSaleGuiId());

        // Remover del servidor
        removeSaleFromServer(rel.getSaleGuiId(), () -> log.warn("Error al remover SaleGui del servidor, cancelando cierre"));

        // El tab se cerrará automáticamente cuando el servidor confirme la eliminación
        // a través de la actualización del MainSaleGuiDto
    }

    /**
     * Verifica si hay al menos un SaleGui y crea uno si es necesario.
     */
    private void ensureAtLeastOneSaleGui() {
        if (currentMainSaleGui == null || currentMainSaleGui.getSalesGui() == null ||
            currentMainSaleGui.getSalesGui().isEmpty()) {

            log.info("No hay SaleGuis disponibles, creando uno nuevo automáticamente");
            handleAddSale();
        }
    }

    /**
     * Maneja la acción de agregar un nuevo SaleGui.
     * Se activa la bandera pendingNewTab para que, al recibir la actualización del servidor,
     * se seleccione automáticamente el nuevo tab.
     * (Este método se invoca mediante el debounce implementado en debouncedAddSale())
     */
    private void handleAddSale() {
        pendingNewTab = true;
        createSingleSaleGuiTab("Nuevo SaleGui");
    }

    /**
     * Maneja la acción de agregar un nuevo SaleGui y hacer focus en txtCodigo.
     * Usado específicamente para el atajo Ctrl+F1.
     */
    private void handleAddSaleAndFocus() {
        pendingNewTab = true;
        pendingFocusOnNewTab = true; // Bandera adicional para hacer focus después de crear
        createSingleSaleGuiTab(
                "Nuevo SaleGui para Ctrl+F1",
                (Void unused) -> log.debug("[CLIENT] Nuevo SaleGui creado OK para Ctrl+F1"),
                error -> {
                    pendingFocusOnNewTab = false; // Limpiar bandera en caso de error
                    runOnUiThread(() -> alertUtil.showError("No se pudo agregar la venta: " + error.getMessage()));
                }
        );
    }

    /**
     * Crea tabs de SaleGui hasta llegar al índice especificado.
     * Utiliza programación reactiva para crear múltiples tabs secuencialmente.
     * @param targetIndex Índice objetivo (base 0) hasta el cual crear tabs
     */
    private void createSaleGuiTabsUpToIndex(int targetIndex) {
        if (mainSaleGuiId == null) {
            alertUtil.showError("No hay mainSaleGuiId configurado.");
            return;
        }

        int currentTabCount = tpSales.getTabs().size();
        int tabsToCreate = (targetIndex + 1) - currentTabCount;

        if (tabsToCreate <= 0) {
            // Ya tenemos suficientes tabs, solo hacer focus
            focusExistingSaleGuiTab(targetIndex);
            return;
        }

        log.debug("Creando {} tabs de SaleGui para llegar al índice {}", tabsToCreate, targetIndex);

        // Marcar que queremos seleccionar el tab en el índice objetivo
        pendingTabIndexToSelect = targetIndex;

        // Crear los tabs faltantes secuencialmente
        createSaleGuiTabsSequentially(tabsToCreate, 0);
    }

    /**
     * Crea tabs de SaleGui secuencialmente usando programación reactiva.
     * @param remainingTabs Número de tabs restantes por crear
     * @param currentIteration Iteración actual (para logging)
     */
    private void createSaleGuiTabsSequentially(int remainingTabs, int currentIteration) {
        if (remainingTabs <= 0) {
            log.debug("Todos los tabs de SaleGui han sido creados exitosamente");
            return;
        }

        log.debug("Creando tab de SaleGui {} de {} faltantes", currentIteration + 1, remainingTabs + currentIteration);

        pendingNewTab = true; // Marcar que esperamos un nuevo tab
        createSingleSaleGuiTab(
                "Tab de SaleGui " + (currentIteration + 1),
                (Void response) -> {
                    log.debug("Tab de SaleGui {} creado exitosamente", currentIteration + 1);
                    // Crear el siguiente tab si es necesario
                    createSaleGuiTabsSequentially(remainingTabs - 1, currentIteration + 1);
                },
                error -> {
                    log.error("Error al crear tab de SaleGui {}: {}", currentIteration + 1, error.getMessage());
                    // Limpiar las banderas si hay error
                    pendingTabIndexToSelect = null;
                    pendingNewTab = false;
                    runOnUiThread(() ->
                            alertUtil.showError("No se pudo crear el tab " + (currentIteration + 1) + ": " + error.getMessage())
                    );
                }
        );
    }

    /**
     * Método genérico para crear un solo SaleGuiTab.
     * @param logContext Contexto para logging
     */
    private void createSingleSaleGuiTab(String logContext) {
        createSingleSaleGuiTab(
                logContext,
                (Void unused) -> log.debug("[CLIENT] {} creado OK", logContext),
                error -> runOnUiThread(() -> alertUtil.showError("No se pudo agregar la venta: " + error.getMessage()))
        );
    }

    /**
     * Método genérico para crear un solo SaleGuiTab con callbacks personalizados.
     * @param logContext Contexto para logging
     * @param onSuccess Callback de éxito
     * @param onError Callback de error
     */
    private void createSingleSaleGuiTab(String logContext,
                                      java.util.function.Consumer<Void> onSuccess,
                                      java.util.function.Consumer<Throwable> onError) {
        if (mainSaleGuiId == null) {
            alertUtil.showError("No hay mainSaleGuiId configurado.");
            return;
        }

        log.debug("[CLIENT] Solicitando creación de {}", logContext);
        subscribeOnBoundedElastic(
                mainSaleGuiService.createNewSaleGui(mainSaleGuiId),
                onSuccess,
                onError
        );
    }

    /**
     * Enfoca un tab de SaleGui existente en el índice especificado.
     * @param tabIndex Índice del tab (base 0) a enfocar
     */
    private void focusExistingSaleGuiTab(int tabIndex) {
        try {
            Platform.runLater(() -> {
                // Seleccionar el tab
                Tab targetTab = tpSales.getTabs().get(tabIndex);
                tpSales.getSelectionModel().select(targetTab);

                // Hacer focus en txtCodigo del tab seleccionado
                focusTabTxtCodigo(targetTab);
                log.debug("Enfocado txtCodigo del tab {} (posición {})", targetTab.getText(), tabIndex + 1);
            });
        } catch (Exception e) {
            log.error("Error al enfocar tab de SaleGui existente en posición {}: {}", tabIndex + 1, e.getMessage(), e);
            alertUtil.showError("Error al enfocar el tab de venta: " + e.getMessage());
        }
    }

    /**
     * Enfoca el txtCodigo del SaleController en el tab de SaleGui especificado.
     * Si el tab no existe, crea automáticamente los tabs faltantes hasta llegar al índice solicitado.
     * @param tabIndex Índice del tab (base 0) a enfocar
     */
    private void focusSaleGuiTab(int tabIndex) {
        if (tabIndex < 0 || tabIndex > 8) { // Máximo 9 tabs (0-8)
            log.warn("Índice de tab fuera de rango válido: {} (válido: 0-8)", tabIndex);
            alertUtil.showError("Posición de tab no válida: " + (tabIndex + 1) + " (máximo 9)");
            return;
        }

        // Si no hay tabs o el índice solicitado no existe, crear los tabs faltantes
        int currentTabCount = tpSales.getTabs().size();
        if (currentTabCount == 0 || tabIndex >= currentTabCount) {
            log.debug("Tab en posición {} no existe (disponibles: {}), creando tabs faltantes", tabIndex + 1, currentTabCount);
            createSaleGuiTabsUpToIndex(tabIndex);
            return; // El focus se hará automáticamente cuando se cree el tab
        }

        try {
            Platform.runLater(() -> {
                // Seleccionar el tab
                Tab targetTab = tpSales.getTabs().get(tabIndex);
                tpSales.getSelectionModel().select(targetTab);

                // Obtener el controlador del SaleGui
                Parent tabContent = (Parent) targetTab.getContent();
                if (tabContent != null) {
                    SaleGuiController saleGuiController = springFXMLLoader.getController(tabContent);
                    if (saleGuiController != null && saleGuiController.getCurrentSaleController() != null) {
                        // Enfocar el txtCodigo del SaleController
                        if (saleGuiController.getCurrentSaleController().getTxtCodigo() != null) {
                            saleGuiController.getCurrentSaleController().getTxtCodigo().requestFocus();
                            log.debug("Enfocado txtCodigo del tab {} (posición {})", targetTab.getText(), tabIndex + 1);
                        } else {
                            log.warn("No se pudo acceder al txtCodigo del tab en posición {}", tabIndex + 1);
                        }
                    } else {
                        log.warn("No se pudo obtener el SaleController del tab en posición {}", tabIndex + 1);
                    }
                } else {
                    log.warn("Contenido del tab en posición {} es nulo", tabIndex + 1);
                }
            });
        } catch (Exception e) {
            log.error("Error al enfocar tab de SaleGui en posición {}: {}", tabIndex + 1, e.getMessage(), e);
            alertUtil.showError("Error al enfocar el tab de venta: " + e.getMessage());
        }
    }

    /**
     * Enfoca el txtCodigo del tab especificado.
     * @param tab El tab cuyo txtCodigo se debe enfocar
     */
    private void focusTabTxtCodigo(Tab tab) {
        if (tab == null) {
            log.warn("No se puede enfocar txtCodigo: tab es nulo");
            return;
        }

        try {
            // Obtener el controlador del SaleGui
            Parent tabContent = (Parent) tab.getContent();
            if (tabContent != null) {
                SaleGuiController saleGuiController = springFXMLLoader.getController(tabContent);
                if (saleGuiController != null && saleGuiController.getCurrentSaleController() != null) {
                    // Enfocar el txtCodigo del SaleController
                    if (saleGuiController.getCurrentSaleController().getTxtCodigo() != null) {
                        saleGuiController.getCurrentSaleController().getTxtCodigo().requestFocus();
                        log.debug("Enfocado txtCodigo del tab {}", tab.getText());
                    } else {
                        log.warn("No se pudo acceder al txtCodigo del tab {}", tab.getText());
                    }
                } else {
                    log.warn("No se pudo obtener el SaleController del tab {}", tab.getText());
                }
            } else {
                log.warn("Contenido del tab {} es nulo", tab.getText());
            }
        } catch (Exception e) {
            log.error("Error al enfocar txtCodigo del tab {}: {}", tab.getText(), e.getMessage(), e);
        }
    }

    /**
     * Enfoca el txtCodigo del último tab de SaleGui (el más reciente).
     */
    private void focusLastSaleGuiTab() {
        if (tpSales.getTabs().isEmpty()) {
            log.warn("No hay tabs de SaleGui disponibles para enfocar");
            return;
        }

        try {
            // Enfocar el último tab (el más reciente)
            int lastIndex = tpSales.getTabs().size() - 1;
            Tab lastTab = tpSales.getTabs().get(lastIndex);

            // Seleccionar el tab si no está seleccionado
            if (tpSales.getSelectionModel().getSelectedItem() != lastTab) {
                tpSales.getSelectionModel().select(lastTab);
            }

            focusTabTxtCodigo(lastTab);
        } catch (Exception e) {
            log.error("Error al enfocar el último tab de SaleGui: {}", e.getMessage(), e);
        }
    }

    /**
     * Elimina todos los tabs de SaleGui. Se creará uno automáticamente por la lógica existente.
     */
    private void handleRemoveAllSaleGuiTabs() {
        if (mainSaleGuiId == null) {
            alertUtil.showError("MainSaleGuiId no configurado.");
            return;
        }

        if (tpSales.getTabs().isEmpty()) {
            log.debug("No hay tabs de SaleGui para eliminar");
            return;
        }

        try {
            log.info("Eliminando todos los tabs de SaleGui (total: {})", tpSales.getTabs().size());

            // Usar el nuevo endpoint que elimina todos de una vez para evitar deadlocks
            mainSaleGuiService.removeAllSaleGuis(mainSaleGuiId)
                    .subscribe(
                            unused -> log.info("[CLIENT] Todos los SaleGuis eliminados OK"),
                            error -> {
                                log.error("[CLIENT] Error al eliminar todos los SaleGuis", error);
                                runOnUiThread(() ->
                                    alertUtil.showError("Error al eliminar todos los tabs de venta: " + error.getMessage())
                                );
                            }
                    );

            log.info("Solicitud de eliminación de todos los tabs enviada al servidor");
        } catch (Exception e) {
            log.error("Error al eliminar todos los tabs de SaleGui: {}", e.getMessage(), e);
            alertUtil.showError("Error al eliminar los tabs de venta: " + e.getMessage());
        }
    }

    private void handleSubscriptionError(Throwable error) {
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de MainSaleGui: " + error.getMessage()));
        log.error("[CLIENT] Error en la suscripción de MainSaleGuiDto: {}", error.getMessage(), error);
    }

    @Override
    public void onClose() {
        super.onClose();
        if (!scheduler.isShutdown()) {
            scheduler.shutdownNow();
        }
        log.info("[CLIENT] MainSaleGuiController cerrado.");
    }
}
