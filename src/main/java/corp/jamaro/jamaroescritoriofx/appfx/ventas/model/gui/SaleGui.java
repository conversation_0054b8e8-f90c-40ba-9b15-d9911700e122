package corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui;

import corp.jamaro.jamaroescritoriofx.appfx.model.collaborative.CollaborativeRoom;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class SaleGui {
    private UUID id;

    private CollaborativeRoom collaborativeRoom;

    private Set<SearchProductGui> searchProducts;
    private Sale sale;
    private Instant createdAt;
}
