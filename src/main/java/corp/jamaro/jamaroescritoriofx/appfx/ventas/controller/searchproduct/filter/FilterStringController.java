package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.filter;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoBusqueda;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextFormatter;
import javafx.scene.layout.AnchorPane;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class FilterStringController extends BaseController {

    @FXML
    private AnchorPane anchorPaneRoot;

    @FXML
    private CustomTextField txtStringData;

    // ComboBox para mostrar el tipo de búsqueda
    private ComboBox<String> cmbTipoBusqueda;

    // Callback para manejar actualizaciones de datos (será llamado desde SearchFilterController)
    @Setter
    private Consumer<FiltroDatoRellenado> onDataUpdate;

    // Campo para almacenar el valor anterior para comparación
    private FiltroDatoRellenado previousValue;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Permite que el campo se expanda horizontalmente.
        txtStringData.setMaxWidth(Double.MAX_VALUE);

        // Configuramos el formateador para restringir caracteres de entrada
        configureStringFormatter();

        // Actualiza el dato al presionar ENTER.
        txtStringData.setOnAction(event -> updateFiltroDatoRellenado());
        // Actualiza también al perder el foco.
        txtStringData.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                updateFiltroDatoRellenado();
            }
        });

        // Inicializar el ComboBox para tipoBusqueda
        initializeTipoBusquedaComboBox();
    }

    /**
     * Inicializa el ComboBox para el tipo de búsqueda con los símbolos correspondientes.
     */
    private void initializeTipoBusquedaComboBox() {
        cmbTipoBusqueda = new ComboBox<>();
        cmbTipoBusqueda.getItems().addAll("=", "*", "!", "|");

        // Aplicar estilo compacto tipo label
        cmbTipoBusqueda.getStyleClass().add("filter-combo-box");

        // Deshabilitar focus traversal para que no sea accesible via Tab
        cmbTipoBusqueda.setFocusTraversable(false);

        // Listener para cambios en el ComboBox
        cmbTipoBusqueda.valueProperty().addListener((obs, oldVal, newVal) -> {
            updateFiltroDatoRellenado();
        });
    }

    /**
     * Configura el formateador para permitir solo caracteres válidos al inicio.
     */
    private void configureStringFormatter() {
        UnaryOperator<TextFormatter.Change> filter = change -> {
            String newText = change.getControlNewText();

            // Permitir texto vacío
            if (newText.isEmpty()) {
                return change;
            }

            // Permitir operadores de búsqueda (=, *, !, |) al inicio seguidos de cualquier texto
            // O permitir texto que no empiece con caracteres especiales no válidos
            // Patrones válidos: "texto", "=texto", "*texto", "!texto", "|texto"
            // Patrones inválidos: "<texto", ">texto", "#texto", "@texto", etc.
            if (newText.matches("[=*!|].*") || newText.matches("[^=*!|<>#@$%^&()\\[\\]{}\\\\/:;\"'`~].*")) {
                return change;
            }

            return null; // Rechazar el cambio
        };

        txtStringData.setTextFormatter(new TextFormatter<>(filter));
    }

    /**
     * Método reusable que recibe un FiltroDatoRellenado.
     * Solo acepta Filtros del tipo CADENA_TEXTO.
     * Usa el nombreFiltro del Filtro para setearlo en txtStringData.setLeft
     * y usa el dato para el txtStringData.setText.
     * Cuando tipoBusqueda es diferente de null, agrega el combo box en txtStringData.setRight.
     */
    public void setFiltroDatoRellenado(FiltroDatoRellenado filtroDatoRellenado) {
        // Validamos que el filtro sea del tipo CADENA_TEXTO (permitimos null para compatibilidad)
        if (filtroDatoRellenado != null && filtroDatoRellenado.getFiltro() != null && 
            filtroDatoRellenado.getFiltro().getTipo() != null && 
            filtroDatoRellenado.getFiltro().getTipo() != TipoFiltro.CADENA_TEXTO) {
            throw new IllegalArgumentException("FilterStringController solo puede recibir Filtros del tipo CADENA_TEXTO. Tipo recibido: " + filtroDatoRellenado.getFiltro().getTipo());
        }

        // Configuramos el texto del campo
        String dato = filtroDatoRellenado != null ? filtroDatoRellenado.getDato() : null;
        txtStringData.setText(dato != null ? dato : "");

        // Almacenamos el valor inicial para comparación
        this.previousValue = copyFiltroDatoRellenado(filtroDatoRellenado);

        // Configuramos el label izquierdo con el nombre del filtro
        if (filtroDatoRellenado != null && filtroDatoRellenado.getFiltro() != null && 
            filtroDatoRellenado.getFiltro().getNombreFiltro() != null) {
            Label leftLabel = new Label(filtroDatoRellenado.getFiltro().getNombreFiltro());

            // Agregamos funcionalidad de doble click para limpiar dato y tipoBusqueda
            leftLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    clearDatoAndTipoBusqueda();
                }
            });

            txtStringData.setLeft(leftLabel);
        } else {
            txtStringData.setLeft(null);
        }

        // Configuramos el ComboBox derecho si tipoBusqueda no es null
        if (filtroDatoRellenado != null && filtroDatoRellenado.getTipoBusqueda() != null) {
            String symbol = getTipoBusquedaSymbol(filtroDatoRellenado.getTipoBusqueda());
            cmbTipoBusqueda.setValue(symbol);
            txtStringData.setRight(cmbTipoBusqueda);
        } else {
            cmbTipoBusqueda.setValue(null);
            txtStringData.setRight(null);
        }
    }

    /**
     * Convierte TipoBusqueda a su símbolo correspondiente.
     */
    private String getTipoBusquedaSymbol(TipoBusqueda tipoBusqueda) {
        if (tipoBusqueda == null) return null;

        switch (tipoBusqueda) {
            case EXACTA: return "=";
            case CONTIENE: return "*";
            case DIFERENTE_DE: return "!";
            case EMPIEZA: return "|";
            default: return null;
        }
    }

    /**
     * Convierte símbolo a TipoBusqueda correspondiente.
     */
    private TipoBusqueda getSymbolTipoBusqueda(String symbol) {
        if (symbol == null) return null;

        switch (symbol) {
            case "=": return TipoBusqueda.EXACTA;
            case "*": return TipoBusqueda.CONTIENE;
            case "!": return TipoBusqueda.DIFERENTE_DE;
            case "|": return TipoBusqueda.EMPIEZA;
            default: return null;
        }
    }

    /**
     * Método para manejar actualizaciones de datos.
     * Normaliza el valor y llama al callback si hay cambios en dato o tipoBusqueda.
     * Tras la actualización, posiciona el caret al final del texto.
     */
    private void updateFiltroDatoRellenado() {
        if (previousValue == null) return;

        String newDato = txtStringData.getText();
        // Normalizamos: si es null o solo espacios se trata como null; de lo contrario se quitan espacios laterales.
        String normalizedNewDato = (newDato != null && !newDato.trim().isEmpty()) ? newDato.trim() : null;
        String normalizedOldDato = (previousValue.getDato() != null && !previousValue.getDato().trim().isEmpty()) ? previousValue.getDato().trim() : null;

        // Obtenemos el nuevo tipoBusqueda del ComboBox
        TipoBusqueda newTipoBusqueda = getSymbolTipoBusqueda(cmbTipoBusqueda.getValue());
        TipoBusqueda oldTipoBusqueda = previousValue.getTipoBusqueda();

        // Verificamos si hubo cambios en dato o tipoBusqueda
        boolean datoChanged = !Objects.equals(normalizedNewDato, normalizedOldDato);
        boolean tipoBusquedaChanged = !Objects.equals(newTipoBusqueda, oldTipoBusqueda);

        if (datoChanged || tipoBusquedaChanged) {
            // Creamos un nuevo FiltroDatoRellenado con los datos actualizados
            FiltroDatoRellenado updatedFiltroDato = copyFiltroDatoRellenado(previousValue);
            updatedFiltroDato.setDato(normalizedNewDato);
            updatedFiltroDato.setTipoBusqueda(newTipoBusqueda);

            // Actualizamos el valor anterior
            this.previousValue = copyFiltroDatoRellenado(updatedFiltroDato);

            // Llamamos al callback si está configurado
            if (onDataUpdate != null) {
                onDataUpdate.accept(updatedFiltroDato);
            }

            // Posicionamos el caret al final del texto
            runOnUiThread(this::positionCaretAtEnd);
        }
    }

    /**
     * Crea una copia de FiltroDatoRellenado para evitar referencias compartidas.
     */
    private FiltroDatoRellenado copyFiltroDatoRellenado(FiltroDatoRellenado original) {
        if (original == null) return null;

        FiltroDatoRellenado copy = new FiltroDatoRellenado();
        copy.setId(original.getId());
        copy.setFiltro(original.getFiltro());
        copy.setFila(original.getFila());
        copy.setColumna(original.getColumna());
        copy.setDato(original.getDato());
        copy.setTipoBusqueda(original.getTipoBusqueda());
        return copy;
    }

    /**
     * Limpia el dato y el tipoBusqueda, estableciendo ambos como null.
     * Se ejecuta cuando se hace doble click en el label izquierdo.
     */
    private void clearDatoAndTipoBusqueda() {
        if (previousValue == null) return;

        // Limpiamos el campo de texto
        txtStringData.setText("");

        // Limpiamos el ComboBox
        cmbTipoBusqueda.setValue(null);

        // Removemos el ComboBox del lado derecho
        txtStringData.setRight(null);

        // Creamos un nuevo FiltroDatoRellenado con datos limpiados
        FiltroDatoRellenado clearedFiltroDato = copyFiltroDatoRellenado(previousValue);
        clearedFiltroDato.setDato(null);
        clearedFiltroDato.setTipoBusqueda(null);

        // Actualizamos el valor anterior
        this.previousValue = copyFiltroDatoRellenado(clearedFiltroDato);

        // Llamamos al callback si está configurado
        if (onDataUpdate != null) {
            onDataUpdate.accept(clearedFiltroDato);
        }

        // Posicionamos el caret al final del texto
        runOnUiThread(this::positionCaretAtEnd);
    }

    /**
     * Método agnóstico para posicionar el caret al final del texto.
     */
    public void positionCaretAtEnd() {
        txtStringData.positionCaret(txtStringData.getText().length());
    }
}
