package corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
public class SearchProductGui {
    private UUID id;
    private String idGrupo;
    private String descripcion;
    private String codProductoOld;
    private String codFabricaOld;
    private String vehiculoSearch;
    private List<FiltroDatoRellenado> filtroDatoRellenados;

    private Instant createdAt = Instant.now();


}
