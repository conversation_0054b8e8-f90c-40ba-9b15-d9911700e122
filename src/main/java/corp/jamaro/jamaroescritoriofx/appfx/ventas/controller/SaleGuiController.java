package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.SearchProductGuiController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.SaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.ToSaleGuiRelationDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SaleGuiService;
import javafx.application.Platform;

import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.util.retry.Retry;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SaleGuiController extends BaseController {

    private final SaleGuiService saleGuiService;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;

    @FXML
    private AnchorPane anchorDetalles;
    @FXML
    private AnchorPane anchorSale;
    @FXML
    private Button btnAddSearchProduct;
    @FXML
    private Label lblIniciadaPor;
    @FXML
    private Label lblusersConnected;
    @FXML
    private ProgressIndicator loadingIndicator;
    @FXML
    private SplitPane splitDetallesDividerX;
    @FXML
    private SplitPane splitSaleDividerY;
    @FXML
    private StackPane stackPaneSale;
    @FXML
    private TabPane tabPaneSearchProduct;

    // Datos iniciales
    private ToSaleGuiRelationDto currentRelation;
    private UUID saleGuiId; // ID del SaleGui (tipo UUID)
    private UUID currentSaleId; // Para rastrear si el saleId ha cambiado (tipo UUID según SaleGuiDto.saleId)
    private SaleController currentSaleController; // Referencia al controlador actual

    // Mapa: searchProductId -> Tab para evitar crear duplicados
    private final ConcurrentHashMap<UUID, Tab> searchProductTabs = new ConcurrentHashMap<>();
    // Registro previo de IDs para comparar en la siguiente actualización
    private Set<UUID> oldSearchProductIds = new HashSet<>();

    // Para rastrear el último tab creado y seleccionarlo automáticamente
    // Puede ser UUID (para seleccionar por ID) o Integer (para seleccionar por índice)
    private Object pendingTabToSelect = null;

    private Disposable subscription;

    // Callback para notificar al controlador padre cuando se debe cerrar este SaleGui
    private Runnable onSaleGuiCloseRequested;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        btnAddSearchProduct.setOnAction(e -> handleAddSearchProduct());

        // Configurar atajos de teclado globales para toda la ventana de SaleGui
        stackPaneSale.setOnKeyPressed(this::handleGlobalKeyboardShortcuts);

        // Asegurar que el StackPane pueda recibir eventos de teclado
        stackPaneSale.setFocusTraversable(true);

        // Agregar un simple listener para el movimiento del divisor del SplitPane
        // Esto ayudará a mantener la visibilidad de los elementos seleccionados
        splitSaleDividerY.getDividers().get(0).positionProperty().addListener((obs, oldVal, newVal) -> {
            // Cuando el divisor termina de moverse, forzar la actualización de los ListViews
            if (currentSaleController != null) {
                Platform.runLater(() -> {
                    // Forzar actualización de los ListViews en SaleController
                    // Only refresh bienServicioCargados as bienServicioDevueltos has been removed
                    if (currentSaleController.getBienServicioCargados() != null) {
                        currentSaleController.getBienServicioCargados().refresh();
                    }
                });
            }
        });
    }

    public void setToSaleGuiRelation(ToSaleGuiRelationDto rel) {
        this.currentRelation = rel;
        this.saleGuiId = rel.getSaleGuiId();
        if (rel.getDividerX() != null) {
            splitDetallesDividerX.setDividerPositions(rel.getDividerX());
        }
        if (rel.getDividerY() != null) {
            splitSaleDividerY.setDividerPositions(rel.getDividerY());
        }
        subscribeToSaleGuiChanges();
    }

    /**
     * Establece el callback para notificar cuando se debe cerrar este SaleGui.
     */
    public void setOnSaleGuiCloseRequested(Runnable callback) {
        this.onSaleGuiCloseRequested = callback;
    }

    /**
     * Solicita el cierre de este SaleGui notificando al controlador padre.
     */
    private void requestSaleGuiClosure() {
        log.info("Solicitando cierre del SaleGui con id: {}", saleGuiId);
        if (onSaleGuiCloseRequested != null) {
            onSaleGuiCloseRequested.run();
        } else {
            log.warn("No hay callback configurado para cerrar el SaleGui");
        }
    }

    private void subscribeToSaleGuiChanges() {
        if (subscription != null) {
            subscription.dispose();
        }
        subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                saleGuiService.subscribeToChanges(saleGuiId)
                        .retryWhen(Retry.backoff(3, Duration.ofMillis(200))
                                .filter(err -> err.getMessage() != null
                                        && err.getMessage().toLowerCase().contains("deadlock"))
                                .doBeforeRetry(signal ->
                                        log.warn("Reintentando suscripción a SaleGuiDto (intento #{}) por: {}",
                                                signal.totalRetries() + 1, signal.failure().getMessage()))
                        ),
                this::onSaleGuiUpdate,
                this::handleSubscriptionError,
                () -> log.debug("[CLIENT] Suscripción a SaleGuiDto completada.")
        );
        registerSubscription(subscription);
    }

    /**
     * Maneja las actualizaciones recibidas del servidor para el SaleGuiDto.
     * Optimizado para minimizar la recreación de componentes UI.
     */
    private void onSaleGuiUpdate(SaleGuiDto dto) {
        runOnUiThread(() -> {
            // Actualizar labels solo si han cambiado
            updateLabelsIfChanged(dto);

            // Manejar cambios en el Sale asociado
            handleSaleChanges(dto);

            // Actualizar Tabs de SearchProduct (método ya optimizado)
            updateSearchProductTabs(dto.getSearchProductIds());
        });
    }

    /**
     * Actualiza las etiquetas de información solo si los datos han cambiado.
     * Ahora obtiene la información de usuarios conectados desde el DTO.
     */
    private void updateLabelsIfChanged(SaleGuiDto dto) {
        // Actualizar iniciadaPor solo si ha cambiado
        if (!Objects.equals(lblIniciadaPor.getText(), dto.getIniciadaPor())) {
            lblIniciadaPor.setText(dto.getIniciadaPor());
        }

        // Actualizar usuarios conectados solo si han cambiado
        String newOnlineUsers = "";
        if (dto.getUsersConnected() != null) {
            newOnlineUsers = dto.getUsersConnected().stream()
                    .filter(user -> Boolean.TRUE.equals(user.getIsOnline()))
                    .map(SaleGuiDto.ConnectedUserDto::getUsername)
                    .collect(Collectors.joining(", "));
        }

        if (!Objects.equals(lblusersConnected.getText(), newOnlineUsers)) {
            lblusersConnected.setText(newOnlineUsers);
        }
    }

    /**
     * Maneja los cambios en el Sale asociado al SaleGui.
     */
    private void handleSaleChanges(SaleGuiDto dto) {
        // Verificar si el saleId ha cambiado o si es la primera carga
        boolean saleIdChanged = (currentSaleId == null && dto.getSaleId() != null) ||
                               (currentSaleId != null && !currentSaleId.equals(dto.getSaleId()));

        // Cargar la vista de Sale solo si el saleId ha cambiado o si no existe un controlador actual
        if (dto.getSaleId() != null && (saleIdChanged || currentSaleController == null)) {
            log.info("[CLIENT] Cargando nueva vista de Sale para saleId={} (anterior={})",
                    dto.getSaleId(), currentSaleId);

            // Actualizar el saleId actual
            currentSaleId = dto.getSaleId();

            // Cargar la nueva vista
            Parent saleView = springFXMLLoader.load(FXMLEnum.SALE);
            currentSaleController = (SaleController) springFXMLLoader.getController(saleView);
            currentSaleController.setSaleId(dto.getSaleId());

            // Configurar callback para cerrar el SaleGui cuando se complete una venta
            currentSaleController.setOnSaleGuiCloseRequested(() -> {
                log.info("Recibida solicitud de cierre del SaleGui desde SaleController");
                requestSaleGuiClosure();
            });

            // Actualizar la UI
            anchorSale.getChildren().clear();
            anchorSale.getChildren().add(saleView);
            AnchorPane.setTopAnchor(saleView, 0.0);
            AnchorPane.setBottomAnchor(saleView, 0.0);
            AnchorPane.setLeftAnchor(saleView, 0.0);
            AnchorPane.setRightAnchor(saleView, 0.0);

            // Actualizar la referencia al SaleController en todos los SearchProductGuiController existentes
            updateSaleControllerReferences();
        } else if (dto.getSaleId() != null && currentSaleController != null && !saleIdChanged) {
            // El saleId no ha cambiado, pero podría haber actualizaciones en el modelo
            // No es necesario hacer nada aquí, ya que el SaleController tiene su propia suscripción
            log.debug("[CLIENT] No se recarga la vista de Sale, saleId no ha cambiado: {}", dto.getSaleId());
        } else if (dto.getSaleId() == null && currentSaleId != null) {
            // El saleId se ha eliminado
            log.info("[CLIENT] Eliminando vista de Sale, saleId es nulo");
            currentSaleId = null;
            currentSaleController = null;
            anchorSale.getChildren().clear();
        }
    }

    /**
     * Actualiza la referencia al SaleController en todos los SearchProductGuiController existentes.
     * Optimizado para proporcionar más información de depuración y evitar operaciones innecesarias.
     */
    private void updateSaleControllerReferences() {
        if (currentSaleController == null) {
            log.debug("[CLIENT] No hay SaleController para actualizar referencias");
            return;
        }

        int updatedCount = 0;
        for (Tab tab : tabPaneSearchProduct.getTabs()) {
            // Necesitamos hacer un cast a Parent ya que tab.getContent() devuelve Node
            Parent tabContent = (Parent) tab.getContent();
            if (tabContent != null) {
                SearchProductGuiController spController = springFXMLLoader.getController(tabContent);
                if (spController != null) {
                    UUID spId = (UUID) tab.getUserData();
                    log.debug("[CLIENT] Actualizando referencia a SaleController en SearchProductGuiController para tab {}", spId);
                    spController.setSaleController(currentSaleController);
                    updatedCount++;
                }
            }
        }

        log.debug("[CLIENT] Actualizadas {} referencias a SaleController en SearchProductGuiControllers", updatedCount);
    }

    /**
     * Actualiza (crea/elimina) los tabs de SearchProductGui en función de la lista proveniente del servidor.
     * Además, reordena y actualiza los títulos de cada tab para que muestren "Busqueda " seguido de la posición.
     * Optimizado para minimizar la recreación de tabs y componentes.
     */
    private void updateSearchProductTabs(List<UUID> newList) {
        if (newList == null) {
            newList = Collections.emptyList();
        }

        // Crear un conjunto para búsquedas más eficientes
        Set<UUID> newSet = new HashSet<>(newList);

        // 1) Si NO hay cambios en el conjunto de IDs, solo actualizamos los títulos si es necesario
        if (newSet.equals(oldSearchProductIds)) {
            // Verificar si el orden ha cambiado
            boolean orderChanged = false;
            List<UUID> currentOrder = tabPaneSearchProduct.getTabs().stream()
                    .map(tab -> (UUID) tab.getUserData())
                    .toList();

            if (!currentOrder.equals(newList)) {
                log.debug("[CLIENT] Orden de searchProductIds cambiado, actualizando títulos");
                orderChanged = true;
            }

            // Si el orden no ha cambiado, asegurar que haya al menos uno y salir
            if (!orderChanged) {
                log.debug("[CLIENT] No hay cambios en searchProductIds ni en su orden => verificando que haya al menos uno.");
                ensureAtLeastOneSearchProduct();
                return;
            }

            // Si solo cambió el orden, actualizamos los títulos
            List<Tab> currentTabs = tabPaneSearchProduct.getTabs();
            for (int i = 0; i < currentTabs.size(); i++) {
                currentTabs.get(i).setText("Busqueda " + (i + 1));
            }
            // Asegurar que haya al menos uno después de actualizar títulos
            ensureAtLeastOneSearchProduct();
            return;
        }

        log.info("[CLIENT] searchProductIds cambiado => antes={} | ahora={}", oldSearchProductIds, newSet);

        // Identificar qué tabs se deben eliminar y cuáles agregar
        Set<UUID> toRemove = new HashSet<>(oldSearchProductIds);
        toRemove.removeAll(newSet); // IDs que estaban antes pero ya no están

        Set<UUID> toAdd = new HashSet<>(newSet);
        toAdd.removeAll(oldSearchProductIds); // IDs nuevos que no estaban antes

        // Actualizar el conjunto de IDs para la próxima comparación
        oldSearchProductIds = newSet;

        // 2) Remover tabs que ya no existen en la nueva lista (más eficiente que removeIf)
        if (!toRemove.isEmpty()) {
            log.debug("[CLIENT] Removiendo tabs: {}", toRemove);
            List<Tab> tabsToRemove = new ArrayList<>();
            for (Tab tab : tabPaneSearchProduct.getTabs()) {
                UUID spId = (UUID) tab.getUserData();
                if (toRemove.contains(spId)) {
                    tabsToRemove.add(tab);
                    searchProductTabs.remove(spId);
                }
            }
            tabPaneSearchProduct.getTabs().removeAll(tabsToRemove);
        }

        // 3) Crear tabs nuevos para IDs que no teníamos
        if (!toAdd.isEmpty()) {
            log.debug("[CLIENT] Agregando nuevos tabs: {}", toAdd);
            for (UUID spId : newList) { // Usamos newList para mantener el orden
                if (toAdd.contains(spId)) {
                    Tab tab = createSearchProductTab(spId);
                    searchProductTabs.put(spId, tab);
                    tabPaneSearchProduct.getTabs().add(tab);
                }
            }
        }

        // 4) Reordenar tabs según el orden de la lista (si es necesario)
        if (!toAdd.isEmpty() || !toRemove.isEmpty() || !tabPaneSearchProduct.getTabs().stream()
                .map(tab -> (UUID) tab.getUserData())
                .toList()
                .equals(newList)) {

            log.debug("[CLIENT] Reordenando tabs según el orden del servidor");

            // Crear un mapa de los tabs actuales para acceso rápido
            Map<UUID, Tab> currentTabs = new HashMap<>();
            for (Tab tab : tabPaneSearchProduct.getTabs()) {
                UUID spId = (UUID) tab.getUserData();
                currentTabs.put(spId, tab);
            }

            // Crear lista con el nuevo orden
            List<Tab> newOrder = new ArrayList<>();
            for (UUID spId : newList) {
                Tab tab = currentTabs.get(spId);
                if (tab != null) {
                    newOrder.add(tab);
                }
            }

            // Actualizar el orden de los tabs
            tabPaneSearchProduct.getTabs().setAll(newOrder);
        }

        // 5) Actualizar títulos de todos los tabs según su posición (posición 1-based)
        List<Tab> currentTabs = tabPaneSearchProduct.getTabs();
        for (int i = 0; i < currentTabs.size(); i++) {
            currentTabs.get(i).setText("Busqueda " + (i + 1));
        }

        // 6) Seleccionar automáticamente el nuevo tab si hay uno pendiente
        selectPendingTabIfNeeded(toAdd, newList);

        // 7) Asegurar que siempre haya al menos un SearchProductGui
        ensureAtLeastOneSearchProduct();
    }

    /**
     * Selecciona automáticamente el nuevo tab si hay uno pendiente de selección.
     */
    private void selectPendingTabIfNeeded(Set<UUID> newlyAddedIds, List<UUID> allIds) {
        log.debug("[CLIENT] selectPendingTabIfNeeded: pendingTabToSelect={}, newlyAddedIds={}",
                pendingTabToSelect, newlyAddedIds);

        if (pendingTabToSelect == null) {
            log.debug("[CLIENT] No hay tab pendiente de selección");
            return; // No hay tab pendiente de selección
        }

        // Verificar si pendingTabToSelect es un índice (Integer)
        if (pendingTabToSelect instanceof Integer) {
            int targetIndex = (Integer) pendingTabToSelect;
            log.debug("[CLIENT] Selección pendiente por índice: {}", targetIndex);

            // Verificar si ya tenemos suficientes tabs para el índice objetivo
            if (tabPaneSearchProduct.getTabs().size() > targetIndex) {
                log.info("[CLIENT] Seleccionando tab por índice: {}", targetIndex);
                selectTabByIndex(targetIndex);
            } else {
                log.debug("[CLIENT] Aún no hay suficientes tabs para el índice {}, esperando más tabs", targetIndex);
                return; // No limpiar el flag, seguir esperando
            }
        } else {
            // Comportamiento original para UUIDs
            if (!newlyAddedIds.isEmpty()) {
                // Encontrar el último tab agregado en el orden de la lista
                UUID lastAddedId = null;
                for (UUID id : allIds) {
                    if (newlyAddedIds.contains(id)) {
                        lastAddedId = id;
                    }
                }

                if (lastAddedId != null) {
                    log.info("[CLIENT] Seleccionando automáticamente el nuevo tab: {}", lastAddedId);
                    selectTabById(lastAddedId);
                } else {
                    log.warn("[CLIENT] No se pudo encontrar el último tab agregado");
                }
            } else {
                log.debug("[CLIENT] No hay nuevos tabs para seleccionar");
            }
        }

        // Limpiar el flag de selección pendiente
        pendingTabToSelect = null;
        log.debug("[CLIENT] Flag de selección pendiente limpiado");
    }

    /**
     * Selecciona un tab específico por su ID y hace focus en txtSearch.
     */
    private void selectTabById(UUID tabId) {
        log.debug("[CLIENT] Intentando seleccionar tab con ID: {}", tabId);
        Platform.runLater(() -> {
            boolean found = false;
            for (Tab tab : tabPaneSearchProduct.getTabs()) {
                UUID spId = (UUID) tab.getUserData();
                if (tabId.equals(spId)) {
                    tabPaneSearchProduct.getSelectionModel().select(tab);
                    log.info("[CLIENT] ✅ Tab seleccionado exitosamente: {} ({})", tab.getText(), tabId);

                    // Hacer focus en txtSearch del tab seleccionado
                    focusTabTxtSearch(tab);
                    found = true;
                    break;
                }
            }
            if (!found) {
                log.warn("[CLIENT] ❌ No se encontró tab con ID: {}", tabId);
            }
        });
    }

    /**
     * Selecciona un tab específico por su índice y hace focus en txtSearch.
     * @param tabIndex Índice del tab (base 0) a seleccionar
     */
    private void selectTabByIndex(int tabIndex) {
        log.debug("[CLIENT] Intentando seleccionar tab por índice: {}", tabIndex);
        Platform.runLater(() -> {
            if (tabIndex >= 0 && tabIndex < tabPaneSearchProduct.getTabs().size()) {
                Tab targetTab = tabPaneSearchProduct.getTabs().get(tabIndex);
                tabPaneSearchProduct.getSelectionModel().select(targetTab);
                log.info("[CLIENT] ✅ Tab seleccionado exitosamente por índice: {} ({})", tabIndex, targetTab.getText());

                // Hacer focus en txtSearch del tab seleccionado
                focusTabTxtSearch(targetTab);
            } else {
                log.warn("[CLIENT] ❌ Índice de tab fuera de rango: {} (disponibles: {})", tabIndex, tabPaneSearchProduct.getTabs().size());
            }
        });
    }

    /**
     * Hace focus en el txtSearch de un tab específico.
     * @param tab El tab cuyo txtSearch se debe enfocar
     */
    private void focusTabTxtSearch(Tab tab) {
        try {
            // Obtener el controlador del SearchProductGui
            Parent tabContent = (Parent) tab.getContent();
            if (tabContent != null) {
                SearchProductGuiController spController = springFXMLLoader.getController(tabContent);
                if (spController != null) {
                    // Acceder al SearchFilterController y enfocar el txtSearch
                    if (spController.getFilterController() != null && spController.getFilterController().getTxtSearch() != null) {
                        spController.getFilterController().getTxtSearch().requestFocus();
                        log.debug("Enfocado txtSearch del tab {}", tab.getText());
                    } else {
                        log.warn("No se pudo acceder al txtSearch del tab {}", tab.getText());
                    }
                } else {
                    log.warn("No se pudo obtener el SearchProductGuiController del tab {}", tab.getText());
                }
            } else {
                log.warn("Contenido del tab {} es nulo", tab.getText());
            }
        } catch (Exception e) {
            log.error("Error al enfocar txtSearch del tab {}: {}", tab.getText(), e.getMessage(), e);
        }
    }

    /**
     * Crea un nuevo Tab para un SearchProductGui dado su UUID.
     * Optimizado para mejorar la velocidad de aparición del tab.
     */
    private Tab createSearchProductTab(UUID spId) {
        Tab tab = new Tab();

        // Configurar propiedades básicas del tab inmediatamente
        tab.setClosable(true);
        tab.setUserData(spId);
        tab.setText("Cargando..."); // Título temporal mientras se carga

        // Cargar el contenido de forma asíncrona para mejorar la velocidad
        Platform.runLater(() -> {
            try {
                Parent spView = springFXMLLoader.load(FXMLEnum.SEARCH_PRODUCT);
                SearchProductGuiController spController = springFXMLLoader.getController(spView);

                // Configurar el controlador
                spController.setSearchProductGuiId(spId);

                // Usar la referencia al SaleController actual si existe
                if (currentSaleController != null) {
                    log.debug("Conectando SearchProductGuiController con SaleController existente");
                    spController.setSaleController(currentSaleController);
                } else {
                    log.debug("No hay SaleController disponible para conectar con SearchProductGuiController");
                }

                // Establecer el contenido del tab
                tab.setContent(spView);

                // Actualizar el título del tab (se actualizará correctamente en updateSearchProductTabs)
                Platform.runLater(() -> {
                    // El título se actualizará automáticamente en el próximo ciclo de updateSearchProductTabs
                    int tabIndex = tabPaneSearchProduct.getTabs().indexOf(tab);
                    if (tabIndex >= 0) {
                        tab.setText("Busqueda " + (tabIndex + 1));
                    }
                });

            } catch (Exception e) {
                log.error("Error al cargar SearchProductGui para tab {}", spId, e);
                Platform.runLater(() -> {
                    tab.setText("Error");
                    alertUtil.showError("Error al cargar SearchProduct: " + e.getMessage());
                });
            }
        });

        // Al cerrar el tab, se solicita al servidor eliminar el SearchProductGui
        tab.setOnCloseRequest(event -> subscribeOnBoundedElastic(
                saleGuiService.deleteSearchProduct(saleGuiId, spId),
                unused -> log.debug("[CLIENT] SearchProductGui {} eliminado OK", spId),
                error -> {
                    log.error("[CLIENT] Error al eliminar SearchProductGui {}", spId, error);
                    runOnUiThread(() -> {
                        alertUtil.showError("Error al eliminar SearchProduct: " + error.getMessage());
                        event.consume(); // Evita el cierre si falla
                    });
                }
        ));

        return tab;
    }

    /**
     * Verifica si hay al menos un SearchProductGui y crea uno si es necesario.
     */
    private void ensureAtLeastOneSearchProduct() {
        if (tabPaneSearchProduct.getTabs().isEmpty()) {
            log.info("No hay SearchProductGuis disponibles, creando uno nuevo automáticamente");
            handleAddSearchProduct();
        }
    }

    /**
     * Maneja los atajos de teclado globales para toda la ventana de SaleGui.
     * Centraliza todos los atajos para que funcionen independientemente del foco.
     */
    private void handleGlobalKeyboardShortcuts(javafx.scene.input.KeyEvent event) {
        // F1 para enfocar txtCodigo del SaleController (solo F1, sin modificadores)
        KeyCombination f1 = new KeyCodeCombination(KeyCode.F1);
        if (f1.match(event) && !event.isControlDown() && !event.isAltDown() && !event.isShiftDown()) {
            focusSaleControllerField("txtCodigo");
            event.consume();
            return;
        }

        // Alt+C para enfocar txtDocumentoNombreRazon del SaleController
        KeyCombination altC = new KeyCodeCombination(KeyCode.C, KeyCombination.ALT_DOWN);
        if (altC.match(event)) {
            focusSaleControllerField("txtDocumentoNombreRazon");
            event.consume();
            return;
        }

        // Alt+S para cerrar este tab de SaleGui
        KeyCombination altS = new KeyCodeCombination(KeyCode.S, KeyCombination.ALT_DOWN);
        if (altS.match(event)) {
            requestSaleGuiClosure();
            event.consume();
            return;
        }

        // Alt+B para agregar nueva búsqueda
        KeyCombination altB = new KeyCodeCombination(KeyCode.B, KeyCombination.ALT_DOWN);
        if (altB.match(event)) {
            handleAddSearchProduct();
            event.consume();
            return;
        }

        // Alt+0 para eliminar todos los tabs de búsqueda
        KeyCombination alt0 = new KeyCodeCombination(KeyCode.DIGIT0, KeyCombination.ALT_DOWN);
        if (alt0.match(event)) {
            handleRemoveAllSearchProductTabs();
            event.consume();
            return;
        }

        // Alt+1 a Alt+9 para enfocar tabs específicos de búsqueda
        for (int i = 1; i <= 9; i++) {
            KeyCode digitKey = KeyCode.valueOf("DIGIT" + i);
            KeyCombination altDigit = new KeyCodeCombination(digitKey, KeyCombination.ALT_DOWN);
            if (altDigit.match(event)) {
                focusSearchProductTab(i - 1); // Convertir a índice base 0
                event.consume();
                return;
            }
        }

        // Delegar atajos relacionados con SaleController si existe
        if (currentSaleController != null) {
            // Alt+V para vender
            KeyCombination altV = new KeyCodeCombination(KeyCode.V, KeyCombination.ALT_DOWN);
            if (altV.match(event)) {
                delegateToSaleController("vender");
                event.consume();
                return;
            }

            // Alt+P para imprimir
            KeyCombination altP = new KeyCodeCombination(KeyCode.P, KeyCombination.ALT_DOWN);
            if (altP.match(event)) {
                delegateToSaleController("imprimir");
                event.consume();
                return;
            }

            // Alt+L para limpiar
            KeyCombination altL = new KeyCodeCombination(KeyCode.L, KeyCombination.ALT_DOWN);
            if (altL.match(event)) {
                delegateToSaleController("limpiar");
                event.consume();
                return;
            }

            // Ctrl+L para limpiar (mantener compatibilidad)
            KeyCombination ctrlL = new KeyCodeCombination(KeyCode.L, KeyCombination.CONTROL_DOWN);
            if (ctrlL.match(event)) {
                delegateToSaleController("limpiar");
                event.consume();
            }
        } else {
            log.debug("No hay SaleController disponible para procesar atajos de teclado");
        }
    }

    /**
     * Delega acciones específicas al SaleController actual.
     */
    private void delegateToSaleController(String action) {
        if (currentSaleController == null) {
            log.warn("No se puede ejecutar acción '{}': no hay SaleController disponible", action);
            alertUtil.showError("No hay una venta activa para realizar esta acción.");
            return;
        }

        try {
            switch (action.toLowerCase()) {
                case "vender":
                    log.debug("Delegando acción 'vender' al SaleController");
                    currentSaleController.handleBtnVender();
                    break;
                case "imprimir":
                    log.debug("Delegando acción 'imprimir' al SaleController");
                    currentSaleController.handleBtnImprimir();
                    break;
                case "limpiar":
                    log.debug("Delegando acción 'limpiar' al SaleController");
                    currentSaleController.handleBtnLimpiar();
                    break;
                default:
                    log.warn("Acción desconocida: {}", action);
                    break;
            }
        } catch (Exception e) {
            log.error("Error al delegar acción '{}' al SaleController: {}", action, e.getMessage(), e);
            alertUtil.showError("Error al ejecutar la acción: " + e.getMessage());
        }
    }

    /**
     * Enfoca un campo específico del SaleController actual.
     * @param fieldName Nombre del campo a enfocar ("txtCodigo" o "txtDocumentoNombreRazon")
     */
    private void focusSaleControllerField(String fieldName) {
        if (currentSaleController == null) {
            log.warn("No se puede enfocar campo '{}': no hay SaleController disponible", fieldName);
            alertUtil.showError("No hay una venta activa para enfocar el campo.");
            return;
        }

        try {
            Platform.runLater(() -> {
                switch (fieldName) {
                    case "txtCodigo":
                        if (currentSaleController.getTxtCodigo() != null) {
                            currentSaleController.getTxtCodigo().requestFocus();
                            log.debug("Enfocado campo txtCodigo del SaleController");
                        } else {
                            log.warn("Campo txtCodigo no disponible en SaleController");
                        }
                        break;
                    case "txtDocumentoNombreRazon":
                        if (currentSaleController.getTxtDocumentoNombreRazon() != null) {
                            currentSaleController.getTxtDocumentoNombreRazon().requestFocus();
                            log.debug("Enfocado campo txtDocumentoNombreRazon del SaleController");
                        } else {
                            log.warn("Campo txtDocumentoNombreRazon no disponible en SaleController");
                        }
                        break;
                    default:
                        log.warn("Campo desconocido: {}", fieldName);
                        break;
                }
            });
        } catch (Exception e) {
            log.error("Error al enfocar campo '{}' del SaleController: {}", fieldName, e.getMessage(), e);
            alertUtil.showError("Error al enfocar el campo: " + e.getMessage());
        }
    }

    /**
     * Enfoca el txtSearch del tab de búsqueda en la posición especificada.
     * Si el tab no existe, crea automáticamente los tabs faltantes hasta llegar al índice solicitado.
     * @param tabIndex Índice del tab (base 0) a enfocar
     */
    private void focusSearchProductTab(int tabIndex) {
        if (tabIndex < 0 || tabIndex > 8) { // Máximo 9 tabs (0-8)
            log.warn("Índice de tab fuera de rango válido: {} (válido: 0-8)", tabIndex);
            alertUtil.showError("Posición de tab no válida: " + (tabIndex + 1) + " (máximo 9)");
            return;
        }

        // Si no hay tabs o el índice solicitado no existe, crear los tabs faltantes
        int currentTabCount = tabPaneSearchProduct.getTabs().size();
        if (currentTabCount == 0 || tabIndex >= currentTabCount) {
            log.debug("Tab en posición {} no existe (disponibles: {}), creando tabs faltantes", tabIndex + 1, currentTabCount);
            createTabsUpToIndex(tabIndex);
            return; // El focus se hará automáticamente cuando se cree el tab
        }

        try {
            Platform.runLater(() -> {
                // Seleccionar el tab
                Tab targetTab = tabPaneSearchProduct.getTabs().get(tabIndex);
                tabPaneSearchProduct.getSelectionModel().select(targetTab);

                // Obtener el controlador del SearchProductGui
                Parent tabContent = (Parent) targetTab.getContent();
                if (tabContent != null) {
                    SearchProductGuiController spController = springFXMLLoader.getController(tabContent);
                    if (spController != null) {
                        // Acceder al SearchFilterController y enfocar el txtSearch
                        if (spController.getFilterController() != null && spController.getFilterController().getTxtSearch() != null) {
                            spController.getFilterController().getTxtSearch().requestFocus();
                            log.debug("Enfocado txtSearch del tab {} (posición {})", targetTab.getText(), tabIndex + 1);
                        } else {
                            log.warn("No se pudo acceder al txtSearch del tab en posición {}", tabIndex + 1);
                        }
                    } else {
                        log.warn("No se pudo obtener el SearchProductGuiController del tab en posición {}", tabIndex + 1);
                    }
                } else {
                    log.warn("Contenido del tab en posición {} es nulo", tabIndex + 1);
                }
            });
        } catch (Exception e) {
            log.error("Error al enfocar tab de búsqueda en posición {}: {}", tabIndex + 1, e.getMessage(), e);
            alertUtil.showError("Error al enfocar el tab de búsqueda: " + e.getMessage());
        }
    }

    /**
     * Elimina todos los tabs de búsqueda. Se creará uno automáticamente por la lógica existente.
     */
    private void handleRemoveAllSearchProductTabs() {
        if (saleGuiId == null) {
            alertUtil.showError("SaleGuiId no configurado.");
            return;
        }

        if (tabPaneSearchProduct.getTabs().isEmpty()) {
            log.debug("No hay tabs de búsqueda para eliminar");
            return;
        }

        try {
            log.info("Eliminando todos los tabs de búsqueda (total: {})", tabPaneSearchProduct.getTabs().size());

            // Usar el nuevo endpoint que elimina todos de una vez para evitar deadlocks
            subscribeOnBoundedElastic(
                    saleGuiService.deleteAllSearchProducts(saleGuiId),
                    unused -> log.info("[CLIENT] Todos los SearchProductGuis eliminados OK"),
                    error -> {
                        log.error("[CLIENT] Error al eliminar todos los SearchProductGuis", error);
                        runOnUiThread(() ->
                            alertUtil.showError("Error al eliminar todos los tabs de búsqueda: " + error.getMessage())
                        );
                    }
            );

            log.info("Solicitud de eliminación de todos los tabs de búsqueda enviada al servidor");
        } catch (Exception e) {
            log.error("Error al eliminar todos los tabs de búsqueda: {}", e.getMessage(), e);
            alertUtil.showError("Error al eliminar los tabs de búsqueda: " + e.getMessage());
        }
    }

    /**
     * Botón para crear un nuevo SearchProductGui en el SaleGui.
     */
    private void handleAddSearchProduct() {
        // Marcar que queremos seleccionar el próximo tab que se cree
        markNextTabForSelection();
        // Crear un solo tab
        createSingleSearchProductTab("Nuevo SearchProductGui");
    }

    /**
     * Crea tabs de SearchProduct hasta llegar al índice especificado.
     * Utiliza programación reactiva para crear múltiples tabs secuencialmente.
     * @param targetIndex Índice objetivo (base 0) hasta el cual crear tabs
     */
    private void createTabsUpToIndex(int targetIndex) {
        if (saleGuiId == null) {
            alertUtil.showError("SaleGuiId no configurado.");
            return;
        }

        int currentTabCount = tabPaneSearchProduct.getTabs().size();
        int tabsToCreate = (targetIndex + 1) - currentTabCount;

        if (tabsToCreate <= 0) {
            // Ya tenemos suficientes tabs, solo hacer focus
            focusExistingSearchProductTab(targetIndex);
            return;
        }

        log.debug("Creando {} tabs para llegar al índice {}", tabsToCreate, targetIndex);

        // Marcar que queremos seleccionar el tab en el índice objetivo
        pendingTabToSelect = targetIndex;

        // Crear los tabs faltantes secuencialmente
        createTabsSequentially(tabsToCreate, 0);
    }

    /**
     * Crea tabs secuencialmente usando programación reactiva.
     * @param remainingTabs Número de tabs restantes por crear
     * @param currentIteration Iteración actual (para logging)
     */
    private void createTabsSequentially(int remainingTabs, int currentIteration) {
        if (remainingTabs <= 0) {
            log.debug("Todos los tabs han sido creados exitosamente");
            return;
        }

        log.debug("Creando tab {} de {} faltantes", currentIteration + 1, remainingTabs + currentIteration);

        createSingleSearchProductTab(
                "Tab " + (currentIteration + 1),
                (Void response) -> {
                    log.debug("Tab {} creado exitosamente", currentIteration + 1);
                    // Crear el siguiente tab si es necesario
                    createTabsSequentially(remainingTabs - 1, currentIteration + 1);
                },
                error -> {
                    log.error("Error al crear tab {}: {}", currentIteration + 1, error.getMessage());
                    // Limpiar el flag si hay error
                    pendingTabToSelect = null;
                    runOnUiThread(() ->
                            alertUtil.showError("No se pudo crear el tab " + (currentIteration + 1) + ": " + error.getMessage())
                    );
                }
        );
    }

    /**
     * Método genérico para crear un solo SearchProductTab.
     * @param logContext Contexto para logging
     */
    private void createSingleSearchProductTab(String logContext) {
        createSingleSearchProductTab(
                logContext,
                (Void response) -> log.debug("[CLIENT] {} creado exitosamente", logContext),
                error -> {
                    log.error("[CLIENT] Error al crear {}", logContext, error);
                    // Limpiar el flag si hay error
                    pendingTabToSelect = null;
                    runOnUiThread(() ->
                            alertUtil.showError("No se pudo agregar SearchProduct: " + error.getMessage())
                    );
                }
        );
    }

    /**
     * Método genérico para crear un solo SearchProductTab con callbacks personalizados.
     * @param logContext Contexto para logging
     * @param onSuccess Callback de éxito
     * @param onError Callback de error
     */
    private void createSingleSearchProductTab(String logContext,
                                            java.util.function.Consumer<Void> onSuccess,
                                            java.util.function.Consumer<Throwable> onError) {
        if (saleGuiId == null) {
            alertUtil.showError("SaleGuiId no configurado.");
            return;
        }

        log.debug("[CLIENT] Solicitando creación de {}", logContext);
        subscribeOnBoundedElastic(
                saleGuiService.createSearchProduct(saleGuiId),
                onSuccess,
                onError
        );
    }

    /**
     * Enfoca un tab de búsqueda existente en el índice especificado.
     * @param tabIndex Índice del tab (base 0) a enfocar
     */
    private void focusExistingSearchProductTab(int tabIndex) {
        try {
            Platform.runLater(() -> {
                // Seleccionar el tab
                Tab targetTab = tabPaneSearchProduct.getTabs().get(tabIndex);
                tabPaneSearchProduct.getSelectionModel().select(targetTab);

                // Obtener el controlador del SearchProductGui
                Parent tabContent = (Parent) targetTab.getContent();
                if (tabContent != null) {
                    SearchProductGuiController spController = springFXMLLoader.getController(tabContent);
                    if (spController != null) {
                        // Acceder al SearchFilterController y enfocar el txtSearch
                        if (spController.getFilterController() != null && spController.getFilterController().getTxtSearch() != null) {
                            spController.getFilterController().getTxtSearch().requestFocus();
                            log.debug("Enfocado txtSearch del tab {} (posición {})", targetTab.getText(), tabIndex + 1);
                        } else {
                            log.warn("No se pudo acceder al txtSearch del tab en posición {}", tabIndex + 1);
                        }
                    } else {
                        log.warn("No se pudo obtener el SearchProductGuiController del tab en posición {}", tabIndex + 1);
                    }
                } else {
                    log.warn("Contenido del tab en posición {} es nulo", tabIndex + 1);
                }
            });
        } catch (Exception e) {
            log.error("Error al enfocar tab de búsqueda existente en posición {}: {}", tabIndex + 1, e.getMessage(), e);
            alertUtil.showError("Error al enfocar el tab de búsqueda: " + e.getMessage());
        }
    }

    /**
     * Marca que el próximo tab creado debe ser seleccionado automáticamente.
     * Como no conocemos el ID hasta que llegue del servidor, usamos un flag especial.
     */
    private void markNextTabForSelection() {
        // Usamos un UUID especial para indicar que queremos seleccionar el próximo tab nuevo
        pendingTabToSelect = UUID.fromString("00000000-0000-0000-0000-000000000001");
        log.debug("[CLIENT] Marcado para seleccionar el próximo tab creado");
    }

    /**
     * Getter para acceder al SaleController actual desde otros controladores.
     * @return El SaleController actual o null si no hay ninguno
     */
    public SaleController getCurrentSaleController() {
        return currentSaleController;
    }

    private void handleSubscriptionError(Throwable error) {
        log.error("[CLIENT] Error en la suscripción de SaleGuiDto: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de SaleGui: " + error.getMessage())
        );
    }

    @Override
    public void onClose() {
        super.onClose();
        if (subscription != null && !subscription.isDisposed()) {
            subscription.dispose();
        }
        log.info("[CLIENT] SaleGuiController cerrado.");
    }
}
