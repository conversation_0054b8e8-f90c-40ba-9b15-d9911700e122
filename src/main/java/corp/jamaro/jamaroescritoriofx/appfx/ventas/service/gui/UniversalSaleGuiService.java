package corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.UniversalSaleGuiDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Servicio cliente para interactuar con el servidor RSocket en operaciones
 * relacionadas con la UniversalSaleGui (ahora usando UniversalSaleGuiDto).
 *
 * Endpoints en el servidor:
 * <ul>
 *   <li>universalSaleGui.subscribe</li>
 *   <li>universalSaleGui.saveDto</li>
 *   <li>universalSaleGui.addAuditedMainSale</li>
 *   <li>universalSaleGui.removeAuditedMainSale</li>
 *   <li>universalSaleGui.saveGuiConfig</li>
 * </ul>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UniversalSaleGuiService {

    private static final String ROUTE_SUBSCRIBE          = "universalSaleGui.subscribe";
    private static final String ROUTE_ADD_AUDITED        = "universalSaleGui.addAuditedMainSale";
    private static final String ROUTE_REMOVE_AUDITED     = "universalSaleGui.removeAuditedMainSale";
    private static final String ROUTE_SAVE_GUI_CONFIG    = "universalSaleGui.saveGuiConfig";

    private final ConnectionService connectionService;

    /**
     * Se suscribe a los cambios de la UniversalSaleGui (en forma de DTO) para el usuario dado.
     *
     * @param userId Identificador del usuario.
     * @return Flux que emite UniversalSaleGuiDto con el estado actual y las actualizaciones.
     */
    public Flux<UniversalSaleGuiDto> subscribeToChanges(UUID userId) {
        log.debug("Suscribiéndose a cambios de UniversalSaleGuiDto para userId={}", userId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, userId, UniversalSaleGuiDto.class);
    }


    /**
     * Agrega el id de un MainSaleGui a la lista de auditoría.
     *
     * @param universalSaleGuiId Identificador del UniversalSaleGuiDto.
     * @param mainSaleGuiId      Identificador del MainSaleGui a agregar.
     * @return Mono con el UniversalSaleGuiDto actualizado.
     */
    public Mono<UniversalSaleGuiDto> addAuditedMainSale(UUID universalSaleGuiId, UUID mainSaleGuiId) {
        log.debug("Agregando MainSaleGuiId={} a la auditoría de universalSaleGuiId={}", mainSaleGuiId, universalSaleGuiId);
        var request = new AddAuditedMainSaleRequest(universalSaleGuiId, mainSaleGuiId);
        return connectionService.authenticatedRequest(ROUTE_ADD_AUDITED, request, UniversalSaleGuiDto.class);
    }

    /**
     * Remueve el id de un MainSaleGui de la lista de auditoría.
     *
     * @param universalSaleGuiId Identificador del UniversalSaleGuiDto.
     * @param mainSaleGuiId      Identificador del MainSaleGui a remover.
     * @return Mono con el UniversalSaleGuiDto actualizado.
     */
    public Mono<UniversalSaleGuiDto> removeAuditedMainSale(UUID universalSaleGuiId, UUID mainSaleGuiId) {
        log.debug("Removiendo MainSaleGuiId={} de la auditoría de universalSaleGuiId={}", mainSaleGuiId, universalSaleGuiId);
        var request = new RemoveAuditedMainSaleRequest(universalSaleGuiId, mainSaleGuiId);
        return connectionService.authenticatedRequest(ROUTE_REMOVE_AUDITED, request, UniversalSaleGuiDto.class);
    }

    /**
     * Actualiza la configuración (guiConfig) en el servidor y retorna el DTO actualizado.
     *
     * @param json JSON con la nueva configuración.
     * @return Mono con UniversalSaleGuiDto reflejando el nuevo estado.
     */
    public Mono<UniversalSaleGuiDto> saveGuiConfig(String json) {
        log.debug("Guardando configuración (guiConfig): {}", json);
        return connectionService.authenticatedRequest(ROUTE_SAVE_GUI_CONFIG, json, UniversalSaleGuiDto.class);
    }

    /**
     * Record para la petición de agregar un MainSaleGui a la auditoría.
     */
    public record AddAuditedMainSaleRequest(UUID universalSaleGuiId, UUID mainSaleGuiId) {}

    /**
     * Record para la petición de remover un MainSaleGui de la auditoría.
     */
    public record RemoveAuditedMainSaleRequest(UUID universalSaleGuiId, UUID mainSaleGuiId) {}
}
