package corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoBusqueda;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchProductGuiService {

    private final ConnectionService connectionService;

    // Rutas RSocket existentes
    private static final String ROUTE_SUBSCRIBE = "searchProductGui.subscribe";
    private static final String ROUTE_UPDATE_VEHICULO_SEARCH = "searchProductGui.update.vehiculoSearch";
    private static final String ROUTE_UPDATE_GRUPO = "searchProductGui.update.grupo";
    private static final String ROUTE_DUPLICATE_FILTRO_GROUP_ROW = "searchProductGui.duplicate.filtro-group-row";
    private static final String ROUTE_DELETE_FILTRO_GROUP_ROW = "searchProductGui.delete.filtro-group-row";
    private static final String ROUTE_UPDATE_FILTRO_DATO = "searchProductGui.update.filtro-dato";

    // Nuevas rutas para obtener productos e items
    private static final String ROUTE_GET_PRODUCT_SEARCHED = "searchProductGui.getProductSearched";
    private static final String ROUTE_GET_ITEMS_BY_PRODUCTO_ID = "searchProductGui.getItemsByProductoId";

    // Nueva ruta para obtener sugerencias de descripción
    private static final String ROUTE_PRODUCTO_DESCRIPCION_SUGERENCIAS = "searchProductGui.productoDescripcionSugerencias";

    // Nueva ruta para actualizar los comodines (descripcion, codProductoOld, codFabricaOld)
    private static final String ROUTE_UPDATE_COMODINES = "searchProductGui.update.comodines";
    private static final String ROUTE_CREATE_AND_SUBSCRIBE = "searchProductGui.createAndSubscribe";
    private static final String ROUTE_CLOSE_AND_DELETE = "searchProductGui.closeAndDelete";

    /**
     * Se suscribe a los cambios del SearchProductGui vía RSocket.
     *
     * @param id UUID del SearchProductGui.
     * @return Flux con el objeto inicial y sus actualizaciones.
     */
    public Flux<SearchProductGui> subscribeToChanges(UUID id) {
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, id, SearchProductGui.class);
    }

    /**
     * Obtiene los productos según la configuración del SearchProductGui.
     *
     * @param spg Configuración de búsqueda.
     * @return Flux de Producto.
     */
    public Flux<Producto> getProductsSearched(SearchProductGui spg) {
        if (spg == null) {
            spg = new SearchProductGui();
            log.warn("getProductsSearched: SearchProductGui era nulo, se creó uno nuevo");
        }

        log.info("Solicitando productos con: id={}, descripcion='{}', codProductoOld='{}', codFabricaOld='{}', vehiculoSearch='{}'",
                spg.getId(), spg.getDescripcion(), spg.getCodProductoOld(), spg.getCodFabricaOld(), spg.getVehiculoSearch());

        return connectionService.authenticatedSubscription(ROUTE_GET_PRODUCT_SEARCHED, spg, Producto.class)
                .doOnSubscribe(s -> log.debug("Suscripción a búsqueda de productos iniciada"))
                .doOnComplete(() -> log.debug("Búsqueda de productos completada"));
    }

    /**
     * Obtiene los Items asociados a un Producto.
     *
     * @param productoId Identificador del Producto.
     * @return Flux de Item.
     */
    public Flux<Item> getItemsByProductoId(UUID productoId) {
        return connectionService.authenticatedSubscription(ROUTE_GET_ITEMS_BY_PRODUCTO_ID, productoId, Item.class);
    }

    /**
     * Actualiza el campo vehiculoSearch de un SearchProductGui.
     *
     * @param id                 UUID del SearchProductGui.
     * @param nuevoVehiculoSearch Nuevo valor para vehiculoSearch.
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> updateVehiculoSearch(UUID id, String nuevoVehiculoSearch) {
        return connectionService.authenticatedRequest(
                ROUTE_UPDATE_VEHICULO_SEARCH,
                new UpdateVehiculoSearchRequest(id, nuevoVehiculoSearch),
                SearchProductGui.class
        );
    }

    /**
     * Actualiza el grupo asignado a un SearchProductGui.
     *
     * @param id      UUID del SearchProductGui.
     * @param idGrupo Nuevo id del grupo (puede ser null para limpiar la asignación).
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> updateGrupo(UUID id, String idGrupo) {
        return connectionService.authenticatedRequest(
                ROUTE_UPDATE_GRUPO,
                new UpdateGrupoRequest(id, idGrupo),
                SearchProductGui.class
        );
    }

    /**
     * Duplica una fila de filtros en un SearchProductGui.
     *
     * @param id       UUID del SearchProductGui.
     * @param rowIndex Índice de la fila a duplicar.
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> duplicateFiltroGroupRow(UUID id, int rowIndex) {
        return connectionService.authenticatedRequest(
                ROUTE_DUPLICATE_FILTRO_GROUP_ROW,
                new DuplicateFiltroGroupRowRequest(id, rowIndex),
                SearchProductGui.class
        );
    }

    /**
     * Actualiza un filtro específico en un SearchProductGui con los datos completos del FiltroDatoRellenado.
     * Este método unificado permite actualizar tanto el dato como el tipo de búsqueda en una sola operación,
     * eliminando la necesidad de métodos separados para cada campo.
     * 
     * Procesa caracteres especiales al inicio del dato:
     * - < establece tipoBusqueda como MENOR_QUE
     * - > establece tipoBusqueda como MAYOR_QUE
     * - = establece tipoBusqueda como EXACTA
     * - ! establece tipoBusqueda como DIFERENTE_DE
     * - * establece tipoBusqueda como CONTIENE
     *
     * @param id ID del SearchProductGui a actualizar
     * @param filtroDatoRellenado Objeto FiltroDatoRellenado con los datos actualizados (debe incluir fila, columna, dato y tipoBusqueda)
     * @return Mono con el SearchProductGui actualizado
     */
    public Mono<SearchProductGui> updateFiltroDato(UUID id, FiltroDatoRellenado filtroDatoRellenado) {
        // Procesar caracteres especiales en el dato
        FiltroDatoRellenado processedFiltroDato = processSpecialCharacters(filtroDatoRellenado);

        return connectionService.authenticatedRequest(
                ROUTE_UPDATE_FILTRO_DATO,
                new UpdateFiltroDatoRequest(id, processedFiltroDato),
                SearchProductGui.class
        );
    }

    /**
     * Obtiene sugerencias de descripción de Producto basadas en el SearchProductGui recibido.
     * Si spg.idGrupo está definido, se filtran las descripciones para ese grupo;
     * de lo contrario se devuelven todas las sugerencias.
     *
     * @param spg SearchProductGui con filtros (especialmente spg.descripcion y spg.idGrupo).
     * @return Flux de sugerencias de descripción (hasta 30 resultados).
     */
    public Flux<String> productoDescripcionSugerencias(SearchProductGui spg) {
        // Se envía el objeto completo al servidor, que aplicará el buildContainsAllRegex y el filtro de grupo
        return connectionService.authenticatedSubscription(
                ROUTE_PRODUCTO_DESCRIPCION_SUGERENCIAS,
                spg,
                String.class
        );
    }

    /**
     * Actualiza los campos comodín (descripcion, codProductoOld, codFabricaOld) de un SearchProductGui.
     * El servidor se encargará de interpretar los valores vacíos o nulos y limpiarlos.
     *
     * @param spg SearchProductGui con los nuevos valores para los campos comodín.
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> updateComodines(SearchProductGui spg) {
        log.info("Enviando actualización de comodines al servidor: id={}, descripcion='{}', codProductoOld='{}', codFabricaOld='{}'",
                spg.getId(), spg.getDescripcion(), spg.getCodProductoOld(), spg.getCodFabricaOld());

        return connectionService.authenticatedRequest(
                ROUTE_UPDATE_COMODINES,
                spg,
                SearchProductGui.class
        ).doOnSuccess(updatedGui -> {
            log.info("Comodines actualizados en el servidor: id={}, descripcion='{}', codProductoOld='{}', codFabricaOld='{}'",
                    updatedGui.getId(), updatedGui.getDescripcion(), updatedGui.getCodProductoOld(), updatedGui.getCodFabricaOld());
        });
    }

    /**
     * Elimina una fila de filtros en un SearchProductGui.
     * Solo permite eliminar filas si hay más de una fila de filtros.
     *
     * @param id       UUID del SearchProductGui.
     * @param rowIndex Índice de la fila a eliminar.
     * @return Mono del SearchProductGui actualizado.
     */
    public Mono<SearchProductGui> deleteFiltroGroupRow(UUID id, int rowIndex) {
        return connectionService.authenticatedRequest(
                ROUTE_DELETE_FILTRO_GROUP_ROW,
                new DeleteFiltroGroupRowRequest(id, rowIndex),
                SearchProductGui.class
        );
    }

    /**
     * Crea un nuevo SearchProductGui en el servidor y se suscribe automáticamente a sus actualizaciones.
     *
     * @return Flux que emite el SearchProductGui inicial y luego sus actualizaciones.
     */
    public Flux<SearchProductGui> createAndSubscribe() {
        log.info("Solicitando creación y suscripción de nuevo SearchProductGui");
        return connectionService.authenticatedSubscription(
                ROUTE_CREATE_AND_SUBSCRIBE,
                null,
                SearchProductGui.class
        );
    }

    /**
     * Elimina el SearchProductGui por su ID y cierra la suscripción en el servidor.
     *
     * @param id UUID del SearchProductGui a eliminar.
     * @return Mono que se completa cuando la operación es exitosa.
     */
    public Mono<Void> closeAndDelete(UUID id) {
        log.info("Solicitando eliminación y cierre de SearchProductGui con id: {}", id);
        return connectionService.authenticatedRequest(
                ROUTE_CLOSE_AND_DELETE,
                new CloseAndDeleteRequest(id),
                Void.class
        );
    }

    /**
     * Procesa caracteres especiales al inicio del campo dato de FiltroDatoRellenado.
     * Si el dato empieza con <, >, =, !, *, | elimina ese carácter y establece el tipoBusqueda correspondiente.
     * Si no empieza con esos caracteres, devuelve el FiltroDatoRellenado sin modificaciones.
     *
     * @param filtroDatoRellenado El objeto FiltroDatoRellenado a procesar
     * @return FiltroDatoRellenado procesado con el dato modificado y tipoBusqueda establecido si corresponde
     */
    private FiltroDatoRellenado processSpecialCharacters(FiltroDatoRellenado filtroDatoRellenado) {
        if (filtroDatoRellenado == null || filtroDatoRellenado.getDato() == null || filtroDatoRellenado.getDato().isEmpty()) {
            return filtroDatoRellenado;
        }

        String dato = filtroDatoRellenado.getDato();
        char firstChar = dato.charAt(0);

        // Crear una copia del objeto para modificar
        FiltroDatoRellenado processed = new FiltroDatoRellenado();
        processed.setId(filtroDatoRellenado.getId());
        processed.setFiltro(filtroDatoRellenado.getFiltro());
        processed.setFila(filtroDatoRellenado.getFila());
        processed.setColumna(filtroDatoRellenado.getColumna());
        processed.setTipoBusqueda(filtroDatoRellenado.getTipoBusqueda());

        switch (firstChar) {
            case '<':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.MENOR_QUE);
                break;
            case '>':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.MAYOR_QUE);
                break;
            case '=':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.EXACTA);
                break;
            case '!':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.DIFERENTE_DE);
                break;
            case '*':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.CONTIENE);
                break;
            case '|':
                processed.setDato(dato.substring(1));
                processed.setTipoBusqueda(TipoBusqueda.EMPIEZA);
                break;
            default:
                // Si no empieza con caracteres especiales, devolver sin modificaciones
                processed.setDato(dato);
                break;
        }

        return processed;
    }

    // Records para agrupar los parámetros de cada solicitud.
    public static record CloseAndDeleteRequest(UUID id) {}

    public static record UpdateVehiculoSearchRequest(UUID id, String vehiculoSearch) {}
    public static record UpdateGrupoRequest(UUID id, String idGrupo) {}
    public static record DuplicateFiltroGroupRowRequest(UUID id, int rowIndex) {}
    public static record DeleteFiltroGroupRowRequest(UUID id, int rowIndex) {}
    public static record UpdateFiltroDatoRequest(UUID id, FiltroDatoRellenado filtroDatoRellenado) {}
}
