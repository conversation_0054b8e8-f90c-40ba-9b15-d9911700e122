package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.filter;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoBusqueda;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextFormatter;
import javafx.scene.layout.AnchorPane;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.DecimalFormat;
import java.text.ParsePosition;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class FilterNumericController extends BaseController {

    @FXML
    private AnchorPane anchorPaneRoot;

    @FXML
    private CustomTextField txtNumericData;

    // ComboBox para mostrar el tipo de búsqueda
    private ComboBox<String> cmbTipoBusqueda;

    // Callback para manejar actualizaciones de datos (será llamado desde SearchFilterController)
    @Setter
    private Consumer<FiltroDatoRellenado> onDataUpdate;

    // Campo para almacenar el valor anterior para comparación
    private FiltroDatoRellenado previousValue;

    // Formateador para números decimales
    private final DecimalFormat decimalFormat = new DecimalFormat("#.##");

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Permite que el campo se expanda horizontalmente.
        txtNumericData.setMaxWidth(Double.MAX_VALUE);

        // Configuramos el formateador para solo permitir números
        configureNumericFormatter();

        // Actualiza el dato al presionar ENTER.
        txtNumericData.setOnAction(event -> updateFiltroDatoRellenado());
        // Actualiza también al perder el foco.
        txtNumericData.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                updateFiltroDatoRellenado();
            }
        });

        // Inicializar el ComboBox para tipoBusqueda
        initializeTipoBusquedaComboBox();
    }

    /**
     * Inicializa el ComboBox para el tipo de búsqueda con los símbolos correspondientes.
     */
    private void initializeTipoBusquedaComboBox() {
        cmbTipoBusqueda = new ComboBox<>();
        cmbTipoBusqueda.getItems().addAll("=", "<", ">");

        // Aplicar estilo compacto tipo label
        cmbTipoBusqueda.getStyleClass().add("filter-combo-box");

        // Deshabilitar focus traversal para que no sea accesible via Tab
        cmbTipoBusqueda.setFocusTraversable(false);

        // Listener para cambios en el ComboBox
        cmbTipoBusqueda.valueProperty().addListener((obs, oldVal, newVal) -> {
            updateFiltroDatoRellenado();
        });
    }

    /**
     * Configura el formateador para permitir solo entrada numérica.
     */
    private void configureNumericFormatter() {
        UnaryOperator<TextFormatter.Change> filter = change -> {
            String newText = change.getControlNewText();

            // Permitir texto vacío
            if (newText.isEmpty()) {
                return change;
            }

            // Permitir operadores de comparación (=, <, >) al inicio seguidos de números
            // Patrones válidos: "123", "=123", "<123", ">123", "-123", "=−123", etc.
            if (newText.matches("[=<>]?-?\\d*\\.?\\d*")) {
                // Verificar que no haya más de un punto decimal
                long dotCount = newText.chars().filter(ch -> ch == '.').count();
                if (dotCount <= 1) {
                    return change;
                }
            }

            return null; // Rechazar el cambio
        };

        txtNumericData.setTextFormatter(new TextFormatter<>(filter));
    }

    /**
     * Convierte TipoBusqueda a su símbolo correspondiente.
     */
    private String getTipoBusquedaSymbol(TipoBusqueda tipoBusqueda) {
        if (tipoBusqueda == null) return null;

        switch (tipoBusqueda) {
            case EXACTA: return "=";
            case MENOR_QUE: return "<";
            case MAYOR_QUE: return ">";
            default: return null;
        }
    }

    /**
     * Convierte símbolo a TipoBusqueda correspondiente.
     */
    private TipoBusqueda getSymbolTipoBusqueda(String symbol) {
        if (symbol == null) return null;

        switch (symbol) {
            case "=": return TipoBusqueda.EXACTA;
            case "<": return TipoBusqueda.MENOR_QUE;
            case ">": return TipoBusqueda.MAYOR_QUE;
            default: return null;
        }
    }

    /**
     * Método reusable que recibe un FiltroDatoRellenado.
     * Solo acepta Filtros del tipo NUMERICO.
     * Usa el nombreFiltro del Filtro para setearlo en txtNumericData.setLeft
     * y usa el dato para el txtNumericData.setText.
     * Cuando tipoBusqueda es diferente de null, agrega el combo box en txtNumericData.setRight.
     */
    public void setFiltroDatoRellenado(FiltroDatoRellenado filtroDatoRellenado) {
        // Validamos que el filtro sea del tipo NUMERICO (permitimos null para compatibilidad)
        if (filtroDatoRellenado != null && filtroDatoRellenado.getFiltro() != null && 
            filtroDatoRellenado.getFiltro().getTipo() != null && 
            filtroDatoRellenado.getFiltro().getTipo() != TipoFiltro.NUMERICO) {
            throw new IllegalArgumentException("FilterNumericController solo puede recibir Filtros del tipo NUMERICO. Tipo recibido: " + filtroDatoRellenado.getFiltro().getTipo());
        }

        // Configuramos el texto del campo
        String dato = filtroDatoRellenado != null ? filtroDatoRellenado.getDato() : null;
        txtNumericData.setText(dato != null ? dato : "");

        // Almacenamos el valor inicial para comparación
        this.previousValue = copyFiltroDatoRellenado(filtroDatoRellenado);

        // Configuramos el label izquierdo con el nombre del filtro
        if (filtroDatoRellenado != null && filtroDatoRellenado.getFiltro() != null && 
            filtroDatoRellenado.getFiltro().getNombreFiltro() != null) {
            Label leftLabel = new Label(filtroDatoRellenado.getFiltro().getNombreFiltro());

            // Agregamos funcionalidad de doble click para limpiar dato y tipoBusqueda
            leftLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    clearDatoAndTipoBusqueda();
                }
            });

            txtNumericData.setLeft(leftLabel);
        } else {
            txtNumericData.setLeft(null);
        }

        // Configuramos el ComboBox derecho si tipoBusqueda no es null
        if (filtroDatoRellenado != null && filtroDatoRellenado.getTipoBusqueda() != null) {
            String symbol = getTipoBusquedaSymbol(filtroDatoRellenado.getTipoBusqueda());
            cmbTipoBusqueda.setValue(symbol);
            txtNumericData.setRight(cmbTipoBusqueda);
        } else {
            cmbTipoBusqueda.setValue(null);
            txtNumericData.setRight(null);
        }
    }

    /**
     * Método para manejar actualizaciones de datos.
     * Normaliza el valor y llama al callback si hay cambios en dato o tipoBusqueda.
     * Tras la actualización, posiciona el caret al final del texto.
     */
    private void updateFiltroDatoRellenado() {
        if (previousValue == null) return;

        String newDato = txtNumericData.getText();
        // Normalizamos: si es null o solo espacios se trata como null; de lo contrario se quitan espacios laterales.
        String normalizedNewDato = (newDato != null && !newDato.trim().isEmpty()) ? newDato.trim() : null;
        String normalizedOldDato = (previousValue.getDato() != null && !previousValue.getDato().trim().isEmpty()) ? previousValue.getDato().trim() : null;

        // Validamos que el valor sea un número válido si no es null
        if (normalizedNewDato != null && !isValidNumber(normalizedNewDato)) {
            log.warn("Valor numérico inválido: {}", normalizedNewDato);
            // Restauramos el valor anterior
            txtNumericData.setText(previousValue.getDato() != null ? previousValue.getDato() : "");
            return;
        }

        // Obtenemos el nuevo tipoBusqueda del ComboBox
        TipoBusqueda newTipoBusqueda = getSymbolTipoBusqueda(cmbTipoBusqueda.getValue());
        TipoBusqueda oldTipoBusqueda = previousValue.getTipoBusqueda();

        // Verificamos si hubo cambios en dato o tipoBusqueda
        boolean datoChanged = !Objects.equals(normalizedNewDato, normalizedOldDato);
        boolean tipoBusquedaChanged = !Objects.equals(newTipoBusqueda, oldTipoBusqueda);

        if (datoChanged || tipoBusquedaChanged) {
            // Creamos un nuevo FiltroDatoRellenado con los datos actualizados
            FiltroDatoRellenado updatedFiltroDato = copyFiltroDatoRellenado(previousValue);
            updatedFiltroDato.setDato(normalizedNewDato);
            updatedFiltroDato.setTipoBusqueda(newTipoBusqueda);

            // Actualizamos el valor anterior
            this.previousValue = copyFiltroDatoRellenado(updatedFiltroDato);

            // Llamamos al callback si está configurado
            if (onDataUpdate != null) {
                onDataUpdate.accept(updatedFiltroDato);
            }

            // Posicionamos el caret al final del texto
            runOnUiThread(() -> positionCaretAtEnd());
        }
    }

    /**
     * Crea una copia de FiltroDatoRellenado para evitar referencias compartidas.
     */
    private FiltroDatoRellenado copyFiltroDatoRellenado(FiltroDatoRellenado original) {
        if (original == null) return null;

        FiltroDatoRellenado copy = new FiltroDatoRellenado();
        copy.setId(original.getId());
        copy.setFiltro(original.getFiltro());
        copy.setFila(original.getFila());
        copy.setColumna(original.getColumna());
        copy.setDato(original.getDato());
        copy.setTipoBusqueda(original.getTipoBusqueda());
        return copy;
    }

    /**
     * Valida si una cadena representa un número válido.
     * Considera que puede empezar con operadores de comparación (=, <, >).
     */
    private boolean isValidNumber(String value) {
        if (value == null || value.trim().isEmpty()) {
            return true; // null o vacío es válido
        }

        String trimmedValue = value.trim();

        // Si empieza con operadores de comparación, los removemos para validar solo la parte numérica
        if (trimmedValue.startsWith("=") || trimmedValue.startsWith("<") || trimmedValue.startsWith(">")) {
            trimmedValue = trimmedValue.substring(1);
        }

        // Si después de remover el operador queda vacío, es válido (ej: solo ">")
        if (trimmedValue.isEmpty()) {
            return true;
        }

        try {
            Double.parseDouble(trimmedValue);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Limpia el dato y el tipoBusqueda, estableciendo ambos como null.
     * Se ejecuta cuando se hace doble click en el label izquierdo.
     */
    private void clearDatoAndTipoBusqueda() {
        if (previousValue == null) return;

        // Limpiamos el campo de texto
        txtNumericData.setText("");

        // Limpiamos el ComboBox
        cmbTipoBusqueda.setValue(null);

        // Removemos el ComboBox del lado derecho
        txtNumericData.setRight(null);

        // Creamos un nuevo FiltroDatoRellenado con datos limpiados
        FiltroDatoRellenado clearedFiltroDato = copyFiltroDatoRellenado(previousValue);
        clearedFiltroDato.setDato(null);
        clearedFiltroDato.setTipoBusqueda(null);

        // Actualizamos el valor anterior
        this.previousValue = copyFiltroDatoRellenado(clearedFiltroDato);

        // Llamamos al callback si está configurado
        if (onDataUpdate != null) {
            onDataUpdate.accept(clearedFiltroDato);
        }

        // Posicionamos el caret al final del texto
        runOnUiThread(() -> positionCaretAtEnd());
    }

    /**
     * Método agnóstico para posicionar el caret al final del texto.
     */
    public void positionCaretAtEnd() {
        txtNumericData.positionCaret(txtNumericData.getText().length());
    }
}
