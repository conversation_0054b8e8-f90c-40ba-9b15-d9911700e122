package corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class UniversalSaleGui {
    private UUID id;
    private User user;

    private MainSaleGui mainSaleGui;

    private Set<UUID> auditingMainSales;// Admins y managers pueden ver el MainSale de otros

    private String guiConfig; // un json con las preferencias del usuario para su gui (color de usuario, tamaño letra, etc)

}
