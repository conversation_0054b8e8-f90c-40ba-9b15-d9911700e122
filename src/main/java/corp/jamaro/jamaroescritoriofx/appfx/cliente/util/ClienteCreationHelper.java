package corp.jamaro.jamaroescritoriofx.appfx.cliente.util;

import corp.jamaro.jamaroescritoriofx.appfx.cliente.controller.ClienteCreationController;
import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Helper class para facilitar el uso del componente ClienteCreationController.
 * Proporciona métodos estáticos para abrir diálogos de creación/edición de clientes.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ClienteCreationHelper {

    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;

    /**
     * Abre un diálogo para crear un nuevo cliente basado en texto de entrada
     * @param inputText Texto inicial (documento o nombre)
     * @param isNumeric Si el texto es numérico (documento) o no (nombre)
     * @param onClienteCreated Callback cuando se crea el cliente exitosamente
     * @param parentStage Stage padre para el diálogo modal
     */
    public void showCreateClienteDialog(String inputText, boolean isNumeric, 
                                      ClienteCreationController.ClienteCreatedHandler onClienteCreated,
                                      Stage parentStage) {
        try {
            // Cargar el FXML
            Parent root = springFXMLLoader.load("fxml/cliente/clienteCreation.fxml");
            ClienteCreationController controller = springFXMLLoader.getController(root);
            
            // Crear el stage del diálogo
            Stage dialogStage = new Stage();
            dialogStage.setTitle("Crear Cliente");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(parentStage);
            dialogStage.initStyle(StageStyle.DECORATED);
            dialogStage.setResizable(true);
            
            // Configurar la escena
            Scene scene = new Scene(root);
            dialogStage.setScene(scene);
            
            // Configurar el controlador
            controller.configureForNewCliente(inputText, isNumeric);
            
            // Configurar handlers
            controller.setClienteCreatedHandler((cliente, originalText) -> {
                log.info("Cliente creado exitosamente desde diálogo: {}", cliente.getId());
                dialogStage.close();
                if (onClienteCreated != null) {
                    onClienteCreated.handle(cliente, originalText);
                }
            });
            
            controller.setCancelHandler(() -> {
                log.debug("Usuario canceló la creación de cliente");
                dialogStage.close();
            });
            
            controller.setErrorHandler((errorMessage, error) -> {
                log.error("Error en diálogo de creación de cliente: {}", errorMessage, error);
                alertUtil.showError(errorMessage);
            });
            
            // Mostrar el diálogo
            dialogStage.showAndWait();
            
        } catch (Exception e) {
            log.error("Error al abrir diálogo de creación de cliente: {}", e.getMessage(), e);
            alertUtil.showError("Error al abrir el formulario de cliente: " + e.getMessage());
        }
    }

    /**
     * Abre un diálogo para editar un cliente existente
     * @param cliente Cliente a editar
     * @param onClienteUpdated Callback cuando se actualiza el cliente exitosamente
     * @param parentStage Stage padre para el diálogo modal
     */
    public void showEditClienteDialog(Cliente cliente,
                                    ClienteCreationController.ClienteUpdatedHandler onClienteUpdated,
                                    Stage parentStage) {
        try {
            // Cargar el FXML
            Parent root = springFXMLLoader.load("fxml/cliente/clienteCreation.fxml");
            ClienteCreationController controller = springFXMLLoader.getController(root);
            
            // Crear el stage del diálogo
            Stage dialogStage = new Stage();
            dialogStage.setTitle("Editar Cliente");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(parentStage);
            dialogStage.initStyle(StageStyle.DECORATED);
            dialogStage.setResizable(true);
            
            // Configurar la escena
            Scene scene = new Scene(root);
            dialogStage.setScene(scene);
            
            // Configurar el controlador
            controller.configureForEditCliente(cliente);
            
            // Configurar handlers
            controller.setClienteUpdatedHandler((updatedCliente) -> {
                log.info("Cliente actualizado exitosamente desde diálogo: {}", updatedCliente.getId());
                dialogStage.close();
                if (onClienteUpdated != null) {
                    onClienteUpdated.handle(updatedCliente);
                }
            });
            
            controller.setCancelHandler(() -> {
                log.debug("Usuario canceló la edición de cliente");
                dialogStage.close();
            });
            
            controller.setErrorHandler((errorMessage, error) -> {
                log.error("Error en diálogo de edición de cliente: {}", errorMessage, error);
                alertUtil.showError(errorMessage);
            });
            
            // Mostrar el diálogo
            dialogStage.showAndWait();
            
        } catch (Exception e) {
            log.error("Error al abrir diálogo de edición de cliente: {}", e.getMessage(), e);
            alertUtil.showError("Error al abrir el formulario de cliente: " + e.getMessage());
        }
    }

    /**
     * Abre un diálogo simple para crear un cliente con handlers por defecto
     * @param inputText Texto inicial
     * @param isNumeric Si es numérico
     * @param parentStage Stage padre
     * @return El cliente creado, o null si se canceló
     */
    public Cliente showCreateClienteDialogSync(String inputText, boolean isNumeric, Stage parentStage) {
        final Cliente[] result = {null};
        
        try {
            // Cargar el FXML
            Parent root = springFXMLLoader.load("fxml/cliente/clienteCreation.fxml");
            ClienteCreationController controller = springFXMLLoader.getController(root);
            
            // Crear el stage del diálogo
            Stage dialogStage = new Stage();
            dialogStage.setTitle("Crear Cliente");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(parentStage);
            dialogStage.initStyle(StageStyle.DECORATED);
            dialogStage.setResizable(true);
            
            // Configurar la escena
            Scene scene = new Scene(root);
            dialogStage.setScene(scene);
            
            // Configurar el controlador
            controller.configureForNewCliente(inputText, isNumeric);
            
            // Configurar handlers
            controller.setClienteCreatedHandler((cliente, originalText) -> {
                result[0] = cliente;
                dialogStage.close();
            });
            
            controller.setCancelHandler(dialogStage::close);
            
            controller.setErrorHandler((errorMessage, error) -> alertUtil.showError(errorMessage));
            
            // Mostrar el diálogo y esperar
            dialogStage.showAndWait();
            
        } catch (Exception e) {
            log.error("Error al abrir diálogo de creación de cliente: {}", e.getMessage(), e);
            alertUtil.showError("Error al abrir el formulario de cliente: " + e.getMessage());
        }
        
        return result[0];
    }

    /**
     * Método de conveniencia para mostrar confirmación antes de crear cliente
     * @param inputText Texto que no se encontró
     * @param isNumeric Si es numérico
     * @param onClienteCreated Callback cuando se crea
     * @param parentStage Stage padre
     */
    public void offerToCreateCliente(String inputText, boolean isNumeric,
                                   ClienteCreationController.ClienteCreatedHandler onClienteCreated,
                                   Stage parentStage) {
        // Mostrar diálogo de confirmación
        var result = alertUtil.showConfirmation(
                "Cliente no encontrado",
                "No se encontró un cliente con '" + inputText + "'",
                "¿Desea crear un nuevo cliente?",
                "Sí, crear",
                "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == javafx.scene.control.ButtonBar.ButtonData.OK_DONE) {
            // Usuario quiere crear un nuevo cliente
            showCreateClienteDialog(inputText, isNumeric, onClienteCreated, parentStage);
        }
    }
}
