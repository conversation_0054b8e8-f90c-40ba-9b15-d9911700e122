package corp.jamaro.jamaroescritoriofx.appfx.service;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
@Slf4j
public class GeneralService {

    // Definimos la ruta tal como se expuso en el controller del servidor.
    private static final String ROUTE_GET_ALL_USERS = "users.getAll";

    private final ConnectionService connectionService;

    /**
     * Solicita al servidor la lista de todos los usuarios.
     *
     * @return Flux<User> flujo reactivo con los usuarios obtenidos.
     */
    public Flux<User> getAllUsers() {
        log.debug("Solicitando todos los usuarios desde el servidor RSocket.");
        // Utilizamos authenticatedSubscription ya que el metodo en el servidor retorna un Flux<User>
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL_USERS, null, User.class);
    }
}
