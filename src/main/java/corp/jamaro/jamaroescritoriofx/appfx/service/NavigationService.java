package corp.jamaro.jamaroescritoriofx.appfx.service;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil; // Inyección del ErrorHandlerService
import javafx.application.Platform;
import javafx.scene.Parent;
import javafx.scene.Scene;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class NavigationService {

    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil; // Usar para mostrar errores

    @Setter
    private javafx.stage.Stage stage;

    public Mono<Void> navigateTo(FXMLEnum fxmlEnum) {
        return navigateTo(fxmlEnum, null);
    }

    public Mono<Void> navigateTo(FXMLEnum fxmlEnum, ControllerInitializer initializer) {
        return Mono.create(sink -> {
            Platform.runLater(() -> {
                try {
                    closeCurrentView();
                    Parent root = springFXMLLoader.load(fxmlEnum);
                    if (initializer != null) {
                        initializer.initialize(springFXMLLoader.getController(root));
                    }
                    Scene scene = new Scene(root);
                    stage.setScene(scene);

                    // Maximizar ventana para UniversalSale
                    if (fxmlEnum == FXMLEnum.UNIVERSAL_SALE) {
                        stage.setMaximized(true);
                        log.debug("Ventana maximizada para UniversalSale");
                    }

                    stage.show();
                    log.info("Navegación exitosa a la vista: {}", fxmlEnum.name());
                    sink.success();
                } catch (Exception e) {
                    log.error("Error al navegar a la vista: {}", fxmlEnum.name(), e);
                    handleNavigationError(e);
                    sink.error(e);
                }
            });
        });
    }

    private void closeCurrentView() {
        if (stage != null && stage.getScene() != null && stage.getScene().getRoot() != null) {
            Parent currentRoot = (Parent) stage.getScene().getRoot();
            Object currentController = springFXMLLoader.getController(currentRoot);
            if (currentController instanceof BaseController baseController) {
                baseController.onClose();
            }
        }
    }

    private void handleNavigationError(Exception e) {
        // Usar el errorHandlerService para mostrar detalles
        alertUtil.showErrorWithDetails(e, "Error de Navegación");
    }

    @FunctionalInterface
    public interface ControllerInitializer {
        void initialize(Object controller);
    }
}
