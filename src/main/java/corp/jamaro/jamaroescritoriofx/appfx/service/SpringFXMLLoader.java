package corp.jamaro.jamaroescritoriofx.appfx.service;

import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * Cargador de vistas FXML que integra con el contexto de Spring.
 * Utiliza la inyección de dependencias de Spring para los controladores.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpringFXMLLoader {

    private final ResourceLoader resourceLoader;
    private final ApplicationContext context;

    // Caché unificado para las URLs de las vistas FXML, usando la ruta como clave.
    private final Map<String, URL> fxmlCache = new HashMap<>();

    // Mapa para asociar cada vista con su FXMLLoader.
    private final Map<Parent, FXMLLoader> instanceMap = new HashMap<>();

    /**
     * Carga una vista FXML dada por el enum FXMLEnum.
     *
     * @param fxmlEnum Vista a cargar.
     * @return Nodo raíz (Parent) de la vista FXML.
     */
    public Parent load(FXMLEnum fxmlEnum) {
        return load(fxmlEnum.getPath());
    }

    /**
     * Carga una vista FXML dada por una ruta directa.
     *
     * @param fxmlPath Ruta del archivo FXML a cargar.
     * @return Nodo raíz (Parent) de la vista FXML.
     */
    public Parent load(String fxmlPath) {
        try {
            URL fxmlUrl;
            if (!fxmlCache.containsKey(fxmlPath)) {
                fxmlUrl = resourceLoader.getResource(fxmlPath).getURL();
                fxmlCache.put(fxmlPath, fxmlUrl);
            } else {
                fxmlUrl = fxmlCache.get(fxmlPath);
            }
            FXMLLoader loader = new FXMLLoader();
            loader.setControllerFactory(context::getBean);
            loader.setLocation(fxmlUrl);
            Parent view = loader.load();
            instanceMap.put(view, loader);
            return view;
        } catch (IOException e) {
            log.error("Error al cargar el archivo FXML: {}", fxmlPath, e);
            throw new RuntimeException("Error loading FXML file: " + fxmlPath, e);
        }
    }

    /**
     * Obtiene el controlador asociado a una vista cargada.
     *
     * @param view Vista cargada.
     * @param <T>  Tipo del controlador.
     * @return Controlador asociado.
     */
    public <T> T getController(Parent view) {
        FXMLLoader loader = instanceMap.get(view);
        if (loader == null) {
            throw new IllegalStateException("No se encontró un FXMLLoader asociado a la vista proporcionada.");
        }
        return loader.getController();
    }
}
