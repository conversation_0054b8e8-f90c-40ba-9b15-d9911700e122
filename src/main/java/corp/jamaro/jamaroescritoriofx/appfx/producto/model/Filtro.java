package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class Filtro {
    private UUID id;
    private String nombreFiltro;//interior, exterior, altura, terminal, terminal|diametro|conico ( filtro compuesto donde "|" es el separador para luego interpretar en el frontend)

    private Set<ToBucketFileRelation> files;// Generalmente van a ser Imagenes, animaciones, modelos 3d, etc.

    private TipoFiltro tipo;
}