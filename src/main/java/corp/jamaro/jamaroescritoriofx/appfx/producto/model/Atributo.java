package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.util.Set;

@Data
public class Atributo {
    private String id;

    private Filtro filtro;

    private String dato; // Campo unificado que reemplaza datoString, datoNumerico, datoDicotomico y datoCompuesto

//private Set<ToBucketFileRelation> datoFile;// Generalmente van a ser Imagenes, pero pueden ser animaciones, modelos 3d etc.
}
