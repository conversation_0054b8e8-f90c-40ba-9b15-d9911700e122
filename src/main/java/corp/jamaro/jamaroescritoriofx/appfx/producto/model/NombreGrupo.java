package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class NombreGrupo {
    private UUID id;

    private String nombre; // direccion, kit de embrague, retenes, rodaje, rodaje de rueda, reten de rueda, etc //y es UNIQUE
    private Boolean isPrincipal;// para saber si es el nombre principal o apodos
    private Instant createdAt;

    @Override
    public String toString() {
        return nombre;
    }
}
