package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.NombreGrupo;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class GrupoCategoriaService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_NOMBRES = "grupo-categoria.search.nombres";
    private static final String ROUTE_GET_BY_NOMBRE_GRUPO = "grupo-categoria.get.by-nombreGrupo";
    private static final String ROUTE_CREATE = "grupo-categoria.create";
    private static final String ROUTE_UPDATE = "grupo-categoria.update";

    private final ConnectionService connectionService;

    /**
     * Busca los NombreGrupo cuyo campo nombre coincida con una expresión regular dada 
     * y que tengan relación con los nodos Grupo cuyo tipo es categoria.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los NombreGrupo que cumplen el criterio.
     */
    public Flux<NombreGrupo> buscarNombresGrupoPorRegex(String userInput) {
        log.debug("Buscando NombreGrupo por regex para grupos de tipo categoria: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_NOMBRES, userInput, NombreGrupo.class);
    }

    /**
     * Recibe el id de un NombreGrupo y obtiene el Grupo con el que tiene relación.
     * Primero hace una consulta cypher simple en el repository y luego un findById para obtener el objeto completo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> obtenerGrupoPorNombreGrupoId(UUID nombreGrupoId) {
        log.debug("Obteniendo Grupo por NombreGrupo id: {}", nombreGrupoId);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_NOMBRE_GRUPO, nombreGrupoId, Grupo.class);
    }

    /**
     * Crea un nuevo Grupo del tipo categoria. Recibe un Grupo que el cliente ya construyó 
     * con un id asignado. Comprueba que este aún no exista, si no existe y el tipo es categoria 
     * procede a hacer un save.
     *
     * @param grupo El Grupo a crear.
     * @return Un Mono con el Grupo creado o error si ya existe o no es del tipo categoria.
     */
    public Mono<Grupo> crearGrupoCategoria(Grupo grupo) {
        log.debug("Creando nuevo Grupo categoria con id: {}", grupo.getId());
        return connectionService.authenticatedRequest(ROUTE_CREATE, grupo, Grupo.class);
    }

    /**
     * Actualiza un Grupo existente del tipo categoria. Valida que exista y que sea del tipo categoria.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Compara los ids de los nombresGrupo del antiguo con el nuevo y elimina los que ya no existan
     * - Compara los ids de los filtros y elimina las relaciones de los que ya no existan
     * - Los nodos Grupo del tipo categoria no usan el campo subGrupos
     * - Luego hace el save
     *
     * @param grupoActualizado El Grupo con los datos actualizados.
     * @return Un Mono con el Grupo actualizado.
     */
    public Mono<Grupo> actualizarGrupoCategoria(Grupo grupoActualizado) {
        log.debug("Actualizando Grupo categoria con id: {}", grupoActualizado.getId());
        return connectionService.authenticatedRequest(ROUTE_UPDATE, grupoActualizado, Grupo.class);
    }
}
