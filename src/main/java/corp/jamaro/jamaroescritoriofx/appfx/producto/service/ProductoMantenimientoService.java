package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.CodigoFabrica;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductoMantenimientoService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_BY_COD_PRODUCTO_OLD = "producto-mantenimiento.search.by-cod-producto-old";
    private static final String ROUTE_GET_BY_ID = "producto-mantenimiento.get.by-id";
    private static final String ROUTE_CREATE = "producto-mantenimiento.create";
    private static final String ROUTE_UPDATE = "producto-mantenimiento.update";
    private static final String ROUTE_SEARCH_CODIGO_FABRICA_BY_CODIGO = "producto-mantenimiento.search.codigo-fabrica-by-codigo";
    private static final String ROUTE_SEARCH_ITEMS_BY_COD_PRODUCTO_OLD = "producto-mantenimiento.search.items-by-cod-producto-old";


    private final ConnectionService connectionService;


    /**
     * Busca un producto por su código exacto del sistema anterior (codProductoOld).
     *
     * @param codProductoOld Código exacto del producto en el sistema anterior.
     * @return Un Mono con el Producto encontrado o vacío si no existe.
     */
    public Mono<Producto> buscarProductoPorCodProductoOld(String codProductoOld) {
        log.debug("Buscando Producto por codProductoOld exacto: {}", codProductoOld);
        return connectionService.authenticatedRequest(ROUTE_SEARCH_BY_COD_PRODUCTO_OLD, codProductoOld, Producto.class);
    }

    /**
     * Obtiene un producto por su ID.
     *
     * @param productoId UUID del Producto.
     * @return Un Mono con el Producto encontrado o vacío si no existe.
     */
    public Mono<Producto> obtenerProductoPorId(UUID productoId) {
        log.debug("Obteniendo Producto por id: {}", productoId);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_ID, productoId, Producto.class);
    }

    /**
     * Crea un nuevo Producto. Recibe un Producto que el cliente ya construyó
     * con un id asignado. Comprueba que este aún no exista y procede a hacer un save.
     *
     * @param producto El Producto a crear.
     * @return Un Mono con el Producto creado o error si ya existe.
     */
    public Mono<Producto> crearProducto(Producto producto) {
        log.debug("Creando nuevo Producto con id: {}", producto.getId());
        return connectionService.authenticatedRequest(ROUTE_CREATE, producto, Producto.class);
    }

    /**
     * Actualiza un Producto existente. Valida que exista.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Actualiza los atributos del producto
     * - Actualiza las relaciones con vehículos
     * - Actualiza las relaciones con grupos
     * - Actualiza los códigos de fábrica
     * - Actualiza los archivos asociados
     * - Luego hace el save
     *
     * @param productoActualizado El Producto con los datos actualizados.
     * @return Un Mono con el Producto actualizado.
     */
    public Mono<Producto> actualizarProducto(Producto productoActualizado) {
        log.debug("Actualizando Producto con id: {}", productoActualizado.getId());
        return connectionService.authenticatedRequest(ROUTE_UPDATE, productoActualizado, Producto.class);
    }

    /**
     * Busca CodigoFabrica por su campo codigo usando una expresión regular que contenga cualquier coincidencia.
     * Utiliza RegexUtil.buildContainsAllRegex para construir la expresión regular de forma segura.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los CodigoFabrica que cumplen el criterio.
     */
    public Flux<CodigoFabrica> buscarCodigoFabricaPorCodigo(String userInput) {
        log.debug("Buscando CodigoFabrica por codigo con regex: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_CODIGO_FABRICA_BY_CODIGO, userInput, CodigoFabrica.class);
    }

    /**
     * Busca Items por el campo codProductoOld del Producto relacionado.
     *
     * @param codProductoOld Código del producto en el sistema anterior.
     * @return Un Flux que emite los Items que cumplen el criterio.
     */
    public Flux<Item> buscarItemsPorCodProductoOld(String codProductoOld) {
        log.debug("Buscando Items por codProductoOld: {}", codProductoOld);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_ITEMS_BY_COD_PRODUCTO_OLD, codProductoOld, Item.class);
    }

}
