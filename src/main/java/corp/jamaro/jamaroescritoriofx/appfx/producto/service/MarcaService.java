package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Marca;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class MarcaService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_BY_NOMBRE = "marca.search.by-nombre";
    private static final String ROUTE_CREATE = "marca.create";
    private static final String ROUTE_UPDATE = "marca.update";
    private static final String ROUTE_GET_BY_ABREVIACION = "marca.get.by-abreviacion";
    private static final String ROUTE_GET_ALL = "marca.get.all";

    private final ConnectionService connectionService;

    /**
     * Busca Marcas por su campo nombre usando buildContainsAllRegex de RegexUtil.
     * Utiliza una expresión regular que contenga todas las palabras del input.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con las Marcas que cumplen el criterio de búsqueda.
     */
    public Flux<Marca> buscarMarcaPorNombre(String userInput) {
        log.debug("Buscando Marca por nombre con input: {}", userInput);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_BY_NOMBRE, userInput, Marca.class);
    }

    /**
     * Crea una nueva Marca.
     * Verifica que no exista una Marca con la misma abreviacion antes de crear.
     *
     * @param marca La Marca a crear.
     * @return Mono con la Marca creada o error si ya existe.
     */
    public Mono<Marca> crearMarca(Marca marca) {
        log.debug("Creando nueva Marca: {}", marca);
        return connectionService.authenticatedRequest(ROUTE_CREATE, marca, Marca.class);
    }

    /**
     * Actualiza una Marca existente.
     * Valida que la Marca exista antes de actualizarla.
     *
     * @param marcaActualizada La Marca con los datos actualizados.
     * @return Mono con la Marca actualizada.
     */
    public Mono<Marca> actualizarMarca(Marca marcaActualizada) {
        log.debug("Actualizando Marca: {}", marcaActualizada);
        return connectionService.authenticatedRequest(ROUTE_UPDATE, marcaActualizada, Marca.class);
    }

    /**
     * Obtiene una Marca por su abreviacion (ID).
     *
     * @param abreviacion La abreviacion de la Marca.
     * @return Mono con la Marca encontrada o vacío si no existe.
     */
    public Mono<Marca> obtenerMarcaPorAbreviacion(String abreviacion) {
        log.debug("Obteniendo Marca por abreviacion: {}", abreviacion);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_ABREVIACION, abreviacion, Marca.class);
    }

    /**
     * Obtiene todas las Marcas.
     *
     * @return Flux con todas las Marcas.
     */
    public Flux<Marca> obtenerTodasLasMarcas() {
        log.debug("Obteniendo todas las Marcas");
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL, "", Marca.class);
    }
}
