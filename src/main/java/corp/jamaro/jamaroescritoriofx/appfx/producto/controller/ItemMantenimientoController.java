package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ItemMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.MarcaService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ProductoMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.function.UnaryOperator;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

/**
 * Estados del controlador de mantenimiento de items
 */

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class ItemMantenimientoController extends BaseController {

    // Services
    private final ItemMantenimientoService itemMantenimientoService;
    private final MarcaService marcaService;
    private final ProductoMantenimientoService productoMantenimientoService;
    private final AlertUtil alertUtil;

    // FXML Components - Header
    @FXML private Button btnGuardar;
    @FXML private Button btnCancelar;
    @FXML private ProgressIndicator progressIndicator;
    @FXML private CustomTextField txtBuscarCodProductoItem;
    @FXML private CustomTextField txtMarca;
    @FXML private Button btnBuscarProducto;
    @FXML private Label lblProductoCargado;

    // FXML Components - Main Content
    @FXML private Label lblDescripcion;
    @FXML private CustomTextField txtDescripcion;
    @FXML private FlowPane flowPaneGrupos;
    @FXML private Label lblAtributo;
    @FXML private FlowPane flowPaneAtributos;
    @FXML private CustomTextField txtBuscarCodigoFabrica;
    @FXML private FlowPane flowPaneCodigosFabrica;
    @FXML private CustomTextField txtBuscarUbicaciones;
    @FXML private FlowPane flowPaneUbicaciones;

    // FXML Components - Precios y Stock
    @FXML private CustomTextField txtPrecioCostoPromedio;
    @FXML private CustomTextField txtPrecioVentaBase;
    @FXML private CustomTextField txtPrecioVentaPromocion;
    @FXML private CustomTextField txtPrecioVentaPublico;
    @FXML private CustomTextField txtStockTotal;
    @FXML private CustomTextField txtStockDeSeguridad;
    @FXML private TextArea txtAnotaciones;

    // FXML Components - Archivos
    @FXML private Button btnAgregarImagen;
    @FXML private FlowPane flowPaneArchivos;

    // State management using JavaFX Properties
    private final ObjectProperty<ControllerState> currentState = new SimpleObjectProperty<>(ControllerState.INITIAL);
    private final ObjectProperty<Producto> currentProducto = new SimpleObjectProperty<>();
    private final ObjectProperty<Item> currentItem = new SimpleObjectProperty<>();
    private final ObjectProperty<Marca> currentMarca = new SimpleObjectProperty<>();
    private final BooleanProperty isLoading = new SimpleBooleanProperty(false);
    private final StringProperty generatedCodCompuesto = new SimpleStringProperty();

    // AutoCompletion bindings
    private AutoCompletionBinding<Marca> marcaAutoCompletionBinding;

    // Observable lists for UI components
    private final ObservableList<Atributo> atributosData = FXCollections.observableArrayList();
    private final ObservableList<CodigoFabrica> codigosFabricaData = FXCollections.observableArrayList();
    private final ObservableList<Ubicacion> ubicacionesData = FXCollections.observableArrayList();

    // Pattern for decimal validation
    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d*\\.?\\d{0,2}$");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.debug("Inicializando ItemMantenimientoController");
        setupStateBindings();
        setupEventHandlers();
        setupValidations();
        setupMarcaAutoCompletion();
        transitionToState(ControllerState.INITIAL);
    }

    /**
     * Configura los bindings reactivos basados en el estado del controlador
     */
    private void setupStateBindings() {
        // Binding del indicador de progreso
        progressIndicator.visibleProperty().bind(isLoading);

        // Binding del estado del botón guardar
        btnGuardar.disableProperty().bind(
            currentState.isNotEqualTo(ControllerState.EDITING)
                .and(currentState.isNotEqualTo(ControllerState.CREATING_NEW))
                .or(isLoading)
        );

        // Binding del campo de búsqueda principal
        txtBuscarCodProductoItem.disableProperty().bind(
            currentState.isEqualTo(ControllerState.PRODUCTO_LOADED)
                .or(currentState.isEqualTo(ControllerState.ITEM_LOADED))
                .or(currentState.isEqualTo(ControllerState.CREATING_NEW))
                .or(currentState.isEqualTo(ControllerState.EDITING))
        );

        // Binding del campo de marca - habilitado solo cuando hay producto cargado pero no item
        txtMarca.disableProperty().bind(
            currentState.isNotEqualTo(ControllerState.PRODUCTO_LOADED)
        );

        // Binding de los campos de edición de item - habilitados cuando estamos editando o creando
        BooleanProperty itemFieldsDisabled = new SimpleBooleanProperty();
        itemFieldsDisabled.bind(
            currentState.isNotEqualTo(ControllerState.EDITING)
                .and(currentState.isNotEqualTo(ControllerState.CREATING_NEW))
        );

        bindItemFields(itemFieldsDisabled);

        // Binding del label de producto cargado
        lblProductoCargado.visibleProperty().bind(currentProducto.isNotNull());
        lblProductoCargado.managedProperty().bind(lblProductoCargado.visibleProperty());

        // Listener para actualizar el código compuesto cuando cambian producto y marca
        currentProducto.addListener((obs, oldVal, newVal) -> updateGeneratedCodCompuesto());
        currentMarca.addListener((obs, oldVal, newVal) -> updateGeneratedCodCompuesto());

        // Binding del texto del campo de búsqueda con el código generado
        generatedCodCompuesto.addListener((obs, oldVal, newVal) -> {
            if (newVal != null && !newVal.isEmpty() &&
                (currentState.get() == ControllerState.PRODUCTO_LOADED ||
                 currentState.get() == ControllerState.CREATING_NEW)) {
                txtBuscarCodProductoItem.setText(newVal);
            }
        });
    }

    private void bindItemFields(BooleanProperty disabled) {
        // Incluir descripción en el binding para que esté deshabilitada por defecto
        txtDescripcion.disableProperty().bind(disabled);
        txtBuscarCodigoFabrica.disableProperty().bind(disabled);
        txtBuscarUbicaciones.disableProperty().bind(disabled);
        txtPrecioCostoPromedio.disableProperty().bind(disabled);
        txtPrecioVentaBase.disableProperty().bind(disabled);
        txtPrecioVentaPromocion.disableProperty().bind(disabled);
        txtPrecioVentaPublico.disableProperty().bind(disabled);
        txtStockTotal.disableProperty().bind(disabled);
        txtStockDeSeguridad.disableProperty().bind(disabled);
        txtAnotaciones.disableProperty().bind(disabled);
        btnAgregarImagen.disableProperty().bind(disabled);
    }

    private void updateGeneratedCodCompuesto() {
        if (currentProducto.get() != null && currentMarca.get() != null) {
            String codCompuesto = currentProducto.get().getCodProductoOld() + currentMarca.get().getAbreviacion();
            generatedCodCompuesto.set(codCompuesto);
        } else {
            generatedCodCompuesto.set(null);
        }
    }

    /**
     * Transición segura entre estados del controlador
     */
    private void transitionToState(ControllerState newState) {
        ControllerState oldState = currentState.get();
        log.debug("Transición de estado: {} -> {}", oldState, newState);

        // Validar transición
        if (!isValidTransition(oldState, newState)) {
            log.warn("Transición inválida de {} a {}", oldState, newState);
            return;
        }

        // Ejecutar acciones de salida del estado anterior
        onExitState(oldState);

        // Cambiar estado
        currentState.set(newState);

        // Ejecutar acciones de entrada al nuevo estado
        onEnterState(newState);
    }

    private boolean isValidTransition(ControllerState from, ControllerState to) {
        // Definir transiciones válidas
        return switch (from) {
            case INITIAL -> to == ControllerState.PRODUCTO_LOADED || to == ControllerState.EDITING;
            case PRODUCTO_LOADED -> to == ControllerState.INITIAL || to == ControllerState.CREATING_NEW || to == ControllerState.EDITING;
            case ITEM_LOADED -> to == ControllerState.INITIAL || to == ControllerState.EDITING;
            case CREATING_NEW -> to == ControllerState.INITIAL || to == ControllerState.EDITING;
            case EDITING -> to == ControllerState.INITIAL || to == ControllerState.ITEM_LOADED;
        };
    }

    private void onExitState(ControllerState state) {
        // Acciones específicas al salir de cada estado
        switch (state) {
            case PRODUCTO_LOADED -> {
                // Limpiar autocompletado de marca si está activo
                if (marcaAutoCompletionBinding != null) {
                    marcaAutoCompletionBinding.dispose();
                }
            }
            case EDITING, CREATING_NEW -> {
                // Guardar cambios pendientes si es necesario
                // (esto se podría implementar como un draft automático)
            }
        }
    }

    private void onEnterState(ControllerState state) {
        // Acciones específicas al entrar a cada estado
        switch (state) {
            case INITIAL -> {
                clearAllData();
                txtBuscarCodProductoItem.clear();
                txtBuscarCodProductoItem.requestFocus();
            }
            case PRODUCTO_LOADED -> {
                updateProductoLabel();
                txtMarca.clear();
                setupMarcaAutoCompletion(); // Reconfigurar autocompletado
                txtMarca.requestFocus();
            }
            case ITEM_LOADED -> {
                loadItemDataToUI();
                setupAllEditModes(); // Configurar edición para todos los campos
            }
            case CREATING_NEW -> {
                initializeNewItem();
                loadItemDataToUI();
                setupAllEditModes(); // Configurar edición para todos los campos
                txtDescripcion.requestFocus();
            }
            case EDITING -> {
                // Asegurar que la UI esté cargada si viene de un item existente
                if (currentItem.get() != null) {
                    loadItemDataToUI();
                }
                setupAllEditModes(); // Configurar edición para todos los campos
                txtDescripcion.requestFocus();
            }
        }
    }

    private void setupEventHandlers() {
        setupSearchHandlers();
        setupButtonHandlers();
        setupDescriptionHandlers();
    }

    private void setupSearchHandlers() {
        // Búsqueda por Enter en el campo principal - busca por código de Producto o Item
        txtBuscarCodProductoItem.setOnAction(event -> {
            if (currentState.get() == ControllerState.INITIAL) {
                buscarPorCodigo();
            }
        });

        // Botón de búsqueda avanzada
        btnBuscarProducto.setOnAction(event -> abrirBusquedaAvanzada());

        // Limpiar datos cuando se cambia el texto de búsqueda (solo en estado inicial)
        txtBuscarCodProductoItem.textProperty().addListener((obs, oldVal, newVal) -> {
            if (currentState.get() == ControllerState.INITIAL &&
                (newVal == null || newVal.trim().isEmpty())) {
                clearAllData();
            }
        });
    }

    private void setupButtonHandlers() {
        btnGuardar.setOnAction(event -> guardarItem());
        btnCancelar.setOnAction(event -> cancelarOperacion());
        btnAgregarImagen.setOnAction(event -> agregarImagen());
    }

    private void setupDescriptionHandlers() {
        // Este método se llama una sola vez en initialize
        // La configuración específica se hace en setupDescriptionEditMode()
    }

    /**
     * Configura el modo de edición con doble click para todos los campos editables.
     * Excluye txtBuscarCodProductoItem y txtMarca que tienen lógicas diferentes.
     */
    private void setupAllEditModes() {
        boolean canEdit = currentState.get() == ControllerState.CREATING_NEW ||
                         currentState.get() == ControllerState.EDITING ||
                         currentState.get() == ControllerState.ITEM_LOADED;

        if (canEdit) {
            // Aplicar a todos los CustomTextField especificados
            setupDoubleClickEditMode(txtDescripcion);
            setupDoubleClickEditMode(txtBuscarCodigoFabrica);
            setupDoubleClickEditMode(txtBuscarUbicaciones);
            setupDoubleClickEditMode(txtPrecioCostoPromedio);
            setupDoubleClickEditMode(txtPrecioVentaBase);
            setupDoubleClickEditMode(txtPrecioVentaPromocion);
            setupDoubleClickEditMode(txtPrecioVentaPublico);
            setupDoubleClickEditMode(txtStockTotal);
            setupDoubleClickEditMode(txtStockDeSeguridad);
            
            // Aplicar al TextArea
            setupDoubleClickEditMode(txtAnotaciones);
        } else {
            // Resetear todos los campos a no editables
            resetFieldEditMode(txtDescripcion);
            resetFieldEditMode(txtBuscarCodigoFabrica);
            resetFieldEditMode(txtBuscarUbicaciones);
            resetFieldEditMode(txtPrecioCostoPromedio);
            resetFieldEditMode(txtPrecioVentaBase);
            resetFieldEditMode(txtPrecioVentaPromocion);
            resetFieldEditMode(txtPrecioVentaPublico);
            resetFieldEditMode(txtStockTotal);
            resetFieldEditMode(txtStockDeSeguridad);
            resetTextAreaEditMode(txtAnotaciones);
        }
    }

    private void setupDescriptionEditMode() {
        // Configurar edición de descripción según el estado actual
        boolean canEdit = currentState.get() == ControllerState.CREATING_NEW ||
                         currentState.get() == ControllerState.EDITING ||
                         currentState.get() == ControllerState.ITEM_LOADED;

        if (canEdit) {
            setupDoubleClickEditMode(txtDescripcion);
        } else {
            txtDescripcion.setEditable(false);
            txtDescripcion.setOnMouseClicked(null);
        }
    }

    /**
     * Configura el modo de edición con doble click para un CustomTextField.
     * Aplica el principio DRY para reutilizar la lógica en múltiples campos.
     */
    private void setupDoubleClickEditMode(CustomTextField textField) {
        textField.setEditable(false); // Por defecto no editable

        // Hacer editable con doble click
        textField.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2 && currentItem.get() != null) {
                textField.setEditable(true);
                textField.positionCaret(textField.getText().length());

                // Si no estamos en modo edición, transicionar automáticamente
                if (currentState.get() == ControllerState.ITEM_LOADED) {
                    transitionToState(ControllerState.EDITING);
                }
            }
        });

        // Volver a no editable cuando pierde el foco
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                textField.setEditable(false);
            }
        });

        // Hacer editable con Enter y volver a no editable con Enter
        textField.setOnAction(event -> {
            if (textField.isEditable()) {
                textField.setEditable(false);
            } else if (currentItem.get() != null) {
                textField.setEditable(true);
                textField.positionCaret(textField.getText().length());

                // Si no estamos en modo edición, transicionar automáticamente
                if (currentState.get() == ControllerState.ITEM_LOADED) {
                    transitionToState(ControllerState.EDITING);
                }
            }
        });
    }

    /**
     * Configura el modo de edición con doble click para un TextArea.
     * Versión específica para TextArea que no extiende de CustomTextField.
     */
    private void setupDoubleClickEditMode(TextArea textArea) {
        textArea.setEditable(false); // Por defecto no editable

        // Hacer editable con doble click
        textArea.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2 && currentItem.get() != null) {
                textArea.setEditable(true);
                // Verificar que el texto no sea null antes de posicionar el cursor
                String text = textArea.getText();
                if (text != null) {
                    textArea.positionCaret(text.length());
                } else {
                    textArea.positionCaret(0);
                }

                // Si no estamos en modo edición, transicionar automáticamente
                if (currentState.get() == ControllerState.ITEM_LOADED) {
                    transitionToState(ControllerState.EDITING);
                }
            }
        });

        // Volver a no editable cuando pierde el foco
        textArea.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                textArea.setEditable(false);
            }
        });
    }

    /**
     * Resetea el modo de edición de un CustomTextField.
     */
    private void resetFieldEditMode(CustomTextField textField) {
        textField.setEditable(false);
        textField.setOnMouseClicked(null);
        textField.setOnAction(null);
    }

    /**
     * Resetea el modo de edición de un TextArea.
     */
    private void resetTextAreaEditMode(TextArea textArea) {
        textArea.setEditable(false);
        textArea.setOnMouseClicked(null);
    }

    private void setupMarcaAutoCompletion() {
        // Limpiar binding anterior si existe
        if (marcaAutoCompletionBinding != null) {
            marcaAutoCompletionBinding.dispose();
        }

        // Solo configurar si estamos en el estado correcto
        if (currentState.get() != ControllerState.PRODUCTO_LOADED) {
            return;
        }

        // Configurar autocompletado para marca
        marcaAutoCompletionBinding = TextFields.bindAutoCompletion(txtMarca, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) {
                return Collections.emptyList();
            }

            // No usar isLoading aquí para no interferir con otras operaciones
            try {
                Optional<List<Marca>> suggestions = marcaService.buscarMarcaPorNombre(userText)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .blockOptional(Duration.ofSeconds(2));

                return suggestions.orElse(Collections.emptyList());
            } catch (Exception e) {
                log.error("Error buscando marcas: {}", e.getMessage(), e);
                return Collections.emptyList();
            }
        });

        // Manejar selección de marca
        marcaAutoCompletionBinding.setOnAutoCompleted(event -> {
            Marca selectedMarca = event.getCompletion();
            if (selectedMarca != null && currentProducto.get() != null) {
                currentMarca.set(selectedMarca);

                if (currentState.get() == ControllerState.PRODUCTO_LOADED) {
                    buscarOCrearItem();
                }
            }
        });
    }

    private void setupValidations() {
        // Configurar validaciones numéricas para campos de precio y stock
        setupDecimalField(txtPrecioCostoPromedio);
        setupDecimalField(txtPrecioVentaBase);
        setupDecimalField(txtPrecioVentaPromocion);
        setupDecimalField(txtPrecioVentaPublico);
        setupDecimalField(txtStockTotal);
        setupDecimalField(txtStockDeSeguridad);
    }

    private void setupDecimalField(TextField field) {
        UnaryOperator<TextFormatter.Change> filter = change -> {
            String newText = change.getControlNewText();

            // Permitir texto vacío
            if (newText.isEmpty()) {
                return change;
            }

            // Validar formato decimal con máximo 2 decimales
            if (DECIMAL_PATTERN.matcher(newText).matches()) {
                return change;
            }

            return null; // Rechazar el cambio
        };

        field.setTextFormatter(new TextFormatter<>(filter));
    }

    /**
     * Busca por código ingresado en txtBuscarCodProductoItem.
     * Puede ser un codCompuesto (Item) o un codProductoOld (Producto).
     * Primero intenta buscar como Item, si no encuentra, busca como Producto.
     */
    private void buscarPorCodigo() {
        String codigo = txtBuscarCodProductoItem.getText();
        if (codigo == null || codigo.trim().isEmpty()) {
            return;
        }

        final String codigoFinal = codigo.trim().toUpperCase();
        log.info("Iniciando búsqueda con código: {}", codigoFinal);
        isLoading.set(true);

        try {
            // Primero intentar buscar como Item por codCompuesto
            Optional<Item> itemOpt = itemMantenimientoService.buscarItemPorCodCompuesto(codigoFinal)
                    .onErrorResume(error -> {
                        log.debug("Error en búsqueda de item, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            if (itemOpt.isPresent() && itemOpt.get() != null) {
                // Item encontrado
                Item item = itemOpt.get();
                log.info("Item encontrado: {}", item.getCodCompuesto());
                isLoading.set(false);
                loadItem(item);
                return;
            }

            // Si no es un Item, intentar buscar como Producto por codProductoOld
            Optional<Producto> productoOpt = productoMantenimientoService.buscarProductoPorCodProductoOld(codigoFinal)
                    .onErrorResume(error -> {
                        log.debug("Error en búsqueda de producto, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            isLoading.set(false);

            if (productoOpt.isPresent() && productoOpt.get() != null) {
                // Producto encontrado
                Producto producto = productoOpt.get();
                log.info("Producto encontrado: {}", producto.getCodProductoOld());
                loadProducto(producto);
            } else {
                // Ni Item ni Producto encontrado
                log.info("No se encontró item ni producto con código: {}", codigoFinal);
                showErrorMessage("No se encontró ningún producto o item con el código especificado");
            }

        } catch (RuntimeException e) {
            isLoading.set(false);

            // Verificar si la causa es una InterruptedException
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt(); // Restaurar el estado de interrupción
                log.debug("Búsqueda interrumpida");
                showErrorMessage("Búsqueda interrumpida");
            } else {
                log.error("Error en búsqueda: {}", e.getMessage(), e);
                showErrorMessage("Error al buscar: " + e.getMessage());
            }
        } catch (Exception e) {
            isLoading.set(false);
            log.error("Error en búsqueda: {}", e.getMessage(), e);
            showErrorMessage("Error al buscar: " + e.getMessage());
        }
    }

    /**
     * TODO: Implementar interfaz de búsqueda avanzada de productos.
     * Este método abrirá una ventana de búsqueda más completa.
     */
    private void abrirBusquedaAvanzada() {
        alertUtil.showInfo("Pendiente", "Funcionalidad de búsqueda avanzada pendiente de implementación");
    }

    /**
     * Carga un Item y transiciona al estado correspondiente
     */
    private void loadItem(Item item) {
        log.debug("Cargando Item: {}", item.getCodCompuesto());

        currentItem.set(item);
        currentProducto.set(item.getProducto());
        currentMarca.set(item.getMarca());

        // Ir directamente a EDITING para habilitar todos los campos
        transitionToState(ControllerState.EDITING);
    }

    /**
     * Carga un Producto y transiciona al estado correspondiente
     */
    private void loadProducto(Producto producto) {
        log.debug("Cargando Producto: {}", producto.getCodProductoOld());

        currentProducto.set(producto);
        currentItem.set(null);
        currentMarca.set(null);

        transitionToState(ControllerState.PRODUCTO_LOADED);
    }

    private void buscarOCrearItem() {
        if (currentProducto.get() == null || currentMarca.get() == null) {
            return;
        }

        String codCompuesto = generatedCodCompuesto.get();
        log.info("Buscando item con código compuesto: {}", codCompuesto);

        isLoading.set(true);

        try {
            Optional<Item> itemOpt = itemMantenimientoService.buscarItemPorCodCompuesto(codCompuesto)
                    .onErrorResume(error -> {
                        log.debug("Error en búsqueda de item, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            isLoading.set(false);

            if (itemOpt.isPresent() && itemOpt.get() != null) {
                // Item encontrado
                Item item = itemOpt.get();
                log.info("Item encontrado: {}", item.getCodCompuesto());
                loadItem(item);
            } else {
                // Item no encontrado, preguntar si crear nuevo
                preguntarCrearNuevoItem(codCompuesto);
            }

        } catch (RuntimeException e) {
            isLoading.set(false);

            // Verificar si la causa es una InterruptedException
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt(); // Restaurar el estado de interrupción
                log.debug("Búsqueda de item interrumpida");
                showErrorMessage("Búsqueda de item interrumpida");
            } else {
                log.error("Error buscando item: {}", e.getMessage(), e);
                showErrorMessage("Error al buscar item: " + e.getMessage());
            }
        } catch (Exception e) {
            isLoading.set(false);
            log.error("Error buscando item: {}", e.getMessage(), e);
            showErrorMessage("Error al buscar item: " + e.getMessage());
        }
    }

    private void preguntarCrearNuevoItem(String codCompuesto) {
        String mensaje = String.format(
            "No existe un item con el código '%s'.\n¿Desea crear un nuevo item?",
            codCompuesto
        );

        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Crear Nuevo Item",
            null,
            mensaje,
            "Crear",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            // Transicionar a CREATING_NEW que automáticamente habilitará los campos
            transitionToState(ControllerState.CREATING_NEW);
        }
    }

    private void initializeNewItem() {
        String codCompuesto = generatedCodCompuesto.get();
        log.debug("Inicializando modo creación para codCompuesto: {}", codCompuesto);

        // Crear nuevo Item con datos básicos
        Item newItem = new Item();
        newItem.setCodCompuesto(codCompuesto);
        newItem.setProducto(currentProducto.get());
        newItem.setMarca(currentMarca.get());

        // Descripción inicial
        String descripcionInicial = currentProducto.get().getDescripcion() + " de la marca " + currentMarca.get().getNombre();
        newItem.setDescripcion(descripcionInicial);

        // Copiar atributos del producto
        if (currentProducto.get().getAtributos() != null) {
            Set<Atributo> atributosCopiados = currentProducto.get().getAtributos().stream()
                .map(this::copiarAtributo)
                .collect(Collectors.toSet());
            newItem.setAtributos(atributosCopiados);
        }

        currentItem.set(newItem);
    }

    private Atributo copiarAtributo(Atributo original) {
        Atributo copia = new Atributo();
        copia.setFiltro(original.getFiltro());
        copia.setDato(original.getDato());
        return copia;
    }

    // ========== MÉTODOS DE CARGA Y ACTUALIZACIÓN DE UI ==========

    private void updateProductoLabel() {
        if (currentProducto.get() == null) {
            lblProductoCargado.setText("");
            return;
        }

        String codProductoOld = currentProducto.get().getCodProductoOld();
        log.debug("Actualizando label producto cargado para: {}", codProductoOld);

        try {
            // Buscar items relacionados con este producto
            List<Item> itemsRelacionados = productoMantenimientoService.buscarItemsPorCodProductoOld(codProductoOld)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .blockOptional(Duration.ofSeconds(5))
                    .orElse(Collections.emptyList());

            // Extraer información de marcas
            Set<String> marcasInfo = itemsRelacionados.stream()
                    .filter(item -> item.getMarca() != null)
                    .map(item -> {
                        Marca marca = item.getMarca();
                        return marca.getAbreviacion() + " (" + marca.getNombre() + ")";
                    })
                    .collect(Collectors.toSet());

            // Construir texto del label
            StringBuilder labelText = new StringBuilder();
            labelText.append("Producto: ").append(codProductoOld);

            if (currentProducto.get().getDescripcion() != null && !currentProducto.get().getDescripcion().trim().isEmpty()) {
                labelText.append(" - ").append(currentProducto.get().getDescripcion());
            }

            if (!marcasInfo.isEmpty()) {
                labelText.append(" | Marcas: ").append(String.join(", ", marcasInfo));
            }

            lblProductoCargado.setText(labelText.toString());

        } catch (Exception e) {
            log.error("Error actualizando label producto cargado: {}", e.getMessage(), e);
            // En caso de error, mostrar solo información básica del producto
            String basicText = "Producto: " + codProductoOld;
            if (currentProducto.get().getDescripcion() != null && !currentProducto.get().getDescripcion().trim().isEmpty()) {
                basicText += " - " + currentProducto.get().getDescripcion();
            }
            lblProductoCargado.setText(basicText);
        }
    }

    // ========== MÉTODOS DE CARGA DE DATOS EN UI ==========

    private void loadItemDataToUI() {
        Item item = currentItem.get();
        if (item == null) return;

        // Limpiar campos primero
        clearUIFields();
        clearFlowPanes();

        // Cargar datos básicos
        txtDescripcion.setText(item.getDescripcion());

        // Cargar marca
        if (item.getMarca() != null) {
            txtMarca.setText(item.getMarca().getNombre());
        }

        // Cargar grupos del producto
        if (item.getProducto() != null && item.getProducto().getGrupos() != null) {
            loadGruposToUI(item.getProducto().getGrupos());
        }

        // Cargar atributos
        loadAtributosToUI(item.getAtributos());

        // Cargar códigos de fábrica
        loadCodigosFabricaToUI(item.getCodigosFabrica());

        // Cargar ubicaciones
        loadUbicacionesToUI(item.getUbicaciones());

        // Cargar precios y stock
        txtPrecioCostoPromedio.setText(formatDecimal(item.getPrecioCostoPromedio()));
        txtPrecioVentaBase.setText(formatDecimal(item.getPrecioVentaBase()));
        txtPrecioVentaPromocion.setText(formatDecimal(item.getPrecioVentaPromocion()));
        txtPrecioVentaPublico.setText(formatDecimal(item.getPrecioVentaPublico()));
        txtStockTotal.setText(formatDecimal(item.getStockTotal()));
        txtStockDeSeguridad.setText(formatDecimal(item.getStockDeSeguridad()));

        // Cargar anotaciones
        txtAnotaciones.setText(item.getAnotacionesOld());

        // TODO: Cargar imágenes
    }

    private void loadGruposToUI(Set<Grupo> grupos) {
        flowPaneGrupos.getChildren().clear();

        if (grupos == null || grupos.isEmpty()) {
            return;
        }

        for (Grupo grupo : grupos) {
            Label grupoLabel = new Label(getGrupoDisplayName(grupo));
            grupoLabel.getStyleClass().addAll("tag", "tag-info");
            grupoLabel.setPadding(new Insets(4, 8, 4, 8));
            flowPaneGrupos.getChildren().add(grupoLabel);
        }
    }

    private String getGrupoDisplayName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                .filter(ng -> ng.getIsPrincipal() != null && ng.getIsPrincipal())
                .findFirst()
                .map(NombreGrupo::getNombre)
                .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return grupo.getId();
    }

    private void loadAtributosToUI(Set<Atributo> atributos) {
        flowPaneAtributos.getChildren().clear();
        atributosData.clear();

        if (atributos == null || atributos.isEmpty()) {
            return;
        }

        atributosData.addAll(atributos);

        for (Atributo atributo : atributos) {
            HBox atributoContainer = createAtributoField(atributo);
            flowPaneAtributos.getChildren().add(atributoContainer);
        }
    }

    private HBox createAtributoField(Atributo atributo) {
        HBox container = new HBox(5);
        container.setAlignment(Pos.CENTER_LEFT);
        container.setPadding(new Insets(2));
        // Hacer el container responsive dentro del FlowPane
        container.setPrefWidth(200);
        container.setMaxWidth(Double.MAX_VALUE);

        Filtro filtro = atributo.getFiltro();
        if (filtro == null) {
            return container; // Skip if no filter
        }

        TipoFiltro tipo = filtro.getTipo();
        if (tipo == null) {
            tipo = TipoFiltro.CADENA_TEXTO; // Default type
        }

        // Crear label con el nombre del filtro
        Label label = new Label(filtro.getNombreFiltro() + ":");
        label.getStyleClass().add("field-label");
        label.setMinWidth(80);

        // Crear campo según el tipo de filtro
        switch (tipo) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case NUMERICO:
                CustomTextField textField = new CustomTextField();
                textField.setText(atributo.getDato() != null ? atributo.getDato() : "");
                textField.setLeft(label);
                textField.setMaxWidth(Double.MAX_VALUE);
                HBox.setHgrow(textField, javafx.scene.layout.Priority.ALWAYS);

                // Configurar validación para campos numéricos
                if (tipo == TipoFiltro.NUMERICO) {
                    setupDecimalField(textField);
                }

                // Aplicar funcionalidad de doble click para edición
                setupDoubleClickEditMode(textField);

                // Listener para actualizar el atributo
                textField.textProperty().addListener((obs, oldVal, newVal) -> {
                    atributo.setDato(newVal != null && !newVal.trim().isEmpty() ? newVal.trim() : null);
                });

                container.getChildren().add(textField);
                break;

            case DICOTOMICO:
            case COMPUESTO:
            default:
                // TODO: Implementar otros tipos de filtros
                Label todoLabel = new Label("TODO: " + tipo.name());
                todoLabel.getStyleClass().add("label-warning");
                container.getChildren().addAll(label, todoLabel);
                break;
        }

        return container;
    }

    private void loadCodigosFabricaToUI(Set<CodigoFabrica> codigosFabrica) {
        flowPaneCodigosFabrica.getChildren().clear();
        codigosFabricaData.clear();

        if (codigosFabrica == null || codigosFabrica.isEmpty()) {
            return;
        }

        codigosFabricaData.addAll(codigosFabrica);

        for (CodigoFabrica codigo : codigosFabrica) {
            Label codigoLabel = new Label(codigo.getCodigo());
            codigoLabel.getStyleClass().addAll("tag", "tag-secondary");
            codigoLabel.setPadding(new Insets(4, 8, 4, 8));
            flowPaneCodigosFabrica.getChildren().add(codigoLabel);
        }
    }

    private void loadUbicacionesToUI(Set<Ubicacion> ubicaciones) {
        flowPaneUbicaciones.getChildren().clear();
        ubicacionesData.clear();

        if (ubicaciones == null || ubicaciones.isEmpty()) {
            return;
        }

        ubicacionesData.addAll(ubicaciones);

        // Crear ToggleGroup para los radio buttons
        ToggleGroup ubicacionGroup = new ToggleGroup();

        // Ordenar ubicaciones alfabéticamente
        List<Ubicacion> ubicacionesOrdenadas = ubicaciones.stream()
            .sorted(Comparator.comparing(Ubicacion::getNombre))
            .collect(Collectors.toList());

        // Determinar cuál debe ser la principal
        Ubicacion ubicacionPrincipal = determinarUbicacionPrincipal(ubicacionesOrdenadas);

        for (Ubicacion ubicacion : ubicacionesOrdenadas) {
            HBox ubicacionContainer = new HBox(5);
            ubicacionContainer.setAlignment(Pos.CENTER_LEFT);

            RadioButton radioButton = new RadioButton();
            radioButton.setToggleGroup(ubicacionGroup);
            radioButton.setSelected(ubicacion.equals(ubicacionPrincipal));

            // Listener para actualizar la ubicación principal
            radioButton.selectedProperty().addListener((obs, oldVal, newVal) -> {
                if (newVal) {
                    actualizarUbicacionPrincipal(ubicacion);
                }
            });

            Label nombreLabel = new Label(ubicacion.getNombre());
            nombreLabel.getStyleClass().add("field-label");

            ubicacionContainer.getChildren().addAll(radioButton, nombreLabel);
            flowPaneUbicaciones.getChildren().add(ubicacionContainer);
        }
    }

    private Ubicacion determinarUbicacionPrincipal(List<Ubicacion> ubicaciones) {
        if (ubicaciones.isEmpty()) {
            return null;
        }

        // Buscar una ubicación marcada como principal
        Optional<Ubicacion> principal = ubicaciones.stream()
            .filter(u -> u.getIsPrincipal() != null && u.getIsPrincipal())
            .findFirst();

        if (principal.isPresent()) {
            return principal.get();
        }

        // Si no hay ninguna marcada como principal, tomar la primera alfabéticamente
        return ubicaciones.get(0);
    }

    private void actualizarUbicacionPrincipal(Ubicacion nuevaPrincipal) {
        // Marcar todas como no principales
        ubicacionesData.forEach(u -> u.setIsPrincipal(false));

        // Marcar la nueva como principal
        nuevaPrincipal.setIsPrincipal(true);

        log.debug("Ubicación principal actualizada a: {}", nuevaPrincipal.getNombre());
    }

    private String formatDecimal(Double value) {
        return value != null ? String.valueOf(value) : "";
    }

    // ========== MÉTODOS DE LIMPIEZA ==========

    private void clearAllData() {
        currentProducto.set(null);
        currentItem.set(null);
        currentMarca.set(null);
        generatedCodCompuesto.set(null);

        clearUIFields();
        clearFlowPanes();
        clearObservableLists();
    }

    private void clearUIFields() {
        // Solo limpiar campos que no están controlados por bindings
        if (currentState.get() == ControllerState.INITIAL) {
            txtBuscarCodProductoItem.clear();
        }
        txtMarca.clear();
        txtDescripcion.clear();
        txtBuscarCodigoFabrica.clear();
        txtBuscarUbicaciones.clear();

        txtPrecioCostoPromedio.clear();
        txtPrecioVentaBase.clear();
        txtPrecioVentaPromocion.clear();
        txtPrecioVentaPublico.clear();
        txtStockTotal.clear();
        txtStockDeSeguridad.clear();
        txtAnotaciones.clear();
    }

    private void clearFlowPanes() {
        flowPaneGrupos.getChildren().clear();
        flowPaneAtributos.getChildren().clear();
        flowPaneCodigosFabrica.getChildren().clear();
        flowPaneUbicaciones.getChildren().clear();
        flowPaneArchivos.getChildren().clear();
    }

    private void clearObservableLists() {
        atributosData.clear();
        codigosFabricaData.clear();
        ubicacionesData.clear();
    }

    // ========== MÉTODOS DE ACCIÓN ==========

    private void guardarItem() {
        Item item = currentItem.get();
        if (item == null) {
            showErrorMessage("No hay item para guardar");
            return;
        }

        // Validar datos antes de guardar
        if (!validarDatos(item)) {
            return;
        }

        // Actualizar item con datos de la UI
        actualizarItemConDatosUI(item);

        isLoading.set(true);

        boolean isCreating = currentState.get() == ControllerState.CREATING_NEW;

        if (isCreating) {
            // Crear nuevo item
            subscribeMonoWithUiUpdate(
                itemMantenimientoService.crearItem(item),
                this::onItemGuardado,
                this::onErrorGuardando
            );
        } else {
            // Actualizar item existente
            subscribeMonoWithUiUpdate(
                itemMantenimientoService.actualizarItem(item),
                this::onItemGuardado,
                this::onErrorGuardando
            );
        }
    }

    private boolean validarDatos(Item item) {
        if (item.getDescripcion() == null || item.getDescripcion().trim().isEmpty()) {
            showErrorMessage("La descripción es obligatoria");
            return false;
        }

        if (item.getMarca() == null) {
            showErrorMessage("La marca es obligatoria");
            return false;
        }

        if (item.getProducto() == null) {
            showErrorMessage("El producto es obligatorio");
            return false;
        }

        return true;
    }

    private void actualizarItemConDatosUI(Item item) {
        // Actualizar descripción
        item.setDescripcion(txtDescripcion.getText());

        // Actualizar precios y stock
        item.setPrecioCostoPromedio(parseDecimal(txtPrecioCostoPromedio.getText()));
        item.setPrecioVentaBase(parseDecimal(txtPrecioVentaBase.getText()));
        item.setPrecioVentaPromocion(parseDecimal(txtPrecioVentaPromocion.getText()));
        item.setPrecioVentaPublico(parseDecimal(txtPrecioVentaPublico.getText()));
        item.setStockTotal(parseDecimal(txtStockTotal.getText()));
        item.setStockDeSeguridad(parseDecimal(txtStockDeSeguridad.getText()));

        // Actualizar anotaciones
        item.setAnotacionesOld(txtAnotaciones.getText());

        // Los atributos ya se actualizan automáticamente a través de los listeners
        // Las ubicaciones también se actualizan automáticamente
    }

    private Double parseDecimal(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(text.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private void onItemGuardado(Item itemGuardado) {
        log.info("Item guardado exitosamente: {}", itemGuardado.getCodCompuesto());

        boolean wasCreating = currentState.get() == ControllerState.CREATING_NEW;
        String mensaje = wasCreating ? "Item creado exitosamente" : "Item actualizado exitosamente";

        alertUtil.showInfo("Éxito", mensaje);

        // Recargar datos frescos desde el servidor
        recargarDatosFrescos(itemGuardado.getCodCompuesto(),
                           itemGuardado.getProducto() != null ? itemGuardado.getProducto().getCodProductoOld() : null);
    }

    /**
     * Recarga datos frescos del Item y Producto desde el servidor después de una operación de guardado
     */
    private void recargarDatosFrescos(String codCompuesto, String codProductoOld) {
        log.info("Recargando datos frescos - Item: {}, Producto: {}", codCompuesto, codProductoOld);

        // Mantener isLoading activo durante la recarga
        // isLoading ya está en true desde guardarItem()

        try {
            // Primero recargar el Item
            Optional<Item> itemOpt = itemMantenimientoService.buscarItemPorCodCompuesto(codCompuesto)
                    .onErrorResume(error -> {
                        log.debug("Error recargando item, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            if (itemOpt.isPresent() && itemOpt.get() != null) {
                Item itemFresco = itemOpt.get();
                log.info("Item fresco recargado: {}", itemFresco.getCodCompuesto());

                // Si tenemos código de producto, recargar también el producto
                if (codProductoOld != null && !codProductoOld.trim().isEmpty()) {
                    Optional<Producto> productoOpt = productoMantenimientoService.buscarProductoPorCodProductoOld(codProductoOld)
                            .onErrorResume(error -> {
                                log.debug("Error recargando producto, retornando Mono vacío: {}", error.getMessage());
                                return Mono.empty();
                            })
                            .blockOptional(Duration.ofSeconds(10));

                    if (productoOpt.isPresent() && productoOpt.get() != null) {
                        Producto productoFresco = productoOpt.get();
                        log.info("Producto fresco recargado: {}", productoFresco.getCodProductoOld());

                        // Actualizar el producto en el item
                        itemFresco.setProducto(productoFresco);
                    }
                }

                // Actualizar con datos frescos
                currentItem.set(itemFresco);
                currentProducto.set(itemFresco.getProducto());
                currentMarca.set(itemFresco.getMarca());

                isLoading.set(false);

                // Transicionar al estado de item cargado
                transitionToState(ControllerState.ITEM_LOADED);

            } else {
                isLoading.set(false);
                log.warn("No se pudo recargar el item con código: {}", codCompuesto);
                showErrorMessage("No se pudo recargar los datos del item guardado");

                // Transicionar al estado inicial como fallback
                transitionToState(ControllerState.INITIAL);
            }

        } catch (RuntimeException e) {
            isLoading.set(false);
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.debug("Recarga de datos interrumpida");
                showErrorMessage("Recarga de datos interrumpida");
            } else {
                log.error("Error recargando datos frescos: {}", e.getMessage(), e);
                showErrorMessage("Error al recargar datos frescos: " + e.getMessage());
            }

            // Transicionar al estado inicial como fallback
            transitionToState(ControllerState.INITIAL);

        } catch (Exception e) {
            isLoading.set(false);
            log.error("Error recargando datos frescos: {}", e.getMessage(), e);
            showErrorMessage("Error al recargar datos frescos: " + e.getMessage());

            // Transicionar al estado inicial como fallback
            transitionToState(ControllerState.INITIAL);
        }
    }

    private void onErrorGuardando(Throwable error) {
        isLoading.set(false);

        log.error("Error guardando item: {}", error.getMessage(), error);
        showErrorMessage("Error guardando el item: " + error.getMessage());
    }

    private void cancelarOperacion() {
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Cancelar",
            null,
            "¿Está seguro que desea cancelar? Se perderán los cambios no guardados.",
            "Sí, cancelar",
            "No"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            transitionToState(ControllerState.INITIAL);
        }
    }

    private void agregarImagen() {
        // TODO: Implementar funcionalidad de agregar imágenes
        alertUtil.showInfo("Pendiente", "Funcionalidad de imágenes pendiente de implementación");
    }

    // ========== MÉTODOS AUXILIARES ==========

    private void showErrorMessage(String message) {
        alertUtil.showError(message);
    }



    // ========== MÉTODOS PÚBLICOS PARA INTEGRACIÓN ==========

    /**
     * Método para cargar un Producto externamente usando su codProductoOld
     * Carga datos frescos desde el servicio
     */
    public void setProducto(String codProductoOld) {
        if (codProductoOld != null && !codProductoOld.trim().isEmpty()) {
            // Asegurar que estamos en estado inicial antes de cargar
            if (currentState.get() != ControllerState.INITIAL) {
                transitionToState(ControllerState.INITIAL);
            }

            txtBuscarCodProductoItem.setText(codProductoOld.trim());
            buscarProductoPorCodigo(codProductoOld.trim());
        }
    }

    /**
     * Método para cargar un Item externamente usando su codCompuesto
     * Carga datos frescos desde el servicio
     */
    public void setItem(String codCompuesto) {
        if (codCompuesto != null && !codCompuesto.trim().isEmpty()) {
            // Asegurar que estamos en estado inicial antes de cargar
            if (currentState.get() != ControllerState.INITIAL) {
                transitionToState(ControllerState.INITIAL);
            }

            txtBuscarCodProductoItem.setText(codCompuesto.trim());
            buscarItemPorCodigo(codCompuesto.trim());
        }
    }

    /**
     * Método auxiliar para buscar un Producto por código
     */
    private void buscarProductoPorCodigo(String codProductoOld) {
        log.info("Cargando producto fresco con código: {}", codProductoOld);
        isLoading.set(true);

        try {
            Optional<Producto> productoOpt = productoMantenimientoService.buscarProductoPorCodProductoOld(codProductoOld)
                    .onErrorResume(error -> {
                        log.debug("Error en búsqueda de producto, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            isLoading.set(false);

            if (productoOpt.isPresent() && productoOpt.get() != null) {
                Producto producto = productoOpt.get();
                log.info("Producto fresco cargado: {}", producto.getCodProductoOld());
                loadProducto(producto);
            } else {
                log.warn("No se encontró producto con código: {}", codProductoOld);
                showErrorMessage("No se encontró producto con código: " + codProductoOld);
            }

        } catch (RuntimeException e) {
            isLoading.set(false);
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.debug("Búsqueda de producto interrumpida");
                showErrorMessage("Búsqueda de producto interrumpida");
            } else {
                log.error("Error cargando producto: {}", e.getMessage(), e);
                showErrorMessage("Error al cargar producto: " + e.getMessage());
            }
        } catch (Exception e) {
            isLoading.set(false);
            log.error("Error cargando producto: {}", e.getMessage(), e);
            showErrorMessage("Error al cargar producto: " + e.getMessage());
        }
    }

    /**
     * Método auxiliar para buscar un Item por código
     */
    private void buscarItemPorCodigo(String codCompuesto) {
        log.info("Cargando item fresco con código: {}", codCompuesto);
        isLoading.set(true);

        try {
            Optional<Item> itemOpt = itemMantenimientoService.buscarItemPorCodCompuesto(codCompuesto)
                    .onErrorResume(error -> {
                        log.debug("Error en búsqueda de item, retornando Mono vacío: {}", error.getMessage());
                        return Mono.empty();
                    })
                    .blockOptional(Duration.ofSeconds(10));

            isLoading.set(false);

            if (itemOpt.isPresent() && itemOpt.get() != null) {
                Item item = itemOpt.get();
                log.info("Item fresco cargado: {}", item.getCodCompuesto());
                loadItem(item);
            } else {
                log.warn("No se encontró item con código: {}", codCompuesto);
                showErrorMessage("No se encontró item con código: " + codCompuesto);
            }

        } catch (RuntimeException e) {
            isLoading.set(false);
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.debug("Búsqueda de item interrumpida");
                showErrorMessage("Búsqueda de item interrumpida");
            } else {
                log.error("Error cargando item: {}", e.getMessage(), e);
                showErrorMessage("Error al cargar item: " + e.getMessage());
            }
        } catch (Exception e) {
            isLoading.set(false);
            log.error("Error cargando item: {}", e.getMessage(), e);
            showErrorMessage("Error al cargar item: " + e.getMessage());
        }
    }

    enum ControllerState {
        INITIAL,        // Estado inicial - solo búsqueda habilitada
        PRODUCTO_LOADED, // Producto cargado - marca habilitada
        ITEM_LOADED,    // Item cargado - todos los campos habilitados para edición
        CREATING_NEW,   // Creando nuevo item
        EDITING         // Editando item existente
    }

}
