package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemMantenimientoService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_BY_COD_COMPUESTO = "item-mantenimiento.search.by-cod-compuesto";
    private static final String ROUTE_EXISTS_BY_COD_COMPUESTO = "item-mantenimiento.exists.by-cod-compuesto";
    private static final String ROUTE_CREATE = "item-mantenimiento.create";
    private static final String ROUTE_UPDATE = "item-mantenimiento.update";

    private final ConnectionService connectionService;

    /**
     * Busca un Item por su código compuesto.
     *
     * @param codCompuesto Código compuesto del Item.
     * @return Un Mono con el Item encontrado o vacío si no existe.
     */
    public Mono<Item> buscarItemPorCodCompuesto(String codCompuesto) {
        log.debug("Buscando Item por codCompuesto: {}", codCompuesto);
        return connectionService.authenticatedRequest(ROUTE_SEARCH_BY_COD_COMPUESTO, codCompuesto, Item.class);
    }

    /**
     * Verifica si existe un Item con el codCompuesto especificado.
     *
     * @param codCompuesto Código compuesto del Item a verificar.
     * @return Un Mono con true si existe, false en caso contrario.
     */
    public Mono<Boolean> existeItemPorCodCompuesto(String codCompuesto) {
        log.debug("Verificando existencia de Item por codCompuesto: {}", codCompuesto);
        return connectionService.authenticatedRequest(ROUTE_EXISTS_BY_COD_COMPUESTO, codCompuesto, Boolean.class);
    }

    /**
     * Crea un nuevo Item.
     * Genera automáticamente el codCompuesto usando la lógica establecida
     * (Producto.codProductoOld + Marca.abreviacion). Procesa los atributos del item para generar sus IDs.
     * Valida que el codCompuesto generado no exista previamente.
     *
     * @param item El Item a crear.
     * @return Un Mono con el Item creado o error si ya existe.
     */
    public Mono<Item> crearItem(Item item) {
        log.debug("Creando nuevo Item con codCompuesto: {}", item.getCodCompuesto());
        return connectionService.authenticatedRequest(ROUTE_CREATE, item, Item.class);
    }

    /**
     * Actualiza un Item existente.
     * Valida que exista el Item con el codCompuesto especificado.
     * Procesa los atributos del item para generar sus IDs según la lógica establecida.
     * Usa transaccionalidad para garantizar la consistencia de los datos.
     * 
     * IMPORTANTE: El codCompuesto NO se puede cambiar en una actualización, ya que es el ID del Item.
     * Si se necesita cambiar el Producto o la Marca, se debe eliminar el Item y crear uno nuevo.
     *
     * @param itemActualizado El Item con los datos actualizados.
     * @return Un Mono con el Item actualizado.
     */
    public Mono<Item> actualizarItem(Item itemActualizado) {
        log.debug("Actualizando Item con codCompuesto: {}", itemActualizado.getCodCompuesto());
        return connectionService.authenticatedRequest(ROUTE_UPDATE, itemActualizado, Item.class);
    }
}