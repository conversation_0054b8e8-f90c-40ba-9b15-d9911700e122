package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoGrupo;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class Grupo {
    private String id;//por ahora va ser la abreviación de la Categoria de la db antigua

    private TipoGrupo tipo; //categoria, sub categoria, familia, etc

    private Set<NombreGrupo> nombresGrupo; //asegurarnos de que cada nombresGrupo le pertenezca solo a un grupo

    private Set<Grupo> subGrupos;

    private List<GrupoFiltroRelation> filtros;//aquí es list xq se puede repetir un filtro

}
