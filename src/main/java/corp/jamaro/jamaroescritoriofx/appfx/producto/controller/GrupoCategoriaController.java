package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.GrupoFiltroRelation;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.NombreGrupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoGrupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.GrupoCategoriaService;
import javafx.application.Platform;
import javafx.beans.property.ReadOnlyObjectWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.DataFormat;
import javafx.scene.input.Dragboard;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.util.converter.IntegerStringConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.kordamp.ikonli.javafx.FontIcon;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class GrupoCategoriaController extends BaseController {

    private final GrupoCategoriaService grupoCategoriaService;

    // FXML Components
    @FXML private CustomTextField txtBuscarGrupoCategoria;
    @FXML private ProgressIndicator progressIndicator;
    @FXML private TextField txtId;
    @FXML private Label lblNombrePrincipal;
    @FXML private Button btnNuevo;
    @FXML private Button btnGuardar;
    @FXML private Button btnCancelar;
    @FXML private Button btnAgregarNombre;
    @FXML private Button btnAgregarFiltro;
    @FXML private TableView<NombreGrupo> tblNombres;
    @FXML private TableColumn<NombreGrupo, String> colNombreTexto;
    @FXML private TableColumn<NombreGrupo, Boolean> colNombrePrincipal;
    @FXML private TableColumn<NombreGrupo, Void> colNombreAcciones;
    @FXML private TableView<GrupoFiltroRelation> tblFiltros;
    @FXML private TableColumn<GrupoFiltroRelation, String> colFiltroNombre;
    @FXML private TableColumn<GrupoFiltroRelation, TipoFiltro> colFiltroTipo;
    @FXML private TableColumn<GrupoFiltroRelation, Integer> colFiltroOrden;
    @FXML private TableColumn<GrupoFiltroRelation, Void> colFiltroAcciones;

    // State variables
    private Grupo currentGrupo;
    private boolean isCreatingNew = false;
    private AutoCompletionBinding<NombreGrupo> autoCompletionBinding;
    private ObservableList<NombreGrupo> nombresData = FXCollections.observableArrayList();
    private ObservableList<GrupoFiltroRelation> filtrosData = FXCollections.observableArrayList();

    // Callback for external group creation
    private java.util.function.Consumer<Grupo> onGroupCreatedCallback;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        setupTables();
        setupEventHandlers();
        setupInitialState();
    }

    private void setupTables() {
        setupNombresTable();
        setupFiltrosTable();
    }

    private void setupNombresTable() {
        // Configure columns
        colNombreTexto.setCellValueFactory(new PropertyValueFactory<>("nombre"));
        colNombreTexto.setCellFactory(TextFieldTableCell.forTableColumn());
        colNombreTexto.setOnEditCommit(event -> {
            NombreGrupo nombre = event.getRowValue();
            nombre.setNombre(event.getNewValue());

            // If this is the principal nombre, update the label
            if (Boolean.TRUE.equals(nombre.getIsPrincipal())) {
                lblNombrePrincipal.setText(event.getNewValue());
            }
        });

        colNombrePrincipal.setCellValueFactory(cellData -> 
            new ReadOnlyObjectWrapper<>(cellData.getValue().getIsPrincipal()));
        colNombrePrincipal.setCellFactory(column -> new TableCell<NombreGrupo, Boolean>() {
            private final RadioButton radioButton = new RadioButton();
            private final ToggleGroup toggleGroup = new ToggleGroup();

            {
                radioButton.setToggleGroup(toggleGroup);
                radioButton.setOnAction(e -> {
                    NombreGrupo item = getTableView().getItems().get(getIndex());
                    if (radioButton.isSelected()) {
                        setPrincipalNombre(item);
                    }
                });
            }

            @Override
            protected void updateItem(Boolean item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setGraphic(null);
                } else {
                    radioButton.setSelected(item);
                    setGraphic(radioButton);
                    setAlignment(Pos.CENTER);
                }
            }
        });

        colNombreAcciones.setCellFactory(column -> new TableCell<NombreGrupo, Void>() {
            private final Button deleteButton = new Button();

            {
                deleteButton.setGraphic(new FontIcon("fas-trash"));
                deleteButton.getStyleClass().addAll("button", "danger-button");
                deleteButton.setOnAction(e -> {
                    NombreGrupo item = getTableView().getItems().get(getIndex());
                    deleteNombre(item);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, deleteButton);
                    buttons.setAlignment(Pos.CENTER);
                    setGraphic(buttons);
                }
            }
        });

        tblNombres.setItems(nombresData);
    }

    private void setupFiltrosTable() {
        // Configure columns
        colFiltroNombre.setCellValueFactory(cellData -> 
            new ReadOnlyObjectWrapper<>(cellData.getValue().getFiltro().getNombreFiltro()));
        colFiltroNombre.setCellFactory(TextFieldTableCell.forTableColumn());
        colFiltroNombre.setOnEditCommit(event -> {
            GrupoFiltroRelation filtroRelation = event.getRowValue();
            filtroRelation.getFiltro().setNombreFiltro(event.getNewValue());
        });

        colFiltroTipo.setCellValueFactory(cellData -> 
            new ReadOnlyObjectWrapper<>(cellData.getValue().getFiltro().getTipo()));
        colFiltroTipo.setCellFactory(ComboBoxTableCell.forTableColumn(TipoFiltro.values()));
        colFiltroTipo.setOnEditCommit(event -> {
            GrupoFiltroRelation filtro = event.getRowValue();
            filtro.getFiltro().setTipo(event.getNewValue());
        });

        colFiltroOrden.setCellValueFactory(new PropertyValueFactory<>("orden"));
        colFiltroOrden.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        colFiltroOrden.setOnEditCommit(event -> {
            GrupoFiltroRelation filtro = event.getRowValue();
            filtro.setOrden(event.getNewValue());
            // Sort the list by orden
            sortFiltrosByOrden();
        });

        // Enable drag and drop reordering
        setupDragAndDropReordering();

        colFiltroAcciones.setCellFactory(column -> new TableCell<GrupoFiltroRelation, Void>() {
            private final Button deleteButton = new Button();
            private final Button imageButton = new Button();
            private final Button viewImageButton = new Button();

            {
                deleteButton.setGraphic(new FontIcon("fas-trash"));
                deleteButton.getStyleClass().addAll("button", "danger-button");
                deleteButton.setOnAction(e -> {
                    GrupoFiltroRelation item = getTableView().getItems().get(getIndex());
                    deleteFiltro(item);
                });

                imageButton.setGraphic(new FontIcon("fas-image"));
                imageButton.getStyleClass().addAll("button", "secondary-button");
                imageButton.setOnAction(e -> {
                    // TODO: Implement image upload functionality
                    log.info("Agregar imagen - funcionalidad pendiente");
                });

                viewImageButton.setGraphic(new FontIcon("fas-eye"));
                viewImageButton.getStyleClass().addAll("button", "info-button");
                viewImageButton.setOnAction(e -> {
                    // TODO: Implement view image functionality
                    log.info("Ver imagen - funcionalidad pendiente");
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, imageButton, viewImageButton, deleteButton);
                    buttons.setAlignment(Pos.CENTER);
                    setGraphic(buttons);
                }
            }
        });

        tblFiltros.setItems(filtrosData);
        tblFiltros.setEditable(true);
    }

    private void setupDragAndDropReordering() {
        tblFiltros.setRowFactory(tv -> {
            TableRow<GrupoFiltroRelation> row = new TableRow<>();

            row.setOnDragDetected(event -> {
                if (!row.isEmpty()) {
                    Integer index = row.getIndex();
                    Dragboard db = row.startDragAndDrop(TransferMode.MOVE);
                    db.setDragView(row.snapshot(null, null));
                    ClipboardContent cc = new ClipboardContent();
                    cc.put(DataFormat.PLAIN_TEXT, index.toString());
                    db.setContent(cc);
                    event.consume();
                }
            });

            row.setOnDragOver(event -> {
                Dragboard db = event.getDragboard();
                if (db.hasString()) {
                    if (row.getIndex() != Integer.parseInt(db.getString())) {
                        event.acceptTransferModes(TransferMode.COPY_OR_MOVE);
                        event.consume();
                    }
                }
            });

            row.setOnDragDropped(event -> {
                Dragboard db = event.getDragboard();
                if (db.hasString()) {
                    int draggedIndex = Integer.parseInt(db.getString());
                    GrupoFiltroRelation draggedItem = tblFiltros.getItems().remove(draggedIndex);

                    int dropIndex;
                    if (row.isEmpty()) {
                        dropIndex = tblFiltros.getItems().size();
                    } else {
                        dropIndex = row.getIndex();
                    }

                    tblFiltros.getItems().add(dropIndex, draggedItem);

                    // Update orden values based on new positions
                    for (int i = 0; i < filtrosData.size(); i++) {
                        filtrosData.get(i).setOrden(i + 1);
                    }

                    tblFiltros.refresh();
                    event.setDropCompleted(true);
                    event.consume();
                }
            });

            return row;
        });
    }

    private void setupEventHandlers() {
        setupSearchAutoCompletion();
        setupButtonHandlers();
        setupIdFieldHandlers();
    }

    private void setupSearchAutoCompletion() {
        autoCompletionBinding = TextFields.bindAutoCompletion(txtBuscarGrupoCategoria, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty() || isCreatingNew) {
                return Collections.emptyList();
            }

            progressIndicator.setVisible(true);

            List<NombreGrupo> suggestions = grupoCategoriaService.buscarNombresGrupoPorRegex(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));

            Platform.runLater(() -> progressIndicator.setVisible(false));

            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        autoCompletionBinding.setOnAutoCompleted(event -> {
            NombreGrupo selectedNombre = event.getCompletion();
            if (selectedNombre != null && selectedNombre.getId() != null) {
                loadGrupoByNombreId(selectedNombre.getId());
                txtBuscarGrupoCategoria.clear();
            }
        });
    }

    private void setupButtonHandlers() {
        btnNuevo.setOnAction(e -> startCreatingNew());
        btnGuardar.setOnAction(e -> saveGrupo());
        btnCancelar.setOnAction(e -> cancelOperation());
        btnAgregarNombre.setOnAction(e -> showAddNombreDialog());
        btnAgregarFiltro.setOnAction(e -> showAddFiltroDialog());
    }

    private void setupIdFieldHandlers() {
        txtId.setOnMouseClicked(e -> {
            if (e.getClickCount() == 2 && isCreatingNew) {
                txtId.setEditable(true);
                txtId.requestFocus();
                txtId.selectAll();
            }
        });

        txtId.focusedProperty().addListener((obs, wasFocused, isFocused) -> {
            if (!isFocused && isCreatingNew) {
                txtId.setEditable(false);
            }
        });
    }

    private void setupInitialState() {
        clearAll();
        enableSearchMode();
    }

    private void loadGrupoByNombreId(UUID nombreGrupoId) {
        subscribeMonoWithUiUpdate(
            grupoCategoriaService.obtenerGrupoPorNombreGrupoId(nombreGrupoId),
            this::loadGrupoData,
            logAndShowError("cargando grupo", this::showErrorMessage)
        );
    }

    private void loadGrupoData(Grupo grupo) {
        this.currentGrupo = grupo;
        this.isCreatingNew = false;

        // Load basic info
        txtId.setText(grupo.getId());
        txtId.setEditable(false);
        txtId.setDisable(true);

        // Load nombres
        nombresData.clear();
        if (grupo.getNombresGrupo() != null) {
            List<NombreGrupo> sortedNombres = grupo.getNombresGrupo().stream()
                    .sorted(Comparator.comparing(NombreGrupo::getCreatedAt))
                    .collect(Collectors.toList());
            nombresData.addAll(sortedNombres);

            // Update principal nombre label
            grupo.getNombresGrupo().stream()
                    .filter(n -> Boolean.TRUE.equals(n.getIsPrincipal()))
                    .findFirst()
                    .ifPresent(principal -> lblNombrePrincipal.setText(principal.getNombre()));
        }

        // Load filtros
        filtrosData.clear();
        if (grupo.getFiltros() != null) {
            List<GrupoFiltroRelation> sortedFiltros = grupo.getFiltros().stream()
                    .sorted(Comparator.comparing(GrupoFiltroRelation::getOrden))
                    .collect(Collectors.toList());
            filtrosData.addAll(sortedFiltros);
        }

        enableEditMode();
    }

    private void startCreatingNew() {
        clearAll();
        this.isCreatingNew = true;
        this.currentGrupo = new Grupo();

        // Initialize tipo as "categoria" for new grupos
        this.currentGrupo.setTipo(TipoGrupo.categoria);

        // Generate random UUID for new grupo
        String newId = UUID.randomUUID().toString();
        txtId.setText(newId);
        txtId.setEditable(false);
        txtId.setDisable(false);

        enableCreateMode();
    }

    private void saveGrupo() {
        if (currentGrupo == null) {
            showErrorMessage("No hay datos para guardar");
            return;
        }

        // Validate required fields
        String grupoId = txtId.getText().trim();
        if (grupoId == null || grupoId.isEmpty()) {
            showErrorMessage("El ID del Grupo no puede estar vacío");
            return;
        }

        if (nombresData.isEmpty()) {
            showErrorMessage("Debe agregar al menos un Nombre para el Grupo");
            return;
        }

        // Update current grupo with form data
        currentGrupo.setId(grupoId);
        currentGrupo.setNombresGrupo(new HashSet<>(nombresData));
        currentGrupo.setFiltros(new ArrayList<>(filtrosData));

        if (isCreatingNew) {
            subscribeMonoWithUiUpdate(
                grupoCategoriaService.crearGrupoCategoria(currentGrupo),
                this::onSaveSuccess,
                logAndShowError("creando grupo categoria", this::showErrorMessage)
            );
        } else {
            subscribeMonoWithUiUpdate(
                grupoCategoriaService.actualizarGrupoCategoria(currentGrupo),
                this::onSaveSuccess,
                logAndShowError("actualizando grupo categoria", this::showErrorMessage)
            );
        }
    }

    private void onSaveSuccess(Grupo savedGrupo) {
        log.info("Grupo guardado exitosamente: {}", savedGrupo.getId());
        showSuccessMessage("Grupo guardado exitosamente");

        // If there's a callback (external group creation), call it
        if (onGroupCreatedCallback != null) {
            onGroupCreatedCallback.accept(savedGrupo);
            onGroupCreatedCallback = null; // Clear callback after use
        } else {
            // Normal operation: clear form after successful save
            cancelOperation();
        }
    }

    private void cancelOperation() {
        clearAll();
        enableSearchMode();
    }

    private void showAddNombreDialog() {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Agregar Nombre");
        dialog.setHeaderText("Ingrese un nuevo nombre para la categoría");
        dialog.setContentText("Nombre:");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(nombre -> {
            if (!nombre.trim().isEmpty()) {
                NombreGrupo nuevoNombre = new NombreGrupo();
                nuevoNombre.setId(UUID.randomUUID());
                nuevoNombre.setNombre(nombre.trim());
                nuevoNombre.setIsPrincipal(nombresData.isEmpty()); // First one is principal
                nuevoNombre.setCreatedAt(Instant.now());

                nombresData.add(nuevoNombre);

                if (nuevoNombre.getIsPrincipal()) {
                    lblNombrePrincipal.setText(nuevoNombre.getNombre());
                }
            }
        });
    }

    private void showAddFiltroDialog() {
        Dialog<Filtro> dialog = new Dialog<>();
        dialog.setTitle("Agregar Filtro");
        dialog.setHeaderText("Crear un nuevo filtro");

        // Apply styles
        dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        // Create form
        TextField nombreField = new TextField();
        nombreField.setPromptText("Nombre del filtro");

        ComboBox<TipoFiltro> tipoCombo = new ComboBox<>();
        tipoCombo.getItems().addAll(TipoFiltro.values());
        tipoCombo.setPromptText("Seleccione tipo");

        dialog.getDialogPane().setContent(new javafx.scene.layout.VBox(10, 
            new Label("Nombre del Filtro:"), nombreField,
            new Label("Tipo:"), tipoCombo));

        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        dialog.setResultConverter(buttonType -> {
            if (buttonType == ButtonType.OK && !nombreField.getText().trim().isEmpty() && tipoCombo.getValue() != null) {
                Filtro filtro = new Filtro();
                filtro.setId(null);
                filtro.setNombreFiltro(nombreField.getText().trim());
                filtro.setTipo(tipoCombo.getValue());
                return filtro;
            }
            return null;
        });

        Optional<Filtro> result = dialog.showAndWait();
        result.ifPresent(filtro -> {
            GrupoFiltroRelation relation = new GrupoFiltroRelation();
            relation.setId(null);
            relation.setFiltro(filtro);
            relation.setOrden(filtrosData.size() + 1);

            filtrosData.add(relation);
            sortFiltrosByOrden();
        });
    }

    private void setPrincipalNombre(NombreGrupo selectedNombre) {
        // Set all to false first
        nombresData.forEach(nombre -> nombre.setIsPrincipal(false));
        // Set selected as principal
        selectedNombre.setIsPrincipal(true);
        // Update label
        lblNombrePrincipal.setText(selectedNombre.getNombre());
        // Refresh table
        tblNombres.refresh();
    }

    private void deleteNombre(NombreGrupo nombre) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmar eliminación");
        alert.setHeaderText("¿Está seguro de eliminar este nombre?");
        alert.setContentText("Nombre: " + nombre.getNombre());

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            nombresData.remove(nombre);

            // If it was principal, set first one as principal
            if (Boolean.TRUE.equals(nombre.getIsPrincipal()) && !nombresData.isEmpty()) {
                NombreGrupo firstNombre = nombresData.get(0);
                firstNombre.setIsPrincipal(true);
                lblNombrePrincipal.setText(firstNombre.getNombre());
                tblNombres.refresh();
            } else if (nombresData.isEmpty()) {
                lblNombrePrincipal.setText("");
            }
        }
    }

    private void deleteFiltro(GrupoFiltroRelation filtro) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmar eliminación");
        alert.setHeaderText("¿Está seguro de eliminar este filtro?");
        alert.setContentText("Filtro: " + filtro.getFiltro().getNombreFiltro());

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            filtrosData.remove(filtro);
            // Reorder remaining filtros
            for (int i = 0; i < filtrosData.size(); i++) {
                filtrosData.get(i).setOrden(i + 1);
            }
            tblFiltros.refresh();
        }
    }

    private void sortFiltrosByOrden() {
        FXCollections.sort(filtrosData, Comparator.comparing(GrupoFiltroRelation::getOrden));
        tblFiltros.refresh();
    }

    private void clearAll() {
        currentGrupo = null;
        isCreatingNew = false;

        txtId.clear();
        txtId.setEditable(false);
        txtId.setDisable(true);
        txtBuscarGrupoCategoria.clear();
        lblNombrePrincipal.setText("");

        nombresData.clear();
        filtrosData.clear();
    }

    private void enableSearchMode() {
        txtBuscarGrupoCategoria.setDisable(false);
        btnNuevo.setDisable(false);
        btnGuardar.setDisable(true);
        btnCancelar.setDisable(true);
        btnAgregarNombre.setDisable(true);
        btnAgregarFiltro.setDisable(true);
        tblNombres.setDisable(true);
        tblFiltros.setDisable(true);
    }

    private void enableEditMode() {
        txtBuscarGrupoCategoria.setDisable(true);
        btnNuevo.setDisable(false);
        btnGuardar.setDisable(false);
        btnCancelar.setDisable(false);
        btnAgregarNombre.setDisable(false);
        btnAgregarFiltro.setDisable(false);
        tblNombres.setDisable(false);
        tblFiltros.setDisable(false);
    }

    private void enableCreateMode() {
        txtBuscarGrupoCategoria.setDisable(true);
        btnNuevo.setDisable(true);
        btnGuardar.setDisable(false);
        btnCancelar.setDisable(false);
        btnAgregarNombre.setDisable(false);
        btnAgregarFiltro.setDisable(false);
        tblNombres.setDisable(false);
        tblFiltros.setDisable(false);
    }

    /**
     * Agnostic method to create a new group with a predefined name.
     * This method puts the controller in creation mode and sets up a callback
     * to be executed when the group is successfully created.
     *
     * @param groupName The name for the new group
     * @param onGroupCreated Callback to execute when the group is created successfully
     */
    public void createGroupWithName(String groupName, java.util.function.Consumer<Grupo> onGroupCreated) {
        // Set up the callback
        this.onGroupCreatedCallback = onGroupCreated;

        // Start creating new group
        startCreatingNew();

        // Add the provided name as the principal name
        if (groupName != null && !groupName.trim().isEmpty()) {
            NombreGrupo nuevoNombre = new NombreGrupo();
            nuevoNombre.setId(UUID.randomUUID());
            nuevoNombre.setNombre(groupName.trim());
            nuevoNombre.setIsPrincipal(true); // This will be the principal name
            nuevoNombre.setCreatedAt(Instant.now());

            nombresData.add(nuevoNombre);
            lblNombrePrincipal.setText(nuevoNombre.getNombre());
        }
    }

    private void showErrorMessage(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText("Ha ocurrido un error");
        alert.setContentText(message);

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        alert.showAndWait();
    }

    private void showSuccessMessage(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Éxito");
        alert.setHeaderText("Operación completada");
        alert.setContentText(message);

        // Apply styles
        alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());

        alert.showAndWait();
    }
}
