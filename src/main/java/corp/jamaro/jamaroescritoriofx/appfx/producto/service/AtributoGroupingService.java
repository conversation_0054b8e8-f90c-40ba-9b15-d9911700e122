package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Servicio para agrupar atributos por grupos y manejar la lógica de creación
 * de atributos automáticos cuando se agregan grupos a productos nuevos.
 */
@Service
@Slf4j
public class AtributoGroupingService {

    /**
     * Clase interna para almacenar información optimizada del grupo
     */
    private static class GrupoInfo {
        final String nombreGrupo;
        final Integer orden;

        GrupoInfo(String nombreGrupo, Integer orden) {
            this.nombreGrupo = nombreGrupo;
            this.orden = orden;
        }
    }
    
    /**
     * Agrupa los atributos existentes por grupos basándose en los filtros comunes.
     * SIEMPRE muestra TODOS los filtros de los grupos como atributos, incluso si no tienen datos.
     * Los atributos que no coincidan con ningún grupo van a "Extras".
     * Los atributos se ordenan según el orden definido en GrupoFiltroRelation.
     */
    public Map<String, List<Atributo>> groupExistingAtributos(
            Set<Atributo> atributos,
            Set<Grupo> grupos) {

        Map<String, List<Atributo>> groupedAtributos = new LinkedHashMap<>();
        List<Atributo> extraAtributos = new ArrayList<>();

        // Crear mapas optimizados para búsqueda y ordenamiento
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = createOptimizedFiltroToGrupoMap(grupos);

        // Crear mapa de atributos existentes por filtro ID para búsqueda rápida
        Map<UUID, Atributo> atributosPorFiltroId = new HashMap<>();
        for (Atributo atributo : atributos) {
            if (atributo.getFiltro() != null && atributo.getFiltro().getId() != null) {
                atributosPorFiltroId.put(atributo.getFiltro().getId(), atributo);
            }
        }

        // Procesar cada grupo y asegurar que TODOS sus filtros estén representados
        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);
            List<Atributo> atributosDelGrupo = new ArrayList<>();

            if (grupo.getFiltros() != null) {
                // Ordenar filtros por orden definido
                List<GrupoFiltroRelation> filtrosOrdenados = grupo.getFiltros().stream()
                        .sorted(Comparator.comparing(relation ->
                            relation.getOrden() != null ? relation.getOrden() : Integer.MAX_VALUE))
                        .collect(Collectors.toList());

                // Para cada filtro del grupo, crear o usar atributo existente
                for (GrupoFiltroRelation filtroRelation : filtrosOrdenados) {
                    Filtro filtro = filtroRelation.getFiltro();
                    if (filtro != null && filtro.getId() != null) {
                        Atributo atributo = atributosPorFiltroId.get(filtro.getId());

                        if (atributo != null) {
                            // Usar atributo existente
                            atributosDelGrupo.add(atributo);
                        } else {
                            // Crear atributo vacío para este filtro
                            Atributo nuevoAtributo = new Atributo();
                            nuevoAtributo.setId(null);
                            nuevoAtributo.setFiltro(filtro);
                            initializeAtributoDefaultValue(nuevoAtributo, filtro);
                            atributosDelGrupo.add(nuevoAtributo);

                            log.debug("Creado atributo vacío para filtro: {} en grupo: {}",
                                    filtro.getNombreFiltro(), grupoNombre);
                        }
                    }
                }
            }

            if (!atributosDelGrupo.isEmpty()) {
                groupedAtributos.put(grupoNombre, atributosDelGrupo);
                log.debug("Grupo {} tiene {} atributos (incluyendo vacíos)", grupoNombre, atributosDelGrupo.size());
            }
        }

        // Agregar atributos que no pertenecen a ningún grupo (Extras)
        for (Atributo atributo : atributos) {
            if (atributo.getFiltro() != null && atributo.getFiltro().getId() != null) {
                GrupoInfo grupoInfo = filtroToGrupoInfoMap.get(atributo.getFiltro().getId());
                if (grupoInfo == null) {
                    extraAtributos.add(atributo);
                }
            } else {
                extraAtributos.add(atributo);
            }
        }

        // Agregar extras si existen
        if (!extraAtributos.isEmpty()) {
            groupedAtributos.put("Extras", extraAtributos);
            log.debug("Agregados {} atributos extras", extraAtributos.size());
        }

        return groupedAtributos;
    }
    
    /**
     * Crea atributos automáticamente para un grupo cuando se agrega a un producto nuevo.
     * Los atributos se crean con UUID vacío y valores por defecto según el tipo de filtro.
     * Se mantiene el orden definido en GrupoFiltroRelation.
     */
    public List<Atributo> createAtributosForGrupo(Grupo grupo) {
        List<Atributo> nuevosAtributos = new ArrayList<>();

        if (grupo.getFiltros() == null || grupo.getFiltros().isEmpty()) {
            log.warn("Grupo {} no tiene filtros definidos", grupo.getId());
            return nuevosAtributos;
        }

        // Ordenar filtros por orden, manejando valores null
        List<GrupoFiltroRelation> filtrosOrdenados = grupo.getFiltros().stream()
                .sorted(Comparator.comparing(
                    relation -> relation.getOrden() != null ? relation.getOrden() : Integer.MAX_VALUE))
                .collect(Collectors.toList());

        // Crear atributo para cada filtro en el orden correcto
        for (GrupoFiltroRelation filtroRelation : filtrosOrdenados) {
            Filtro filtro = filtroRelation.getFiltro();

            if (filtro != null) {
                Atributo nuevoAtributo = new Atributo();
                nuevoAtributo.setId(null); // UUID vacío, el servidor lo asignará
                nuevoAtributo.setFiltro(filtro);

                // Inicializar con valores por defecto según el tipo
                initializeAtributoDefaultValue(nuevoAtributo, filtro);

                nuevosAtributos.add(nuevoAtributo);
                log.debug("Creado atributo para filtro: {} (orden: {})",
                        filtro.getNombreFiltro(), filtroRelation.getOrden());
            }
        }

        log.info("Creados {} atributos para grupo: {}",
                nuevosAtributos.size(), getGrupoPrincipalName(grupo));
        return nuevosAtributos;
    }
    
    /**
     * Actualiza los atributos existentes cuando se agregan o quitan grupos.
     * Mantiene los atributos existentes y agrega los nuevos necesarios.
     * Los nuevos atributos se crean respetando el orden del grupo.
     * IMPORTANTE: Conserva los datos de atributos que ya existían previamente.
     */
    public Set<Atributo> updateAtributosForGroups(
            Set<Atributo> atributosExistentes,
            Set<Grupo> gruposActuales) {

        // Crear mapa de todos los atributos que han existido alguna vez (incluyendo los actuales)
        // Esto permite recuperar datos cuando se vuelve a agregar un grupo
        Map<UUID, Atributo> todosLosAtributosPorFiltro = new HashMap<>();

        // Primero, mapear todos los atributos existentes
        for (Atributo atributo : atributosExistentes) {
            if (atributo.getFiltro() != null && atributo.getFiltro().getId() != null) {
                todosLosAtributosPorFiltro.put(atributo.getFiltro().getId(), atributo);
            }
        }

        Set<Atributo> atributosActualizados = new HashSet<>();

        // Crear mapa optimizado para verificación rápida
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = createOptimizedFiltroToGrupoMap(gruposActuales);

        // Para cada grupo actual, asegurar que TODOS sus filtros tengan atributos
        for (Grupo grupo : gruposActuales) {
            if (grupo.getFiltros() != null) {
                // Ordenar filtros por orden
                List<GrupoFiltroRelation> filtrosOrdenados = grupo.getFiltros().stream()
                        .sorted(Comparator.comparing(relation ->
                            relation.getOrden() != null ? relation.getOrden() : Integer.MAX_VALUE))
                        .collect(Collectors.toList());

                for (GrupoFiltroRelation filtroRelation : filtrosOrdenados) {
                    if (filtroRelation.getFiltro() != null && filtroRelation.getFiltro().getId() != null) {
                        UUID filtroId = filtroRelation.getFiltro().getId();

                        // Verificar si ya existe un atributo para este filtro
                        Atributo atributoExistente = todosLosAtributosPorFiltro.get(filtroId);

                        if (atributoExistente != null) {
                            // Usar el atributo existente (conserva los datos)
                            atributosActualizados.add(atributoExistente);
                            log.debug("Conservado atributo existente para filtro: {} con dato: '{}'",
                                    filtroRelation.getFiltro().getNombreFiltro(),
                                    atributoExistente.getDato());
                        } else {
                            // Crear nuevo atributo vacío
                            Atributo nuevoAtributo = new Atributo();
                            nuevoAtributo.setId(null);
                            nuevoAtributo.setFiltro(filtroRelation.getFiltro());
                            initializeAtributoDefaultValue(nuevoAtributo, filtroRelation.getFiltro());

                            atributosActualizados.add(nuevoAtributo);
                            log.debug("Creado nuevo atributo para filtro: {} (orden: {})",
                                    filtroRelation.getFiltro().getNombreFiltro(), filtroRelation.getOrden());
                        }
                    }
                }
            }
        }

        // Agregar atributos extras (que no pertenecen a ningún grupo actual)
        for (Atributo atributo : atributosExistentes) {
            if (atributo.getFiltro() != null && atributo.getFiltro().getId() != null) {
                GrupoInfo grupoInfo = filtroToGrupoInfoMap.get(atributo.getFiltro().getId());
                if (grupoInfo == null) {
                    // Este atributo no pertenece a ningún grupo actual, agregarlo como extra
                    atributosActualizados.add(atributo);
                }
            } else {
                // Atributos sin filtro definido, mantenerlos como extras
                atributosActualizados.add(atributo);
            }
        }

        log.info("Atributos actualizados: {} total (conservando datos existentes)",
                atributosActualizados.size());

        return atributosActualizados;
    }

    /**
     * Verifica y corrige el orden de atributos existentes según los grupos.
     * IMPORTANTE: Este método ahora asegura que se muestren TODOS los filtros de los grupos.
     * Útil para productos cargados desde el servidor.
     */
    public List<Atributo> ensureCorrectOrder(Set<Atributo> atributos, Set<Grupo> grupos) {
        Map<String, List<Atributo>> groupedAtributos = groupExistingAtributos(atributos, grupos);

        List<Atributo> orderedAtributos = new ArrayList<>();

        // Agregar atributos en el orden correcto por grupo
        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);
            List<Atributo> atributosDelGrupo = groupedAtributos.get(grupoNombre);

            if (atributosDelGrupo != null) {
                orderedAtributos.addAll(atributosDelGrupo);
            }
        }

        // Agregar extras al final
        List<Atributo> extras = groupedAtributos.get("Extras");
        if (extras != null) {
            orderedAtributos.addAll(extras);
        }

        log.debug("Orden asegurado: {} atributos totales (incluyendo filtros sin datos)",
                orderedAtributos.size());
        return orderedAtributos;
    }

    /**
     * Elimina los atributos asociados a un grupo específico
     */
    public Set<Atributo> removeAtributosForGrupo(Set<Atributo> atributosExistentes, Grupo grupoEliminado) {
        if (grupoEliminado == null || grupoEliminado.getFiltros() == null) {
            return new HashSet<>(atributosExistentes);
        }

        // Obtener IDs de filtros del grupo eliminado
        Set<UUID> filtrosDelGrupoEliminado = grupoEliminado.getFiltros().stream()
                .filter(relation -> relation.getFiltro() != null && relation.getFiltro().getId() != null)
                .map(relation -> relation.getFiltro().getId())
                .collect(Collectors.toSet());

        // Filtrar atributos que NO pertenecen al grupo eliminado
        Set<Atributo> atributosRestantes = atributosExistentes.stream()
                .filter(atributo -> {
                    if (atributo.getFiltro() == null || atributo.getFiltro().getId() == null) {
                        return true; // Mantener atributos sin filtro definido
                    }
                    return !filtrosDelGrupoEliminado.contains(atributo.getFiltro().getId());
                })
                .collect(Collectors.toSet());

        log.debug("Eliminados {} atributos del grupo: {}",
                atributosExistentes.size() - atributosRestantes.size(),
                getGrupoPrincipalName(grupoEliminado));

        return atributosRestantes;
    }

    /**
     * Inicializa un atributo con valores por defecto según el tipo de filtro.
     * Si el tipo es null, se considera como CADENA_TEXTO por defecto.
     */
    private void initializeAtributoDefaultValue(Atributo atributo, Filtro filtro) {
        // Si el tipo es null, usar CADENA_TEXTO como valor por defecto
        TipoFiltro tipo = filtro.getTipo() != null ? filtro.getTipo() : TipoFiltro.CADENA_TEXTO;

        switch (tipo) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case DICOTOMICO:
            case COMPUESTO:
                atributo.setDato(null); // Inicializar como null para permitir edición
                break;
            case NUMERICO:
                atributo.setDato(null); // Inicializar como null para permitir edición
                break;
        }

        log.debug("Inicializado atributo para filtro '{}' con tipo: {} (original: {})",
                filtro.getNombreFiltro(), tipo, filtro.getTipo());
    }
    
    /**
     * Crea un mapa optimizado de filtro ID -> información del grupo (nombre + orden)
     */
    private Map<UUID, GrupoInfo> createOptimizedFiltroToGrupoMap(Set<Grupo> grupos) {
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = new HashMap<>();

        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);

            if (grupo.getFiltros() != null) {
                for (GrupoFiltroRelation filtroRelation : grupo.getFiltros()) {
                    if (filtroRelation.getFiltro() != null && filtroRelation.getFiltro().getId() != null) {
                        Integer orden = filtroRelation.getOrden() != null ? filtroRelation.getOrden() : Integer.MAX_VALUE;
                        GrupoInfo grupoInfo = new GrupoInfo(grupoNombre, orden);
                        filtroToGrupoInfoMap.put(filtroRelation.getFiltro().getId(), grupoInfo);
                    }
                }
            }
        }

        log.debug("Creado mapa optimizado con {} filtros", filtroToGrupoInfoMap.size());
        return filtroToGrupoInfoMap;
    }

    /**
     * Crea un mapa de filtro ID -> nombre de grupo para búsqueda rápida (método legacy)
     */
    private Map<UUID, String> createFiltroToGrupoMap(Set<Grupo> grupos) {
        Map<UUID, String> filtroToGrupoMap = new HashMap<>();

        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);

            if (grupo.getFiltros() != null) {
                for (GrupoFiltroRelation filtroRelation : grupo.getFiltros()) {
                    if (filtroRelation.getFiltro() != null && filtroRelation.getFiltro().getId() != null) {
                        filtroToGrupoMap.put(filtroRelation.getFiltro().getId(), grupoNombre);
                    }
                }
            }
        }

        return filtroToGrupoMap;
    }
    
    /**
     * Obtiene el nombre principal de un grupo
     */
    private String getGrupoPrincipalName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                    .filter(nombre -> Boolean.TRUE.equals(nombre.getIsPrincipal()))
                    .findFirst()
                    .map(NombreGrupo::getNombre)
                    .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return "Sin nombre";
    }
    
    /**
     * Obtiene el orden de un filtro dentro de un grupo
     */
    private Integer getFiltroOrden(Grupo grupo, Filtro filtro) {
        if (grupo.getFiltros() == null || filtro == null || filtro.getId() == null) {
            return Integer.MAX_VALUE;
        }
        
        return grupo.getFiltros().stream()
                .filter(relation -> filtro.getId().equals(relation.getFiltro().getId()))
                .findFirst()
                .map(GrupoFiltroRelation::getOrden)
                .orElse(Integer.MAX_VALUE);
    }
}
