package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.NombreGrupo;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class GrupoService {

    // Las rutas deben coincidir con las definidas en el servidor
    private static final String ROUTE_SEARCH_NOMBRES = "grupo.search.nombres";
    private static final String ROUTE_GET_BY_NOMBRE_GRUPO = "grupo.get.by-nombreGrupo";
    private static final String ROUTE_GET_BY_ID = "grupo.get.by-id";
    private static final String ROUTE_SAVE = "grupo.save";

    private final ConnectionService connectionService;

    /**
     * Busca los nodos NombreGrupo cuyo campo 'nombre' coincida con la expresión regular proporcionada.
     * La expresión debe estar en formato compatible con Neo4j, por ejemplo ".*patrón.*".
     *
     * @param regex Expresión regular a utilizar en la búsqueda.
     * @return Un Flux que emite los NombreGrupo que cumplen con el criterio.
     */
    public Flux<NombreGrupo> searchNombresGrupo(String regex) {
        log.debug("Buscando NombreGrupo con regex: {}", regex);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_NOMBRES, regex, NombreGrupo.class);
    }

    /**
     * Obtiene el Grupo asociado a un NombreGrupo a partir del UUID del NombreGrupo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono que emite el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> getGrupoByNombreGrupoId(UUID nombreGrupoId) {
        log.debug("Obteniendo Grupo para NombreGrupo con id: {}", nombreGrupoId);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_NOMBRE_GRUPO, nombreGrupoId, Grupo.class);
    }

    /**
     * Obtiene un Grupo a partir de su id.
     *
     * @param id El id del Grupo.
     * @return Un Mono que emite el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> getGrupoById(String id) {
        log.debug("Obteniendo Grupo por id: {}", id);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_ID, id, Grupo.class);
    }

    /**
     * Guarda un Grupo (crear nuevo o actualizar existente).
     * Después del guardado, se ejecuta automáticamente la limpieza de NombreGrupo huérfanos en el servidor.
     *
     * @param grupo El Grupo a guardar.
     * @return Un Mono que emite el Grupo guardado.
     */
    public Mono<Grupo> saveGrupo(Grupo grupo) {
        log.debug("Guardando Grupo: {}", grupo);
        return connectionService.authenticatedRequest(ROUTE_SAVE, grupo, Grupo.class);
    }
}