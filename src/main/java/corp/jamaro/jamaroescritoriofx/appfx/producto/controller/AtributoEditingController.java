package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Atributo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.collections.FXCollections;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * Controlador especializado para la edición de atributos.
 * Maneja diálogos de edición y validación según el tipo de filtro.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AtributoEditingController {
    
    private final AlertUtil alertUtil;
    
    /**
     * Muestra un diálogo para editar un atributo existente
     */
    public void showEditAtributoDialog(Atributo atributo, Consumer<Atributo> onSuccess) {
        if (atributo == null || atributo.getFiltro() == null) {
            alertUtil.showError("El atributo o su filtro no están definidos correctamente.");
            return;
        }
        
        Dialog<Atributo> dialog = createEditDialog(atributo);
        
        Optional<Atributo> result = dialog.showAndWait();
        result.ifPresent(editedAtributo -> {
            // Copiar valores editados al atributo original
            copyAtributoValues(editedAtributo, atributo);
            onSuccess.accept(atributo);
            log.info("Atributo editado: {} = {}", 
                    atributo.getFiltro().getNombreFiltro(), 
                    getAtributoValueAsString(atributo));
        });
    }
    
    /**
     * Muestra un diálogo para crear un nuevo atributo manualmente
     */
    public void showCreateAtributoDialog(Consumer<Atributo> onSuccess) {
        // TODO: Implementar creación manual de atributos si es necesario
        // Por ahora, los atributos se crean automáticamente al agregar grupos
        alertUtil.showInfo("Creación de Atributos",
                "Los atributos se crean automáticamente al agregar grupos al producto.");
    }
    
    /**
     * Crea el diálogo de edición para un atributo
     */
    private Dialog<Atributo> createEditDialog(Atributo atributo) {
        Dialog<Atributo> dialog = new Dialog<>();
        dialog.setTitle("Editar Atributo");
        dialog.setHeaderText("Modificar valor del atributo: " + atributo.getFiltro().getNombreFiltro());
        
        // Aplicar estilos
        dialog.getDialogPane().getStylesheets().add(
                getClass().getResource("/css/styles.css").toExternalForm());
        
        // Crear contenido del diálogo
        VBox content = createDialogContent(atributo);
        dialog.getDialogPane().setContent(content);
        
        // Botones
        ButtonType saveButtonType = new ButtonType("Guardar", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);
        
        // Configurar resultado
        dialog.setResultConverter(buttonType -> {
            if (buttonType == saveButtonType) {
                return extractAtributoFromDialog(content, atributo);
            }
            return null;
        });
        
        return dialog;
    }
    
    /**
     * Crea el contenido del diálogo según el tipo de filtro
     */
    private VBox createDialogContent(Atributo atributo) {
        VBox content = new VBox(10);
        content.setPadding(new Insets(20));
        
        Filtro filtro = atributo.getFiltro();
        TipoFiltro tipo = filtro.getTipo();
        
        // Información del filtro
        Label lblFiltro = new Label("Filtro: " + filtro.getNombreFiltro());
        lblFiltro.getStyleClass().add("label-bold");
        
        Label lblTipo = new Label("Tipo: " + tipo.toString());
        lblTipo.getStyleClass().add("label-secondary");
        
        content.getChildren().addAll(lblFiltro, lblTipo, new Separator());
        
        // Control de edición según el tipo
        Control editControl = createEditControl(tipo, atributo);
        editControl.setUserData("editControl"); // Para recuperarlo después
        
        Label lblValor = new Label("Valor:");
        content.getChildren().addAll(lblValor, editControl);
        
        // Información adicional según el tipo
        if (tipo == TipoFiltro.COMPUESTO) {
            Label lblInfo = new Label("Formato: campo1|campo2|campo3 (separado por |)");
            lblInfo.getStyleClass().add("label-info");
            content.getChildren().add(lblInfo);
        } else if (tipo == TipoFiltro.DICOTOMICO) {
            Label lblInfo = new Label("Valores permitidos: Sí, No, o vacío para indeterminado");
            lblInfo.getStyleClass().add("label-info");
            content.getChildren().add(lblInfo);
        }
        
        return content;
    }
    
    /**
     * Crea el control de edición apropiado según el tipo de filtro
     */
    private Control createEditControl(TipoFiltro tipo, Atributo atributo) {
        String currentDato = atributo.getDato();
        
        switch (tipo) {
            case NUMERICO:
                Spinner<Double> spinner = new Spinner<>(-Double.MAX_VALUE, Double.MAX_VALUE, 0.0, 0.1);
                spinner.setEditable(true);
                spinner.setPrefWidth(200);
                
                if (currentDato != null && !currentDato.trim().isEmpty()) {
                    try {
                        Double currentNumeric = Double.parseDouble(currentDato.trim());
                        spinner.getValueFactory().setValue(currentNumeric);
                    } catch (NumberFormatException e) {
                        // Si no se puede parsear, usar valor por defecto
                        spinner.getValueFactory().setValue(0.0);
                    }
                }
                
                return spinner;
                
            case DICOTOMICO:
                ComboBox<String> comboBox = new ComboBox<>(
                        FXCollections.observableArrayList("", "Sí", "No"));
                comboBox.setPrefWidth(200);
                
                if (currentDato != null) {
                    comboBox.setValue(currentDato);
                }
                
                return comboBox;
                
            case COMPUESTO:
                TextArea textArea = new TextArea();
                textArea.setPrefRowCount(3);
                textArea.setPrefWidth(300);
                textArea.setWrapText(true);
                
                if (currentDato != null) {
                    textArea.setText(currentDato);
                }
                
                return textArea;
                
            case OPCION_MULTIPLE:
                // Por ahora usar TextField, después se puede mejorar con CheckBox múltiples
                TextField textFieldMultiple = new TextField();
                textFieldMultiple.setPrefWidth(300);
                textFieldMultiple.setPromptText("Separar opciones con comas");
                
                if (currentDato != null) {
                    textFieldMultiple.setText(currentDato);
                }
                
                return textFieldMultiple;
                
            default: // CADENA_TEXTO
                TextField textField = new TextField();
                textField.setPrefWidth(300);
                
                if (currentDato != null) {
                    textField.setText(currentDato);
                }
                
                return textField;
        }
    }
    
    /**
     * Extrae el atributo editado del diálogo
     */
    private Atributo extractAtributoFromDialog(VBox content, Atributo originalAtributo) {
        // Crear copia del atributo original
        Atributo editedAtributo = new Atributo();
        editedAtributo.setId(originalAtributo.getId());
        editedAtributo.setFiltro(originalAtributo.getFiltro());
        
        // Buscar el control de edición
        Control editControl = content.getChildren().stream()
                .filter(node -> "editControl".equals(node.getUserData()))
                .map(node -> (Control) node)
                .findFirst()
                .orElse(null);
        
        if (editControl == null) {
            return originalAtributo;
        }
        
        // Extraer valor según el tipo de control
        TipoFiltro tipo = originalAtributo.getFiltro().getTipo();
        extractValueFromControl(editControl, tipo, editedAtributo);
        
        return editedAtributo;
    }
    
    /**
     * Extrae el valor del control y lo asigna al atributo
     */
    private void extractValueFromControl(Control control, TipoFiltro tipo, Atributo atributo) {
        switch (tipo) {
            case NUMERICO:
                if (control instanceof Spinner) {
                    @SuppressWarnings("unchecked")
                    Spinner<Double> spinner = (Spinner<Double>) control;
                    Double value = spinner.getValue();
                    atributo.setDato(value != null ? value.toString() : null);
                }
                break;
                
            case DICOTOMICO:
                if (control instanceof ComboBox) {
                    @SuppressWarnings("unchecked")
                    ComboBox<String> comboBox = (ComboBox<String>) control;
                    String value = comboBox.getValue();
                    atributo.setDato(value != null && !value.trim().isEmpty() ? value : null);
                }
                break;
                
            case COMPUESTO:
                if (control instanceof TextArea) {
                    TextArea textArea = (TextArea) control;
                    String value = textArea.getText();
                    atributo.setDato(value != null && !value.trim().isEmpty() ? value : null);
                }
                break;
                
            default: // CADENA_TEXTO, OPCION_MULTIPLE
                if (control instanceof TextField) {
                    TextField textField = (TextField) control;
                    String value = textField.getText();
                    atributo.setDato(value != null && !value.trim().isEmpty() ? value : null);
                }
                break;
        }
    }
    
    /**
     * Copia los valores de un atributo a otro
     */
    private void copyAtributoValues(Atributo source, Atributo target) {
        target.setDato(source.getDato());
    }
    
    /**
     * Obtiene el valor del atributo como string para mostrar
     */
    private String getAtributoValueAsString(Atributo atributo) {
        String dato = atributo.getDato();
        return dato != null ? dato : "";
    }
}
