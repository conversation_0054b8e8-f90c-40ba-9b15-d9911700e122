package corp.jamaro.jamaroescritoriofx.connection.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.rsocket.RSocketRequester;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import reactor.util.retry.Retry;

import java.time.Duration;

/**
 * Configuración del cliente RSocket para el front-end.
 * Si en un futuro requieres regenerar el RSocketRequester tras login,
 * puedes modificar esta configuración para no usarlo como un único bean.
 */
@Configuration
@Slf4j
public class RSocketClientConfig {

    @Value("${rsocket.client.host}")
    private String host;

    @Value("${rsocket.client.port}")
    private Integer port;

    @Getter
    private final MimeType bearerMimeType =
            MimeTypeUtils.parseMimeType("message/x.rsocket.authentication.bearer.v0");

    @Bean
    public RSocketRequester rSocketRequester(RSocketRequester.Builder builder) {
        log.info("Configurando RSocketRequester hacia {}:{}", host, port);
        return builder
                .dataMimeType(MimeTypeUtils.APPLICATION_JSON)
                // Por el momento no enviamos setupMetadata de autenticación ya que no se tiene token aún.
                .rsocketConnector(connector -> connector.reconnect(
                        Retry.fixedDelay(3, Duration.ofSeconds(3))
                                .doBeforeRetry(retrySignal -> log.info("Intentando reconectar: intento {} de {}",
                                        retrySignal.totalRetries() + 1,
                                        retrySignal.totalRetriesInARow()))
                ))
                .tcp(host, port);
    }
}
