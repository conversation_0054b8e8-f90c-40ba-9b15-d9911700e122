package corp.jamaro.jamaroescritoriofx.connection.service;

import corp.jamaro.jamaroescritoriofx.connection.dto.LoginResponse;
import corp.jamaro.jamaroescritoriofx.connection.security.SecurityContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.rsocket.RSocketRequester;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Servicio para gestionar la conexión RSocket y las solicitudes autenticadas.
 * Ahora este servicio no se suscribe internamente a los flujos,
 * retorna el Flux y es responsabilidad del controlador o quien llame suscribirse y manejar los Disposable.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ConnectionService {

    private final RSocketRequester rSocketRequester;
    private final SecurityContext securityContext;

    // Inyectamos la metadata del cliente desde application.properties
    @Value("${client.type:desktop}")
    private String clientType;

    @Value("${client.version:0.0.1}")
    private String clientVersion;

    private static final String BEARER_MIME_TYPE = "message/x.rsocket.authentication.bearer.v0";
    private static final String ROUTE_LOGIN = "auth.login";
    private static final String ROUTE_LOGIN_BY_RFID = "auth.rfid";

    /**
     * Inicia sesión con username y password, enviando además la metadata del cliente.
     */
    public Mono<LoginResponse> login(String username, String password) {
        log.info("[ConnectionService] Iniciando sesión con username: {}", username);
        return performLogin(ROUTE_LOGIN, Map.of(
                "username", username,
                "password", password,
                "clientType", clientType,
                "clientVersion", clientVersion
        ));
    }

    /**
     * Inicia sesión con RFID, enviando además la metadata del cliente.
     */
    public Mono<LoginResponse> loginByRfid(String rfid) {
        log.info("[ConnectionService] Iniciando sesión con RFID: {}", rfid);
        return performLogin(ROUTE_LOGIN_BY_RFID, Map.of(
                "rfid", rfid,
                "clientType", clientType,
                "clientVersion", clientVersion
        ));
    }

    /**
     * Busca un usuario por RFID sin actualizar el contexto de seguridad.
     * Utiliza la misma ruta "auth.rfid" pero sin los efectos secundarios de loginByRfid.
     */
    public Mono<LoginResponse> findUserByRfid(String rfid) {
        log.info("[ConnectionService] Buscando usuario por RFID sin actualizar contexto: {}", rfid);
        return rSocketRequester
                .route(ROUTE_LOGIN_BY_RFID)
                .data(Map.of(
                        "rfid", rfid,
                        "clientType", clientType,
                        "clientVersion", clientVersion
                ))
                .retrieveMono(LoginResponse.class)
                .doOnError(error -> log.error("[ConnectionService] Error al buscar usuario por RFID '{}': {}", rfid, error.getMessage()));
    }

    /**
     * Realiza una solicitud autenticada (Mono).
     */
    public <T> Mono<T> authenticatedRequest(String route, Object data, Class<T> responseType) {
        String token = securityContext.getJwtToken();
        if (token == null || token.isBlank()) {
            log.error("[ConnectionService] Solicitud autenticada sin token. Usuario no autenticado.");
            return Mono.error(new IllegalStateException("Usuario no autenticado."));
        }

        // Evitar data = null
        if (data == null) {
            data = "";
        }

        log.info("[ConnectionService] Enviando solicitud autenticada a '{}' con token: {}", route, token);
        return rSocketRequester
                .route(route)
                .metadata("Bearer " + token, MimeTypeUtils.parseMimeType(BEARER_MIME_TYPE))
                .data(data)
                .retrieveMono(responseType)
                .doOnError(error -> log.error("[ConnectionService] Error en petición '{}' : {}", route, error.getMessage()));
    }

    /**
     * Realiza una suscripción autenticada (Flux) sin suscribirse internamente.
     * Quien llame a este metodo debe suscribirse manualmente y manejar la suscripción.
     */
    public <T> Flux<T> authenticatedSubscription(String route, Object requestData, Class<T> responseType) {
        String token = securityContext.getJwtToken();
        if (token == null || token.isBlank()) {
            log.error("[ConnectionService] Suscripción solicitada sin token. Usuario no autenticado.");
            return Flux.error(new IllegalStateException("Usuario no autenticado."));
        }

        log.info("[ConnectionService] Creando suscripción autenticada a '{}' con token: {}", route, token);

        // Evitamos pasar null a .data(...)
        if (requestData == null) {
            requestData = "";
        }

        return rSocketRequester
                .route(route)
                .metadata("Bearer " + token, MimeTypeUtils.parseMimeType(BEARER_MIME_TYPE))
                .data(requestData)
                .retrieveFlux(responseType)
                .doOnSubscribe(sub -> log.info("[ConnectionService] Suscripción iniciada en '{}'", route))
                .doOnCancel(() -> log.info("[ConnectionService] Suscripción cancelada en '{}'", route))
                .doOnError(error -> log.error("[ConnectionService] Error en suscripción '{}': {}", route, error.getMessage()));
    }

    /**
     * Cierra sesión limpiando el contexto de seguridad.
     */
    public void logout() {
        log.info("[ConnectionService] Cerrando sesión. Limpiando contexto de seguridad.");
        securityContext.clearContext();
        log.info("[ConnectionService] Sesión cerrada. Esperando nuevo login.");
    }

    /**
     * Realiza el proceso de login (tanto por username/password como por RFID).
     */
    private Mono<LoginResponse> performLogin(String route, Map<String, String> data) {
        log.debug("[ConnectionService] Realizando login en ruta '{}'", route);
        return rSocketRequester
                .route(route)
                .data(data)
                .retrieveMono(LoginResponse.class)
                .doOnNext(response -> {
                    String token = response.getToken();
                    log.info("[ConnectionService] Token recibido en login: {}", token);
                    securityContext.setJwtToken(token);
                    securityContext.setAuthenticatedUser(response.getUser());
                })
                .doOnError(error -> log.error("[ConnectionService] Error en login '{}': {}", route, error.getMessage()));
    }
}
