package corp.jamaro.jamaroescritoriofx.connection.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.PasswordField;
import javafx.scene.control.ProgressIndicator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controlador para la vista de inicio de sesión mediante RFID.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class LoginRfidController extends BaseController implements Initializable {

    private final ConnectionService connectionService;
    private final NavigationService navigationService;
    private final AlertUtil alertUtil;

    @FXML
    private PasswordField rfidField;

    // Indicador de carga definido en el FXML.
    @FXML
    private ProgressIndicator loadingIndicator;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Si se requiere lógica adicional al crear la vista, puedes ponerla aquí.
        // Dejar vacío si no hay nada que inicializar.
    }

    @FXML
    void handleCancel(ActionEvent event) {
        log.info("Limpieza del campo RFID y cancelación del inicio de sesión.");
        rfidField.clear();
    }

    @FXML
    void handleLogin(ActionEvent event) {
        String rfid = rfidField.getText();
        if (rfid == null || rfid.isBlank()) {
            alertUtil.showError("El campo RFID no puede estar vacío.");
            return;
        }

        log.info("Intentando iniciar sesión con RFID: {}", rfid);

        // Suscribimos la operación de login.
        // Registramos el Disposable para cancelarlo si la vista se cierra (onClose).
        Disposable disposable = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                connectionService.loginByRfid(rfid)
                        .switchIfEmpty(
                                // Si el servidor no encuentra el RFID, lanzamos error al suscriptor
                                reactor.core.publisher.Mono.error(
                                        new IllegalArgumentException("RFID no encontrado en el servidor."))
                        ),
                response -> {
                    log.info("Inicio de sesión exitoso. Usuario: {}", response.getUser().getUsername());
                    navigateToVentasPrincipal(response.getUser());
                },
                error -> {
                    log.error("Error al iniciar sesión: {}", error.getMessage());
                    Platform.runLater(() -> {
                        rfidField.clear();
                        alertUtil.showError("Error al iniciar sesión: " + error.getMessage());
                    });
                }
        );

        // Registramos la suscripción para que BaseController la cancele en onClose().
        registerSubscription(disposable);
    }

    /**
     * Navega a la pantalla de selección de menú pasando el User.
     */
    private void navigateToVentasPrincipal(User user) {
        navigationService.navigateTo(FXMLEnum.MENU_SELECTION, controller -> {
                    // Inyectar user en el controlador de selección de menú
                    if (controller instanceof corp.jamaro.jamaroescritoriofx.appfx.controller.MenuSelectionController menuCtrl) {
                        menuCtrl.initUser(user);
                    }
                })
                .doOnSuccess(unused -> log.info("Navegación a MenuSelection exitosa."))
                .doOnError(error -> {
                    log.error("Error al navegar a MenuSelection: {}", error.getMessage());
                    alertUtil.showError(error);
                })
                .subscribe(); // Iniciamos la suscripción
    }
}
