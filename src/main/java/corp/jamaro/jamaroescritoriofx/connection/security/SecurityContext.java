package corp.jamaro.jamaroescritoriofx.connection.security;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Contexto de seguridad que almacena el token JWT y el usuario autenticado.
 */
@Component
@Slf4j
@Getter
@Setter
public class SecurityContext {

    private String jwtToken;
    private User authenticatedUser;

    /**
     * Limpia el token y el usuario del contexto de seguridad.
     */
    public void clearContext() {
        log.info("Limpiando el token JWT y el usuario autenticado del contexto de seguridad.");
        this.jwtToken = null;
        this.authenticatedUser = null;
    }

    /**
     * Verifica si el usuario está autenticado.
     */
    public boolean isAuthenticated() {
        return jwtToken != null && !jwtToken.isBlank();
    }
}
