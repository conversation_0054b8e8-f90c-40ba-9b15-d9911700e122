<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane stylesheets="@../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.connection.controller.LoginRfidController">
    <!-- Contenedor principal -->
    <VBox alignment="CENTER" prefHeight="735.0" prefWidth="1080.0" spacing="20.0" stylesheets="@../css/styles.css">
        <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
        </padding>

        <Label text="Iniciar Sesión con RFID" />

        <PasswordField fx:id="rfidField" onAction="#handleLogin" promptText="Escanee su tarjeta RFID" />

        <Button onAction="#handleLogin" text="Iniciar Sesión" />
        <Button onAction="#handleCancel" text="Cancelar" />
    </VBox>

    <!-- Indicador de carga superpuesto. Inicialmente oculto -->
    <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
</StackPane>
