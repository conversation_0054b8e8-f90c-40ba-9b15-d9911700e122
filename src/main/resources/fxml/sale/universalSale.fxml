<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>

<StackPane fx:id="spUniversal" focusTraversable="true" prefHeight="789.0" prefWidth="1314.0" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.UnisersalSaleGuiController">
   <children>
      <AnchorPane prefHeight="984.0" prefWidth="1314.0" stylesheets="@../../css/styles.css">
         <children>
            <TabPane fx:id="tpUniversal" focusTraversable="false" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
              <tabs>
                <Tab fx:id="tabPrincipal" closable="false" text="Ventas" />
              </tabs>
            </TabPane>
            <Label fx:id="lblInfo" focusTraversable="false" text="Analizar que poner en este label y si es posible animarlo" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" />
            <MenuBar fx:id="mbUniversal" focusTraversable="false" nodeOrientation="RIGHT_TO_LEFT" AnchorPane.leftAnchor="1229.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="3.0">
              <menus>
                <Menu fx:id="mOpciones" mnemonicParsing="false" text="Opciones">
                  <items>
                        <Menu fx:id="mAuditar" mnemonicParsing="false" text="Agregar Vista" />
                        <MenuItem fx:id="miLogOut" mnemonicParsing="false" text="Cerrar Sesión" />
                  </items>
                </Menu>
              </menus>
            </MenuBar>
         </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" focusTraversable="false" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
