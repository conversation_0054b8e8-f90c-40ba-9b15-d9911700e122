<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>

<StackPane fx:id="spRootMainSale" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.MainSaleGuiController">
   <children>
      <AnchorPane fx:id="apMainSale" prefHeight="786.0" prefWidth="1182.0">
          <children>
              <TabPane fx:id="tpSales" prefHeight="480.0" prefWidth="760.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
              <Button fx:id="btnAddSale" prefWidth="99.0" styleClass="nav-button" text="Nueva Venta" AnchorPane.rightAnchor="3.0" AnchorPane.topAnchor="3.0" />
          </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
