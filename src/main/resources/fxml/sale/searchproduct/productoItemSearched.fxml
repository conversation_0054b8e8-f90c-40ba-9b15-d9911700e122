<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane fx:id="root" stylesheets="@../../../css/styles.css, @../../../css/searchProduct.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.ProductoItemSearchedController">
   <children>
      <VBox fx:id="mainVBox" spacing="3.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <padding>
            <Insets bottom="2.0" top="2.0" />
         </padding>
         <children>
            <!-- Primera fila: Código y Descripción -->
            <HBox alignment="CENTER_LEFT" spacing="5.0">
               <children>
                  <Label fx:id="lblCodProductoOld" alignment="CENTER" contentDisplay="CENTER" maxWidth="120.0" minWidth="99.0" text="12345" textAlignment="CENTER">
                     <font>
                        <Font size="15.0" />
                     </font>
                     <HBox.margin>
                        <Insets left="3.0" />
                     </HBox.margin>
                  </Label>
                  <Label fx:id="lblDescripcion" maxWidth="Infinity" text="DESCRIPCION DEL PRODUCTO" wrapText="true" HBox.hgrow="ALWAYS">
                     <padding>
                        <Insets left="3.0" right="3.0" />
                     </padding>
                     <HBox.margin>
                        <Insets right="10.0" />
                     </HBox.margin>
                  </Label>
               </children>
            </HBox>

            <!-- Segunda fila: Códigos de Fábrica y Atributos -->
            <HBox alignment="TOP_LEFT" spacing="5.0">
               <children>
                  <VBox fx:id="vbCodigosFabrica" alignment="CENTER_LEFT" maxWidth="99.0" minWidth="99.0" prefWidth="99.0" spacing="2.0">
                     <children>
                        <Label alignment="CENTER" contentDisplay="CENTER" maxWidth="1.7976931348623157E308" text="codigo fabrica 1" textAlignment="CENTER" wrapText="true" />
                     </children>
                     <HBox.margin>
                        <Insets left="3.0" />
                     </HBox.margin>
                  </VBox>
                  <HBox fx:id="hbFiltrosAtributos" alignment="CENTER_LEFT" fillHeight="false" spacing="9.0" HBox.hgrow="ALWAYS">
                     <children>
                        <HBox alignment="CENTER" spacing="3.0">
                           <children>
                              <Label text="Interior:" />
                              <Label text="30" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER" spacing="3.0">
                           <children>
                              <Label text="Exterior:" />
                              <Label text="150" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER" spacing="3.0">
                           <children>
                              <Label text="Altura:" />
                              <Label text="15" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER" spacing="3.0">
                           <children>
                              <Label text="Posicion:" />
                              <Label text="Delantero" />
                           </children>
                        </HBox>
                     </children>
                     <HBox.margin>
                        <Insets right="3.0" />
                     </HBox.margin>
                  </HBox>
               </children>
            </HBox>

            <!-- Tercera fila: Vehículos -->
            <VBox fx:id="vbVehiculos" alignment="CENTER_LEFT" spacing="2.0">
               <children>
                  <Label alignment="CENTER_LEFT" maxWidth="1.7976931348623157E308" text="asd as asfas afasd f afasd  asda dad s asfatw adfd g" wrapText="true" />
               </children>
               <padding>
                  <Insets left="105.0" right="3.0" />
               </padding>
            </VBox>

            <!-- Cuarta fila: TableView para los items (inicialmente oculto) -->
            <TableView fx:id="tvItems" styleClass="items-table-view" VBox.vgrow="NEVER">
               <VBox.margin>
                  <Insets top="3.0" />
               </VBox.margin>
               <columns>
                  <TableColumn fx:id="marcaColumn" prefWidth="90.0" resizable="false" text="Marca" />
                  <TableColumn fx:id="codCompuestoColumn" prefWidth="108.0" resizable="false" text="Cod Compuesto" />
                  <!-- Dynamic columns for Filtros will be inserted here programmatically -->
                  <TableColumn fx:id="precioVentaBaseColumn" prefWidth="54.0" resizable="false" text="P.V 1" />
                  <TableColumn fx:id="precioVentaPromocionColumn" prefWidth="54.0" resizable="false" text="P.V 2" />
                  <TableColumn fx:id="precioVentaPublicoColumn" prefWidth="54.0" resizable="false" text="P.V 3" />
                  <TableColumn fx:id="stockTotalColumn" prefWidth="54.0" resizable="false" text="Q" />
                  <TableColumn fx:id="ubicacionColumn" prefWidth="180.0" resizable="false" text="Ubicacion" />
               </columns>
            </TableView>
         </children>
      </VBox>
   </children>
</AnchorPane>
