<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.AnchorPane?>
<?import org.controlsfx.control.textfield.CustomTextField?>


<AnchorPane fx:id="anchorPaneRoot" stylesheets="@../../../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.filter.FilterStringController">
   <children>
      <CustomTextField fx:id="txtStringData" prefHeight="29.0" prefWidth="153.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
   </children>
</AnchorPane>
