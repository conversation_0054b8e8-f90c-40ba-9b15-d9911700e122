<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane fx:id="stackPaneSearchProduct" stylesheets="@../../../css/styles.css, @../../../css/searchProduct.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.SearchProductGuiController">
   <children>
      <AnchorPane fx:id="mainContainer" stylesheets="@../../../css/styles.css, @../../../css/searchProduct.css">
         <children>
            <VBox alignment="CENTER" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <!-- Contenedor para cargar la vista de filtros de forma programática -->
                  <AnchorPane fx:id="anchorFilters" />
                  <!-- ListView para mostrar productos -->
                  <ListView fx:id="productsListView" VBox.vgrow="ALWAYS" styleClass="productos-list-view" maxWidth="Infinity" prefWidth="-1" fixedCellSize="-1" />
               </children>
            </VBox>
         </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
