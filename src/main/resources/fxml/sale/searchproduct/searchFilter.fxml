<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<StackPane stylesheets="@../../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.SearchFilterController">
   <children>
      <AnchorPane prefWidth="928.0" stylesheets="@../../../css/styles.css">
         <children>
            <VBox layoutX="3.0" layoutY="3.0" spacing="3.0" AnchorPane.bottomAnchor="3.0" AnchorPane.leftAnchor="3.0" AnchorPane.rightAnchor="3.0" AnchorPane.topAnchor="3.0">
               <children>
                  <HBox>
                     <children>
                        <HBox fx:id="grupos" alignment="BOTTOM_LEFT" spacing="3.0">
                           <HBox.margin>
                              <Insets right="3.0" />
                           </HBox.margin></HBox>
                        <CustomTextField fx:id="txtSearch" promptText="Ingrese busqueda " HBox.hgrow="ALWAYS" />
                        <HBox fx:id="comodines" spacing="3.0">
                           <HBox.margin>
                              <Insets left="3.0" />
                           </HBox.margin></HBox>
                     </children>
                  </HBox>
                  <ScrollPane fx:id="scrollPaneGrupoFiltros" fitToWidth="true" vbarPolicy="NEVER">
                     <content>
                        <VBox fx:id="grupoFiltros" spacing="3.0" />
                     </content>
                     <VBox.margin>
                        <Insets />
                     </VBox.margin>
                  </ScrollPane>
                  <CustomTextField fx:id="txtVehiculo" minWidth="210.0" prefHeight="29.0" prefWidth="210.0" promptText="Vehiculo">
                     <VBox.margin>
                        <Insets left="3.0" right="3.0" />
                     </VBox.margin></CustomTextField>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
         </padding>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
