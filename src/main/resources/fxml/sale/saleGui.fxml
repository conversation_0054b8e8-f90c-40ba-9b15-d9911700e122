<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.SplitPane?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>

<StackPane fx:id="stackPaneSale" focusTraversable="true" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.SaleGuiController">
   <children>
      <AnchorPane prefHeight="930.0" prefWidth="1336.0" stylesheets="@../../css/styles.css">
         <children>
            <SplitPane fx:id="splitDetallesDividerX" dividerPositions="0.81" layoutX="128.0" layoutY="289.0" orientation="VERTICAL" prefHeight="992.0" prefWidth="1336.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <items>
                  <SplitPane fx:id="splitSaleDividerY" dividerPositions="0.22563718140929534" prefWidth="1334.0">
                     <items>
                        <AnchorPane fx:id="anchorSale" maxWidth="630.0" minHeight="0.0" minWidth="300.0" prefHeight="656.0" prefWidth="456.0" />
                        <AnchorPane>
                           <children>
                              <TabPane fx:id="tabPaneSearchProduct" prefHeight="656.0" prefWidth="702.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
                           </children>
                        </AnchorPane>
                     </items>
                  </SplitPane>
                  <AnchorPane fx:id="anchorDetalles" />
               </items>
            </SplitPane>
            <Label fx:id="lblusersConnected" text="info" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="3.0" />
            <Label fx:id="lblIniciadaPor" text="user" AnchorPane.bottomAnchor="18.0" AnchorPane.rightAnchor="3.0" />
            <Button fx:id="btnAddSearchProduct" mnemonicParsing="false" prefWidth="99.0" styleClass="nav-button" text="+ Busqueda" AnchorPane.rightAnchor="3.0" AnchorPane.topAnchor="4.5" />
         </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
