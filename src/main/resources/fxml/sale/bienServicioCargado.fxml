<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane fx:id="rootBienServicioCargado" maxHeight="72.0" minHeight="72.0" onMouseClicked="#handleClick" prefHeight="72.0" styleClass="bien-servicio-cargado" stylesheets="@../../css/bienServicioCargado.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" text="Rodaje" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets top="3.0" />
               </VBox.margin>
            </Label>
            <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefHeight="45.0" prefWidth="90.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="15.0" />
               </font>
            </Label>
            <Label fx:id="lblMarca" alignment="CENTER" maxWidth="90.0" minWidth="90.0" styleClass="marca-label" text="Koyo" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="3.0" />
               </VBox.margin>
            </Label>
         </children>
      </VBox>
      <VBox layoutX="97.0" layoutY="10.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
         <children>
      
            <!-- Panel central responsivo: Descripción -->
            <Label fx:id="descripcionDelBienServicio" maxHeight="1.7976931348623157E308" maxWidth="Infinity" prefWidth="90.0" styleClass="bien-servicio-descripcion" text="Descripcion Producto" wrapText="true" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="14.0" />
               </font>
               <padding>
                  <Insets left="3.0" right="3.0" />
               </padding>
            </Label>
            <Label fx:id="lblUbicacionPrincipal" alignment="BOTTOM_RIGHT" maxWidth="1.7976931348623157E308" text="UBICACION" wrapText="true">
               <padding>
                  <Insets left="3.0" right="3.0" />
               </padding>
            </Label>
         </children>
      </VBox>

      <AnchorPane fx:id="anchorPrecioCantidad" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <!-- Precio Acordado -->
            <Label fx:id="precioAcordado" alignment="CENTER" prefHeight="21.0" prefWidth="57.0" styleClass="bien-servicio-precio" text="3.00" AnchorPane.bottomAnchor="24.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>

            <!-- Cantidad -->
            <Label fx:id="cantidad" alignment="CENTER" prefHeight="21.0" prefWidth="32.0" styleClass="bien-servicio-cantidad" text="001" AnchorPane.bottomAnchor="24.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <font>
                  <Font size="18.0" />
               </font>
            </Label>

            <!-- Monto Acordado -->
            <Label fx:id="montoAcordado" alignment="CENTER" styleClass="bien-servicio-total" text="3.00" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
               <font>
                  <Font size="21.0" />
               </font>
            </Label>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
