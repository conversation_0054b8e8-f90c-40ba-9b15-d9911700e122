<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.SplitPane?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane fx:id="mainStackPane" prefHeight="720.0" prefWidth="1200.0" stylesheets="@../../css/caja.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaController">
   <children>
      <!-- Contenido principal -->
      <VBox spacing="0.0">
         <children>
            <!-- Menu Bar -->
            <MenuBar fx:id="menuBar" nodeOrientation="RIGHT_TO_LEFT">
               <menus>
                  <Menu text="Caja Efectivo">
                     <items>
                        <MenuItem fx:id="menuInicializarEfectivo" text="Inicializar Caja Efectivo" />
                        <MenuItem fx:id="menuCerrarEfectivo" text="Cerrar Caja Efectivo" />
                     </items>
                  </Menu>
                  <Menu text="Caja Digital">
                     <items>
                        <MenuItem fx:id="menuInicializarDigital" text="Inicializar Caja Digital" />
                        <MenuItem fx:id="menuCerrarDigital" text="Cerrar Caja Digital" />
                     </items>
                  </Menu>
               </menus>
            </MenuBar>

            <!-- Contenedor principal -->
            <VBox fx:id="contentContainer" spacing="9.0" VBox.vgrow="ALWAYS">
               <padding>
                  <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
               </padding>
               <children>
                  <!-- Información de la caja actual -->
                  <VBox spacing="3.0" styleClass="cobro-detail-container">
                     <children>
                        <Label styleClass="cobro-info-label" text="Información de Caja" />
                        <Separator />
                        <HBox alignment="CENTER_LEFT" spacing="27.0">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="3.0">
                                 <children>
                                    <Label styleClass="cobro-user-label" text="Caja:" />
                                    <Label fx:id="lblNombreCaja" styleClass="cobro-amount-label" text="No seleccionada" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER_LEFT" spacing="3.0">
                                 <children>
                                    <Label styleClass="cobro-user-label" text="Caja Efectivo:" />
                                    <Label fx:id="lblEstadoEfectivo" styleClass="cobro-date-label" text="No inicializada" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER_LEFT" spacing="3.0">
                                 <children>
                                    <Label styleClass="cobro-user-label" text="Caja Digital:" />
                                    <Label fx:id="lblEstadoDigital" styleClass="cobro-date-label" text="No inicializada" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Área de ventas por cobrar -->
                  <VBox spacing="3.0" VBox.vgrow="ALWAYS">
                     <children>
                        <Label styleClass="cobro-info-label" text="Ventas por Cobrar" />
                        <Separator />
                        <SplitPane fx:id="spSalesPorCobrar" dividerPositions="0.5" VBox.vgrow="ALWAYS">
                           <items>
                              <AnchorPane fx:id="anchorSaleDetails" prefHeight="200.0" prefWidth="200.0" />
                              <TabPane fx:id="salesTabPane" styleClass="caja-tab-pane">
                                 <tabs>
                                    <Tab closable="false" text="CONTADO">
                                       <content>
                                          <TableView fx:id="contadoTable" styleClass="caja-table-view" style="-fx-font-size: 14px;">
                                             <columns>
                                                <TableColumn fx:id="contadoFechaCol" prefWidth="120" text="Fecha" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="contadoUserCol" prefWidth="100" text="Usuario" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="contadoClienteCol" prefWidth="200" text="Cliente" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="contadoMontoCol" prefWidth="120" text="Monto Restante" style="-fx-font-size: 14px;" />
                                             </columns>
                                          </TableView>
                                       </content>
                                    </Tab>
                                    <Tab closable="false" text="CREDITO">
                                       <content>
                                          <TableView fx:id="creditoTable" styleClass="caja-table-view" style="-fx-font-size: 14px;">
                                             <columns>
                                                <TableColumn fx:id="creditoFechaCol" prefWidth="120" text="Fecha" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="creditoUserCol" prefWidth="100" text="Usuario" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="creditoClienteCol" prefWidth="200" text="Cliente" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="creditoMontoCol" prefWidth="120" text="Monto Restante" style="-fx-font-size: 14px;" />
                                             </columns>
                                          </TableView>
                                       </content>
                                    </Tab>
                                    <Tab closable="false" text="PEDIDO">
                                       <content>
                                          <TableView fx:id="pedidoTable" styleClass="caja-table-view" style="-fx-font-size: 14px;">
                                             <columns>
                                                <TableColumn fx:id="pedidoFechaCol" prefWidth="120" text="Fecha" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="pedidoUserCol" prefWidth="100" text="Usuario" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="pedidoClienteCol" prefWidth="200" text="Cliente" style="-fx-font-size: 14px;" />
                                                <TableColumn fx:id="pedidoMontoCol" prefWidth="120" text="Monto Restante" style="-fx-font-size: 14px;" />
                                             </columns>
                                          </TableView>
                                       </content>
                                    </Tab>
                                 </tabs>
                              </TabPane>
                           </items>
                        </SplitPane>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Loading indicator -->
      <ProgressIndicator fx:id="loadingIndicator" visible="false" />
   </children>
</StackPane>
