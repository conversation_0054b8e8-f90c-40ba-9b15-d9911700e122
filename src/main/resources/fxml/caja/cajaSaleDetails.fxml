<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox fx:id="rootSaleDetails" spacing="10.0" stylesheets="@../../css/caja.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaSaleDetailsController">
   <children>
      <!-- Información general del Sale -->
      <VBox spacing="5.0" styleClass="cobro-detail-container">
         <children>
            <Label styleClass="cobro-info-label" text="Detalles de Venta" />
            <Separator />
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Cliente:" />
                        <Label fx:id="lblCliente" styleClass="cobro-amount-label" text="-" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Usuario:" />
                        <Label fx:id="lblUsuario" styleClass="cobro-amount-label" text="-" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Tipo:" />
                        <Label fx:id="lblTipoVenta" styleClass="cobro-amount-label" text="-" />
                     </children>
                  </HBox>
               </children>
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Total Inicial:" />
                        <Label fx:id="lblTotalInicial" styleClass="cobro-amount-label" text="S/ 0.00" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Total Acordado:" />
                        <Label fx:id="lblTotalAcordado" styleClass="cobro-amount-label" text="S/ 0.00" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Total Restante:" />
                        <Label fx:id="lblTotalRestante" styleClass="cobro-amount-label" text="S/ 0.00" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="cobro-user-label" text="Estado:" />
                        <Label fx:id="lblEstado" styleClass="cobro-amount-label" text="-" />
                     </children>
                  </HBox>
               </children>
            </HBox>
         </children>
         <VBox.margin>
            <Insets />
         </VBox.margin>
      </VBox>

      <!-- SplitPane vertical para BienServicioCargado y BienServicioDevuelto -->
      <SplitPane fx:id="spBienesServicios" dividerPositions="0.7" orientation="VERTICAL" VBox.vgrow="ALWAYS">
         <items>
            <!-- Tabla de BienServicioCargado -->
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="cobro-user-label" text="Bienes/Servicios Cargados" />
                  <TableView fx:id="tableBienServicioCargado" styleClass="caja-table-view">
                     <columns>
                        <TableColumn fx:id="colCodCompuesto" prefWidth="120.0" text="Código" />
                        <TableColumn fx:id="colMarca" prefWidth="100.0" text="Marca" />
                        <TableColumn fx:id="colDescripcion" prefWidth="200.0" text="Descripción" />
                        <TableColumn fx:id="colPrecioAcordado" prefWidth="100.0" text="Precio Unit." />
                        <TableColumn fx:id="colCantidad" prefWidth="80.0" text="Cantidad" />
                        <TableColumn fx:id="colMontoAcordado" prefWidth="100.0" text="Monto Total" />
                     </columns>
                  </TableView>
               </children>
            </VBox>

            <!-- Tabla de BienServicioDevuelto -->
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="cobro-user-label" text="Bienes/Servicios Devueltos" />
                  <TableView fx:id="tableBienServicioDevuelto" styleClass="caja-table-view">
                     <columns>
                        <TableColumn fx:id="colDevCodCompuesto" prefWidth="120.0" text="Código" />
                        <TableColumn fx:id="colDevMarca" prefWidth="100.0" text="Marca" />
                        <TableColumn fx:id="colDevDescripcion" prefWidth="200.0" text="Descripción" />
                        <TableColumn fx:id="colDevPrecioAcordado" prefWidth="100.0" text="Precio Unit." />
                        <TableColumn fx:id="colDevCantidad" prefWidth="80.0" text="Cantidad" />
                        <TableColumn fx:id="colDevMontoDevuelto" prefWidth="100.0" text="Monto Dev." />
                        <TableColumn fx:id="colDevMotivo" prefWidth="150.0" text="Motivo" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </items>
      </SplitPane>

      <!-- Botones de acción -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="btnCobrar" styleClass="primary-button" text="Cobrar" />
            <Button fx:id="btnImprimir" styleClass="secondary-button" text="Imprimir" />
         </children>
         <VBox.margin>
            <Insets top="10.0" />
         </VBox.margin>
      </HBox>
   </children>
   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>
</VBox>
