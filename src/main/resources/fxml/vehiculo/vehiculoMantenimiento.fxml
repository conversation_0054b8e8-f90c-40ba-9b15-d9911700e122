<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.FlowPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.SearchableComboBox?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller.VehiculoMantenimientoController">

   <!-- Header Section -->
   <VBox spacing="9.0" styleClass="header-section" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <padding>
         <Insets bottom="10" left="20" right="20" top="20" />
      </padding>

      <!-- Title and Action Buttons -->
      <HBox alignment="CENTER_LEFT" spacing="20">
         <Label styleClass="page-title" text="Mantenimiento de Vehículo" />
         <Region HBox.hgrow="ALWAYS" />

         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="10">
            <Button fx:id="btnNuevo" styleClass="button, success-button" text="Nuevo">
               <graphic>
                  <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnGuardar" styleClass="button, primary-button" text="Guardar">
               <graphic>
                  <FontIcon iconLiteral="fas-save" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnCancelar" styleClass="button, secondary-button" text="Cancelar">
               <graphic>
                  <FontIcon iconLiteral="fas-times" styleClass="font-icon-light" />
               </graphic>
            </Button>
         </HBox>
      </HBox>

      <!-- Search Section -->
      <HBox alignment="CENTER_LEFT" spacing="10">
         <Label styleClass="field-label" text="Buscar Vehículo:" />
         <CustomTextField fx:id="txtBuscarVehiculo" prefWidth="450.0" promptText="Ingrese nombre del vehículo..." styleClass="search-field" HBox.hgrow="ALWAYS" />
         <ProgressIndicator fx:id="progressIndicator" maxHeight="20" maxWidth="20" visible="false" />
      </HBox>
   </VBox>

   <!-- Main Content Area -->
   <ScrollPane fitToWidth="true" prefWidth="969.0" styleClass="main-scroll-pane" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="90.0">

      <VBox spacing="3.0" styleClass="main-content">
         <padding>
            <Insets bottom="20" left="20" right="20" top="10" />
         </padding>

         <HBox alignment="CENTER_LEFT" spacing="9.0">
            <children>
               <Label styleClass="card-title" text="Información del vehículo">
                  <graphic>
                     <FontIcon iconLiteral="fas-info-circle" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <TextField fx:id="txtId" disable="true" editable="false" HBox.hgrow="ALWAYS" />
            </children>
         </HBox>

         <!-- Nombres del Vehículo Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Nombres del Vehículo">
                  <graphic>
                     <FontIcon iconLiteral="fas-tags" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Label fx:id="lblNombrePrincipal" maxWidth="1.7976931348623157E308" styleClass="field-value" text="" HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarNombre" prefWidth="135.0" styleClass="button, success-button" text="Agregar Nombre">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <TableView fx:id="tblNombres" editable="true" prefHeight="120.0">
               <columns>
                  <TableColumn fx:id="colNombreTexto" prefWidth="602.0" text="Nombre" />
                  <TableColumn fx:id="colNombrePrincipal" prefWidth="133.0" text="Principal" />
                  <TableColumn fx:id="colNombreAcciones" prefWidth="149.0" text="Acciones" />
               </columns>
            </TableView>
         </VBox>

         <!-- Basic Vehicle Information Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <Label styleClass="card-title" text="Información Básica del Vehículo">
               <graphic>
                  <FontIcon iconLiteral="fas-car" styleClass="font-icon-light" />
               </graphic>
            </Label>

            <!-- Vehicle Basic Properties -->
            <HBox spacing="15.0">
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Marca:" />
                  <HBox alignment="CENTER" spacing="5">
                     <SearchableComboBox fx:id="cmbMarca" prefWidth="200.0" HBox.hgrow="ALWAYS" />
                     <Button fx:id="btnNuevaMarca" styleClass="button, success-button">
                        <graphic>
                           <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                        </graphic>
                     </Button>
                  </HBox>
               </VBox>
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Modelo:" />
                  <HBox alignment="CENTER" spacing="5">
                     <SearchableComboBox fx:id="cmbModelo" prefWidth="200.0" HBox.hgrow="ALWAYS" />
                     <Button fx:id="btnNuevoModelo" styleClass="button, success-button">
                        <graphic>
                           <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                        </graphic>
                     </Button>
                  </HBox>
               </VBox>
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Motor:" />
                  <HBox alignment="CENTER" spacing="5">
                     <SearchableComboBox fx:id="cmbMotor" prefWidth="200.0" HBox.hgrow="ALWAYS" />
                     <Button fx:id="btnNuevoMotor" styleClass="button, success-button">
                        <graphic>
                           <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                        </graphic>
                     </Button>
                  </HBox>
               </VBox>
            </HBox>

            <!-- Additional Vehicle Properties -->
            <HBox spacing="15.0">
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Cilindrada:" />
                  <CustomTextField fx:id="txtCilindrada" promptText="ej: 1000, 1.5, 2.0" />
               </VBox>
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Versión:" />
                  <CustomTextField fx:id="txtVersion" promptText="ej: GL, XLE, Sport" />
               </VBox>
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Carrocería:" />
                  <CustomTextField fx:id="txtCarroceria" promptText="ej: sedán, SUV, hatchback" />
               </VBox>
            </HBox>

            <HBox spacing="15.0">
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Tipo Tracción:" />
                  <CustomTextField fx:id="txtTipoTraccion" promptText="ej: delantera, trasera, 4x4, AWD" />
               </VBox>
               <VBox spacing="10" HBox.hgrow="ALWAYS">
                  <Label styleClass="field-label" text="Transmisión:" />
                  <CustomTextField fx:id="txtTransmision" promptText="ej: mecánica, automática, CVT" />
               </VBox>
            </HBox>
         </VBox>

         <!-- Años del Vehículo Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Años del Vehículo">
                  <graphic>
                     <FontIcon iconLiteral="fas-calendar" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarAnio" prefWidth="135.0" styleClass="button, success-button" text="Agregar Año">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <!-- Horizontal display of years using FlowPane -->
            <ScrollPane fitToWidth="true" prefHeight="80.0" styleClass="year-scroll-pane">
               <FlowPane fx:id="flowPaneAnios" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
            </ScrollPane>
         </VBox>

         <!-- Imágenes del Vehículo Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Imágenes del Vehículo">
                  <graphic>
                     <FontIcon iconLiteral="fas-images" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarArchivo" prefWidth="135.0" styleClass="button, success-button" text="Agregar Imagen">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <!-- Image Carousel -->
            <ScrollPane fx:id="imageCarouselScrollPane" fitToHeight="true" prefHeight="200.0" styleClass="image-carousel-scroll">
               <HBox fx:id="imageCarouselContainer" alignment="CENTER_LEFT" spacing="10.0" />
            </ScrollPane>
         </VBox>

      </VBox>
   </ScrollPane>

</AnchorPane>
