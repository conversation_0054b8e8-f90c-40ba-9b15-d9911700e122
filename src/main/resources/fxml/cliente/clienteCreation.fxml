<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<StackPane fx:id="stackPaneClienteCreation" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.cliente.controller.ClienteCreationController">
   <children>
      <AnchorPane fx:id="anchorPaneMain" prefHeight="600.0" prefWidth="500.0">
         <children>
            <ScrollPane fx:id="scrollPaneForm" fitToWidth="true" AnchorPane.bottomAnchor="60.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <content>
                  <VBox fx:id="vboxForm" spacing="15.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <!-- Título -->
                        <Label fx:id="lblTitulo" alignment="CENTER" maxWidth="1.7976931348623157E308" styleClass="title-label" text="Crear Nuevo Cliente">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <!-- Información Personal -->
                        <Label styleClass="section-label" text="Información Personal">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="10.0">
                           <children>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Nombre:" />
                                    <CustomTextField fx:id="txtNombre" promptText="Nombre del cliente" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Apellido:" />
                                    <CustomTextField fx:id="txtApellido" promptText="Apellido del cliente" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Razón Social:" />
                              <CustomTextField fx:id="txtRazonSocial" promptText="Razón social (opcional)" />
                           </children>
                        </VBox>
                        
                        <!-- Documentos -->
                        <Label styleClass="section-label" text="Documentos">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="10.0">
                           <children>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="DNI:" />
                                    <CustomTextField fx:id="txtDni" promptText="Documento Nacional de Identidad" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="RUC:" />
                                    <CustomTextField fx:id="txtRuc" promptText="Registro Único de Contribuyentes" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Otro Documento:" />
                              <CustomTextField fx:id="txtOtroDocumento" promptText="Otro tipo de documento" />
                           </children>
                        </VBox>
                        
                        <!-- Información de Contacto -->
                        <Label styleClass="section-label" text="Información de Contacto">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Dirección:" />
                              <CustomTextField fx:id="txtDireccion" promptText="Dirección del cliente" />
                           </children>
                        </VBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Teléfono:" />
                                    <CustomTextField fx:id="txtTelefono" promptText="Número de teléfono" />
                                 </children>
                              </VBox>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Email:" />
                                    <CustomTextField fx:id="txtEmail" promptText="Correo electrónico" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <!-- Configuraciones -->
                        <Label styleClass="section-label" text="Configuraciones">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <CheckBox fx:id="chkTieneCredito" text="Tiene Crédito" />
                              <CheckBox fx:id="chkEsMayorista" text="Es Mayorista" />
                              <CheckBox fx:id="chkEsProveedor" text="Es Proveedor" />
                              <CheckBox fx:id="chkEstado" selected="true" text="Activo" />
                           </children>
                        </HBox>
                        
                        <!-- Metadata -->
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Notas adicionales:" />
                              <TextArea fx:id="txtMetadata" maxHeight="80.0" prefRowCount="3" promptText="Información adicional sobre el cliente" wrapText="true" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Botones de acción -->
            <HBox fx:id="hboxButtons" alignment="CENTER" spacing="15.0" AnchorPane.bottomAnchor="10.0" AnchorPane.leftAnchor="20.0" AnchorPane.rightAnchor="20.0">
               <children>
                  <Button fx:id="btnCancelar" onAction="#handleCancelar" prefWidth="100.0" styleClass="secondary-button" text="Cancelar" />
                  <Button fx:id="btnGuardar" onAction="#handleGuardar" prefWidth="100.0" styleClass="primary-button" text="Guardar" />
               </children>
            </HBox>
            
            <!-- Indicador de carga -->
            <ProgressIndicator fx:id="loadingIndicator" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
         </children>
      </AnchorPane>
   </children>
</StackPane>
