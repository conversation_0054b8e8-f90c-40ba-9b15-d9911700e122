<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane stylesheets="@../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" 
           fx:controller="corp.jamaro.jamaroescritoriofx.appfx.controller.MenuSelectionController">
   <children>
      <VBox alignment="CENTER" maxWidth="600.0" spacing="20.0">
         <children>
            <Label text="Seleccione una opción" textAlignment="CENTER">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Button fx:id="btnUniversalSale" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleUniversalSale" prefWidth="400.0" styleClass="btn-primary" text="Ventas (Universal Sale)">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnCajaGui" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleCajaGui" prefWidth="400.0" styleClass="btn-primary" text="Caja">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnMantenimiento" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleMantenimiento" prefWidth="400.0" styleClass="btn-primary" text="Mantenimiento">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </children>
</StackPane>
