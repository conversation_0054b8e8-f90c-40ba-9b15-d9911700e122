<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.TreeTableColumn?>
<?import javafx.scene.control.TreeTableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.FlowPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.producto.controller.ProductoMantenimientoController">

   <!-- Header Section -->
   <VBox spacing="9.0" styleClass="header-section" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <padding>
         <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
      </padding>

      <!-- Title and Action Buttons -->
      <HBox alignment="CENTER_LEFT" spacing="3.0">
         <Label styleClass="page-title" text="Mantenimiento de Producto" />
         <Region HBox.hgrow="ALWAYS" />

         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="10">
            <Button fx:id="btnNuevo" styleClass="button, success-button" text="Nuevo">
               <graphic>
                  <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnGuardar" styleClass="button, primary-button" text="Guardar">
               <graphic>
                  <FontIcon iconLiteral="fas-save" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnCancelar" styleClass="button, secondary-button" text="Cancelar">
               <graphic>
                  <FontIcon iconLiteral="fas-times" styleClass="font-icon-light" />
               </graphic>
            </Button>
         </HBox>
      </HBox>

      <!-- Search Section -->
      <HBox alignment="CENTER_LEFT" spacing="10">
         <Label styleClass="field-label" text="Buscar por Código:" />
         <CustomTextField fx:id="txtBuscarCodProductoOld" prefWidth="450.0" promptText="Ingrese código del producto..." styleClass="search-field" HBox.hgrow="ALWAYS" />
         <Button fx:id="btnBuscarProducto" styleClass="button, primary-button" text="">
            <graphic>
               <FontIcon iconLiteral="fas-search" styleClass="font-icon-light" />
            </graphic>
         </Button>
         <ProgressIndicator fx:id="progressIndicator" maxHeight="20" maxWidth="20" visible="false" />
      </HBox>
   </VBox>

   <!-- Main Content Area -->
   <ScrollPane fitToWidth="true" minWidth="1200.0" prefWidth="1200.0" styleClass="main-scroll-pane" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="81.0">

      <VBox spacing="3.0" styleClass="main-content">
         <padding>
            <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
         </padding>

         <HBox alignment="CENTER_LEFT" spacing="9.0">
            <children>
               <Label styleClass="card-title" text="Información Básica del Producto">
                  <graphic>
                     <FontIcon iconLiteral="fas-info-circle" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <TextField fx:id="txtCodProductoOld" promptText="Código Producto" />
               <CustomTextField fx:id="txtDescripcion" promptText="Descripción detallada del producto..." HBox.hgrow="ALWAYS" />
            </children>
         </HBox>

         <!-- Grupos Card -->
         <VBox spacing="9.0" styleClass="card">

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label styleClass="field-label" text="Buscar/Agregar Categoria:" />
               <CustomTextField fx:id="txtBuscarGrupo" prefHeight="29.0" prefWidth="630.0" promptText="Escriba para buscar una Categoria o crear uno nueva..." HBox.hgrow="ALWAYS" />
   
               <!-- Horizontal display of groups using FlowPane -->
               <ScrollPane fitToWidth="true" prefHeight="36.0" prefWidth="300.0" styleClass="year-scroll-pane" HBox.hgrow="NEVER">
                  <FlowPane fx:id="flowPaneGrupos" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
               </ScrollPane>
            </HBox>
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>
         </VBox>

         <!-- Atributos Card -->
         <VBox spacing="3.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label styleClass="card-title" text="Atributos del Producto">
                  <graphic>
                     <FontIcon iconLiteral="fas-tags" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Label styleClass="label-info" text="Los atributos se crean automáticamente al agregar grupos" />
            </HBox>

            <TreeTableView fx:id="treeAtributos" prefHeight="210.0" VBox.vgrow="SOMETIMES">
               <columns>
                  <TreeTableColumn fx:id="colAtributoNombre" minWidth="150.0" prefWidth="200.0" text="Filtro / Grupo" />
                  <TreeTableColumn fx:id="colAtributoValor" minWidth="120.0" prefWidth="180.0" text="Valor" />
                  <TreeTableColumn fx:id="colAtributoAcciones" maxWidth="200.0" minWidth="80.0" prefWidth="120.0" text="Tipo" />
               </columns>
               <columnResizePolicy>
                  <TreeTableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TreeTableView>
         </VBox>

         <!-- Vehículos Card -->
         <VBox spacing="3.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Vehículos Compatibles">
                  <graphic>
                     <FontIcon iconLiteral="fas-car" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <CustomTextField fx:id="txtBuscarVehiculo" promptText="Escriba para buscar un vehículo o crear uno nuevo..." HBox.hgrow="ALWAYS" />
            </HBox>

            <TableView fx:id="tblVehiculos" prefHeight="120.0" VBox.vgrow="SOMETIMES">
               <columns>
                  <TableColumn fx:id="colVehiculoNombre" minWidth="200.0" prefWidth="300.0" text="Nombre" />
                  <TableColumn fx:id="colVehiculoMarca" minWidth="120.0" prefWidth="150.0" text="Marca" />
                  <TableColumn fx:id="colVehiculoModelo" minWidth="120.0" prefWidth="150.0" text="Modelo" />
                  <TableColumn fx:id="colVehiculoAcciones" maxWidth="120.0" minWidth="80.0" prefWidth="100.0" text="Acciones" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
         </VBox>

         <!-- Códigos de Fábrica Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Códigos de Fábrica">
                  <graphic>
                     <FontIcon iconLiteral="fas-barcode" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <CustomTextField fx:id="txtBuscarCodigoFabrica" promptText="Escriba para buscar un código o crear uno nuevo..." HBox.hgrow="ALWAYS" />
            </HBox>

            <!-- Horizontal display of factory codes using FlowPane -->
            <ScrollPane fitToWidth="true" styleClass="year-scroll-pane">
               <FlowPane fx:id="flowPaneCodigosFabrica" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
            </ScrollPane>
         </VBox>

         <!-- Archivos/Imágenes Card -->
         <VBox spacing="3.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label styleClass="card-title" text="Archivos e Imágenes">
                  <graphic>
                     <FontIcon iconLiteral="fas-images" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarArchivo" prefWidth="135.0" styleClass="button, success-button" text="Agregar Archivo">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <!-- File Display Area -->
            <ScrollPane fitToHeight="true" prefHeight="90.0" styleClass="file-scroll-pane">
               <FlowPane fx:id="flowPaneArchivos" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
            </ScrollPane>
         </VBox>

         <!-- Items Card -->
         <VBox fx:id="vboxItems" managed="false" spacing="3.0" styleClass="card" visible="false">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Items del Producto">
                  <graphic>
                     <FontIcon iconLiteral="fas-boxes" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarItem" prefWidth="135.0" styleClass="button, success-button" text="Agregar Item">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <TableView fx:id="tblItems" prefHeight="150.0" VBox.vgrow="SOMETIMES">
               <columns>
                  <TableColumn fx:id="colItemCodCompuesto" minWidth="150.0" prefWidth="180.0" text="Código Compuesto" />
                  <TableColumn fx:id="colItemMarca" minWidth="120.0" prefWidth="150.0" text="Marca" />
                  <TableColumn fx:id="colItemDescripcion" minWidth="200.0" prefWidth="280.0" text="Descripción" />
                  <TableColumn fx:id="colItemStockTotal" maxWidth="150.0" minWidth="100.0" prefWidth="120.0" text="Stock Total" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
         </VBox>

      </VBox>
   </ScrollPane>

</AnchorPane>
