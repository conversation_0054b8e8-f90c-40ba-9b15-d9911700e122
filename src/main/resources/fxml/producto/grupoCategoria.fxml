<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.producto.controller.GrupoCategoriaController">

   <!-- Header Section -->
   <VBox spacing="10" styleClass="header-section" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <padding>
         <Insets bottom="10" left="20" right="20" top="20" />
      </padding>

      <!-- Title and Action Buttons -->
      <HBox alignment="CENTER_LEFT" spacing="20">
         <Label styleClass="page-title" text="Mantenimiento de Categoria" />
         <Region HBox.hgrow="ALWAYS" />

         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="10">
            <Button fx:id="btnNuevo" styleClass="button, success-button" text="Nuevo">
               <graphic>
                  <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnGuardar" styleClass="button, primary-button" text="Guardar">
               <graphic>
                  <FontIcon iconLiteral="fas-save" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnCancelar" styleClass="button, secondary-button" text="Cancelar">
               <graphic>
                  <FontIcon iconLiteral="fas-times" styleClass="font-icon-light" />
               </graphic>
            </Button>
         </HBox>
      </HBox>

      <!-- Search Section -->
      <HBox alignment="CENTER_LEFT" spacing="10">
         <Label styleClass="field-label" text="Buscar Categoria:" />
         <CustomTextField fx:id="txtBuscarGrupoCategoria" prefWidth="450.0" promptText="Ingrese nombre de la categoria..." styleClass="search-field" HBox.hgrow="ALWAYS" />
         <ProgressIndicator fx:id="progressIndicator" maxHeight="20" maxWidth="20" visible="false" />
      </HBox>
   </VBox>

   <!-- Main Content Area -->
   <ScrollPane fitToWidth="true" prefWidth="969.0" styleClass="main-scroll-pane" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="90.0">

      <VBox spacing="9.0" styleClass="main-content">
         <padding>
            <Insets bottom="20" left="20" right="20" top="10" />
         </padding>
         <HBox alignment="CENTER_LEFT" spacing="15.0">
            <children>

                  <Label styleClass="card-title" text="Información de la categoria">
                  <graphic>
                     <FontIcon iconLiteral="fas-info-circle" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <TextField fx:id="txtId" HBox.hgrow="ALWAYS" />
            </children>
         </HBox>

         <!-- Nombres del Grupo Card -->
         <VBox spacing="15" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Nombres de la Categoria">
                  <graphic>
                     <FontIcon iconLiteral="fas-tags" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Label fx:id="lblNombrePrincipal" maxWidth="1.7976931348623157E308" styleClass="field-value" text="" HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarNombre" prefWidth="135.0" styleClass="button, success-button" text="Agregar Nombre">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <TableView fx:id="tblNombres" editable="true" prefHeight="120.0">
               <columns>
                  <TableColumn fx:id="colNombreTexto" prefWidth="602.0" text="Nombre" />
                  <TableColumn fx:id="colNombrePrincipal" prefWidth="133.0" text="Principal" />
                  <TableColumn fx:id="colNombreAcciones" prefWidth="149.0" text="Acciones" />
               </columns>
            </TableView>
         </VBox>

         <!-- Filtros Card -->
         <VBox spacing="15" styleClass="card">
            <padding>
               <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="10">
               <Label styleClass="card-title" text="Filtros de la Categoria">
                  <graphic>
                     <FontIcon iconLiteral="fas-filter" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarFiltro" prefWidth="135.0" styleClass="button, success-button" text="Agregar Filtro">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <TableView fx:id="tblFiltros" prefHeight="300.0">
               <columns>
                  <TableColumn fx:id="colFiltroNombre" prefWidth="400.0" text="Filtro" />
                  <TableColumn fx:id="colFiltroTipo" prefWidth="150.0" text="Tipo" />
                  <TableColumn fx:id="colFiltroOrden" prefWidth="100.0" text="Orden" />
                  <TableColumn fx:id="colFiltroAcciones" prefWidth="149.0" text="Acciones" />
               </columns>
            </TableView>
         </VBox>

      </VBox>
   </ScrollPane>

</AnchorPane>
