<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane stylesheets="@../css/styles.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.controller.MantenimientoPrincipalController">
   <children>
      <VBox alignment="CENTER" maxWidth="600.0" spacing="20.0">
         <children>
            <Label text="Mantenimiento Principal" textAlignment="CENTER">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Label text="Seleccione el tipo de mantenimiento" textAlignment="CENTER">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <Button fx:id="btnGrupos" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleGrupos" prefWidth="400.0" styleClass="btn-primary" text="Categorias">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnProductos" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleProductos" prefWidth="400.0" styleClass="btn-primary" text="Productos">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnVehiculos" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleVehiculos" prefWidth="400.0" styleClass="btn-primary" text="Vehículos">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnAtributos" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleAtributos" prefWidth="400.0" styleClass="btn-primary" text="Atributos">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnFiltros" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleFiltros" prefWidth="400.0" styleClass="btn-primary" text="Filtros">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnCodigosFabrica" maxWidth="400.0" minHeight="80.0" mnemonicParsing="false" onAction="#handleCodigosFabrica" prefWidth="400.0" styleClass="btn-primary" text="Códigos de Fábrica">
               <font>
                  <Font size="18.0" />
               </font>
            </Button>
            <Button fx:id="btnVolver" maxWidth="400.0" minHeight="60.0" mnemonicParsing="false" onAction="#handleVolver" prefWidth="400.0" styleClass="btn-secondary" text="Volver al Menú Principal">
               <font>
                  <Font size="16.0" />
               </font>
            </Button>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </children>
</StackPane>
