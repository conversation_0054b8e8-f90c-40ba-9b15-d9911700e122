/*
 * JavaFX CSS - Hoja de estilos para tamaños de fuente
 * Este archivo centraliza todas las definiciones de tamaños de fuente
 * para mantener consistencia en toda la aplicación.
 */

* {
    /* Variables para tamaños de fuente */
    -fx-font-size-xs: 9px;     /* Tamaño extra pequeño para etiquetas secundarias */
    -fx-font-size-sm: 12px;     /* Tamaño pequeño para texto normal */
    -fx-font-size-md: 13px;     /* Tamaño mediano para elementos principales */
    -fx-font-size-lg: 15px;     /* Tamaño grande para encabezados de tabla */
    -fx-font-size-xl: 18px;     /* Tamaño extra grande para etiquetas destacadas */
    -fx-font-size-xxl: 21px;    /* Tamaño doble extra grande para títulos */
}
