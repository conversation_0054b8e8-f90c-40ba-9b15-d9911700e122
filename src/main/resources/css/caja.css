/* CSS específico para el módulo de Caja */
/* Hereda de styles.css y mejora los colores para las tablas de CobroDineroProgramado */

@import "styles.css";

/* Mejoras específicas para las tablas de cobros */
.caja-table-view {
    -fx-background-color: -fx-primary-color-light;
    -fx-border-color: -fx-primary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 3px;
}

/* Encabezados de columnas con mejor contraste */
.caja-table-view .column-header {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: -fx-font-size-md;
    -fx-border-color: -fx-secondary-color-dark;
    -fx-border-width: 0 1px 1px 0;
}

.caja-table-view .column-header:hover {
    -fx-background-color: -fx-secondary-color-light;
}

/* Filas de la tabla con mejor diferenciación */
.caja-table-view .table-row-cell {
    -fx-background-color: -fx-primary-color-light;
    -fx-text-fill: -fx-light-color;
    -fx-border-color: transparent;
    -fx-font-size: -fx-font-size-md;
}

.caja-table-view .table-row-cell:odd {
    -fx-background-color: derive(-fx-primary-color-light, 5%);
}

.caja-table-view .table-row-cell:selected {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
}

.caja-table-view .table-row-cell:selected:odd {
    -fx-background-color: -fx-secondary-color;
}

.caja-table-view .table-row-cell:hover {
    -fx-background-color: derive(-fx-secondary-color-light, -20%);
    -fx-text-fill: -fx-light-color;
}

.caja-table-view .table-row-cell:selected:hover {
    -fx-background-color: -fx-secondary-color-light;
}

/* Celdas de la tabla */
.caja-table-view .table-cell {
    -fx-text-fill: inherit;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 8px 12px;
    -fx-border-color: derive(-fx-primary-color, 10%);
    -fx-border-width: 0 1px 0 0;
}

/* Celdas numéricas alineadas a la derecha */
.caja-table-view .table-cell.numeric-cell {
    -fx-alignment: CENTER_RIGHT;
    -fx-font-family: 'Consolas', 'Monaco', monospace;
    -fx-font-weight: bold;
}

/* Celdas de fecha con formato especial */
.caja-table-view .table-cell.date-cell {
    -fx-alignment: CENTER;
    -fx-font-size: 11px;
}

/* Celdas de usuario con estilo distintivo */
.caja-table-view .table-cell.user-cell {
    -fx-text-fill: -fx-info-color;
    -fx-font-weight: bold;
}

/* Mejoras para los labels de información del cobro */
.cobro-info-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.cobro-amount-label {
    -fx-text-fill: -fx-success-color;
    -fx-font-weight: bold;
    -fx-font-size: 15px;
}

.cobro-remaining-label {
    -fx-text-fill: -fx-warning-color;
    -fx-font-weight: bold;
    -fx-font-size: 15px;
}

.cobro-user-label {
    -fx-text-fill: -fx-info-color;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.cobro-date-label {
    -fx-text-fill: -fx-light-grey;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

/* Mejoras para los botones específicos de caja */
.caja-button {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-padding: 8px 16px;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 3, 0, 0, 1);
}

.caja-button:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 6, 0, 0, 2);
}

.caja-button:pressed {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 1, 0, 0, 0);
}

/* Botón de actualizar con color distintivo */
.refresh-button {
    -fx-background-color: -fx-info-color;
}

.refresh-button:hover {
    -fx-background-color: derive(-fx-info-color, 20%);
}

/* Botón de cobrar con color de éxito */
.cobrar-button {
    -fx-background-color: -fx-success-color;
}

.cobrar-button:hover {
    -fx-background-color: derive(-fx-success-color, 20%);
}

/* Mejoras para el contenedor de detalles del cobro */
.cobro-detail-container {
    -fx-background-color: derive(-fx-primary-color-light, -5%);
    -fx-border-color: -fx-primary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-padding: 10px;
}

/* Mejoras para las pestañas de tipos de cobro */
.caja-tab-pane .tab {
    -fx-background-color: -fx-primary-color;
    -fx-text-fill: -fx-light-grey;
}

.caja-tab-pane .tab:selected {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
}

.caja-tab-pane .tab:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-text-fill: -fx-light-color;
}

/* ===== Estilos específicos para CajaSaleDetails ===== */

/* Contenedor principal del componente */
#rootSaleDetails {
    -fx-background-color: -fx-primary-color-light;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

/* Estilos para las etiquetas de información */
#rootSaleDetails .cobro-info-label {
    -fx-font-size: -fx-font-size-lg;
    -fx-font-weight: bold;
    -fx-text-fill: -fx-secondary-color-light;
}

#rootSaleDetails .cobro-user-label {
    -fx-font-size: -fx-font-size-md;
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-color;
}

#rootSaleDetails .cobro-amount-label {
    -fx-font-size: -fx-font-size-md;
    -fx-font-weight: normal;
    -fx-text-fill: white;
}

/* Estilos para las tablas dentro del componente */
#rootSaleDetails .caja-table-view {
    -fx-font-size: -fx-font-size-md;
}

#rootSaleDetails .caja-table-view .column-header {
    -fx-font-size: -fx-font-size-md;
    -fx-font-weight: bold;
}

#rootSaleDetails .caja-table-view .table-cell {
    -fx-font-size: -fx-font-size-md;
    -fx-text-fill: white;
}

/* Estilos para los botones */
#rootSaleDetails .primary-button {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: -fx-font-size-md;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

#rootSaleDetails .primary-button:hover {
    -fx-background-color: -fx-secondary-color-light;
}

#rootSaleDetails .secondary-button {
    -fx-background-color: -fx-primary-color;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: -fx-font-size-md;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

#rootSaleDetails .secondary-button:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-border-color: -fx-secondary-color-light;
}