package corp.jamaro.jamaroescritoriofx.appfx.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for EncryptionUtil
 */
public class EncryptionUtilTest {

    @Test
    public void testEncryptDouble() {
        // Test the example from the requirements: 12.3 should become "EU.C"
        Double testValue = 12.3;
        String encrypted = EncryptionUtil.encrypt(testValue);
        assertEquals("EU.CO", encrypted); // 12.30 -> EU.CO (with 2 decimal places)
        
        // Test another value: 45.67 should become "AL.IP"
        Double testValue2 = 45.67;
        String encrypted2 = EncryptionUtil.encrypt(testValue2);
        assertEquals("AL.IP", encrypted2);
        
        // Test with null
        String encryptedNull = EncryptionUtil.encrypt((Double) null);
        assertEquals("", encryptedNull);
        
        // Test with zero
        Double zero = 0.0;
        String encryptedZero = EncryptionUtil.encrypt(zero);
        assertEquals("O.OO", encryptedZero);
    }

    @Test
    public void testEncryptString() {
        // Test string encryption
        String encrypted = EncryptionUtil.encrypt("12.3");
        assertEquals("EU.C", encrypted);
        
        String encrypted2 = EncryptionUtil.encrypt("45.67");
        assertEquals("AL.IP", encrypted2);
        
        // Test with null and empty
        assertEquals("", EncryptionUtil.encrypt((String) null));
        assertEquals("", EncryptionUtil.encrypt(""));
    }

    @Test
    public void testDecrypt() {
        // Test decryption
        String decrypted = EncryptionUtil.decrypt("EU.C");
        assertEquals("12.3", decrypted);
        
        String decrypted2 = EncryptionUtil.decrypt("AL.IP");
        assertEquals("45.67", decrypted2);
        
        // Test with null and empty
        assertEquals("", EncryptionUtil.decrypt(null));
        assertEquals("", EncryptionUtil.decrypt(""));
    }

    @Test
    public void testDecryptToDouble() {
        // Test decryption to Double
        Double decrypted = EncryptionUtil.decryptToDouble("EU.C");
        assertEquals(12.3, decrypted, 0.001);
        
        Double decrypted2 = EncryptionUtil.decryptToDouble("AL.IP");
        assertEquals(45.67, decrypted2, 0.001);
        
        // Test with null and empty
        assertNull(EncryptionUtil.decryptToDouble(null));
        assertNull(EncryptionUtil.decryptToDouble(""));
    }

    @Test
    public void testAllDigitMapping() {
        // Test all digit mappings
        assertEquals("E", EncryptionUtil.encrypt("1"));
        assertEquals("U", EncryptionUtil.encrypt("2"));
        assertEquals("C", EncryptionUtil.encrypt("3"));
        assertEquals("A", EncryptionUtil.encrypt("4"));
        assertEquals("L", EncryptionUtil.encrypt("5"));
        assertEquals("I", EncryptionUtil.encrypt("6"));
        assertEquals("P", EncryptionUtil.encrypt("7"));
        assertEquals("T", EncryptionUtil.encrypt("8"));
        assertEquals("H", EncryptionUtil.encrypt("9"));
        assertEquals("O", EncryptionUtil.encrypt("0"));
    }

    @Test
    public void testRoundTripEncryptionDecryption() {
        // Test that encryption and decryption are reversible
        Double originalValue = 123.45;
        String encrypted = EncryptionUtil.encrypt(originalValue);
        Double decrypted = EncryptionUtil.decryptToDouble(encrypted);
        assertEquals(originalValue, decrypted, 0.001);
    }
}