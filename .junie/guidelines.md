# Project Guidelines

# Architecture and Structure
- The application follows a server-as-source-of-truth architecture where controllers subscribe to updates after sending changes.
- Each service has its own responsibility to interact with the server through its specific service.
- The server implements a RSocket communication pattern where clients send requests and receive updates through subscriptions.
